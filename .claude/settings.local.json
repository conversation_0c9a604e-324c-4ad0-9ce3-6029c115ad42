{"permissions": {"allow": ["Bash(git reset:*)", "Bash(npm run typecheck:*)", "Bash(npx tsc:*)", "Bash(find:*)", "Bash(npx prisma generate:*)", "Bash(npx prisma migrate dev:*)", "Bash(npx prisma db push:*)", "mcp__sequential-thinking__sequentialthinking", "Bash(grep:*)", "Bash(grep -n \"export.*DataRequestStatus\" /Users/<USER>/Luminar/apps/command-center/src/modules/gdpr/services/data-portability.service.ts)", "Bash(grep -n -A 5 \"enum DataCategory\\|enum RetentionAction\" /Users/<USER>/Luminar/apps/command-center/prisma/schema.prisma)", "Bash(grep -n \"model User\" /Users/<USER>/Luminar/apps/command-center/prisma/schema.prisma)", "Bash(grep -r \"DataCategory\\|RetentionAction\" /Users/<USER>/Luminar/apps/command-center/prisma/schema.prisma)", "Bash(grep -n -A 15 \"model DataRetentionPolicy\" /Users/<USER>/Luminar/apps/command-center/prisma/schema.prisma)", "Bash(npm run type-check:backend:*)", "Bash(npm run type-check:*)", "Bash(rg:*)", "Bash(# Get all remaining files that need fixing\nfind . -name \"\"*.tsx\"\" -exec awk ''/import type.*{/{flag=1} flag && /defaultComponentProps/{print FILENAME; flag=0; nextfile} /}.*from.*component-props/{flag=0}'' {} \\;)", "Bash(# Count how many files are left\nfind . -name \"\"*.tsx\"\" -exec awk ''/import type.*{/{flag=1} flag && /defaultComponentProps/{print FILENAME; flag=0; nextfile} /}.*from.*component-props/{flag=0}'' {} \\; | wc -l)", "Bash(#!/bin/bash\n# Create a script to fix all remaining files\nfiles_to_fix=(\n  \"\"./components/layouts/application/ecommerce-layout.tsx\"\"\n  \"\"./components/layouts/specialized/landing-page.tsx\"\"\n  \"\"./lib/component-factory.ts\"\"\n)\n\nfor file in \"\"${files_to_fix[@]}\"\"; do\n  if [[ -f \"\"$file\"\" ]]; then\n    echo \"\"Processing: $file\"\"\n    # Create a temporary file with the fix\n    awk ''\n      /import type.*\\{/ { \n        in_import=1; \n        import_block=\"\"\"\"\n      }\n      in_import {\n        import_block = import_block $0 \"\"\\n\"\"\n        if (/\\}.*from.*component-props/) {\n          # Check if defaultComponentProps is in the import block\n          if (import_block ~ /defaultComponentProps/) {\n            # Split the import\n            gsub(/,\\s*defaultComponentProps/, \"\"\"\", import_block)\n            gsub(/defaultComponentProps,\\s*/, \"\"\"\", import_block)\n            gsub(/defaultComponentProps/, \"\"\"\", import_block)\n            printf \"\"%s\"\", import_block\n            print \"\"import { defaultComponentProps } from \"\" substr($0, index($0, \"\"from\"\"))\n          } else {\n            printf \"\"%s\"\", import_block\n          }\n          in_import=0\n          import_block=\"\"\"\"\n        }\n        next\n      }\n      !in_import { print }\n    '' \"\"$file\"\" > \"\"${file}.tmp\"\" && mv \"\"${file}.tmp\"\" \"\"$file\"\"\n  fi\ndone)", "<PERSON><PERSON>(python3:*)", "Bash(# Final verification\nfind . -name \"\"*.ts\"\" -o -name \"\"*.tsx\"\" | xargs grep -l \"\"defaultComponentProps\"\" | xargs grep -l \"\"import type.*{.*defaultComponentProps\"\" | wc -l)", "Bash(rm:*)", "Bash(npm install:*)", "Bash(pnpm add:*)", "Bash(npx biome:*)", "Bash(npx ultracite:*)", "Bash(npm run:*)", "<PERSON><PERSON>(curl:*)", "Bash(pg_isready:*)", "Bash(kill:*)", "Bash(lsof:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(docker:*)", "<PERSON><PERSON>(docker compose:*)", "Bash(ls:*)", "Bash(npx prisma migrate:*)", "Bash(pnpm run dev:*)", "Bash(pnpm -w run dev:optimized)", "Bash(pnpm -w run dev:all)", "Bash(pnpm -w run dev)", "Bash(pnpm run:*)", "Bash(pnpm -w run build:packages)", "Bash(pnpm --filter command-center run dev)", "<PERSON><PERSON>(chmod:*)", "Bash(./start-luminar.sh:*)", "Bash(./quick-test.sh:*)", "Bash(./test-help.sh:*)", "Bash(./test-services.sh:*)", "Bash(./test-individual-services.sh:*)", "Bash(./verify-apps.sh:*)", "Bash(/Users/<USER>/Luminar/verify-apps.sh:*)", "Bash(./start-fresh.sh:*)", "Bash(PORT=5003 pnpm run dev)", "Bash(bash:*)", "<PERSON><PERSON>(timeout 30s pnpm run start:dev)", "Bash(ps:*)", "Bash(PORT=3001 pnpm run start:dev)", "Bash(pnpm --filter amna run dev)", "Bash(pnpm --filter lighthouse run dev)", "<PERSON><PERSON>(python:*)", "Bash(for port in 3001 5000 5001 5002 5003 5005 5006 5007)", "Bash(do echo -n \"Port $port: \")", "Bash(done)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(cat:*)", "Bash(open http://localhost:4000)", "<PERSON><PERSON>(touch:*)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "mcp__filesystem__list_directory", "mcp__filesystem__read_multiple_files", "mcp__filesystem__read_file", "Bash(node:*)", "mcp__filesystem__search_files", "mcp__filesystem__directory_tree", "mcp__filesystem__edit_file", "mcp__filesystem__write_file", "<PERSON><PERSON>(sed:*)", "mcp__filesystem__list_allowed_directories", "<PERSON><PERSON>(realpath:*)", "Bash(npx tsx:*)", "Bash(npx ts-node:*)", "Bash(PORT=3001 node dist/src/main.js)", "<PERSON><PERSON>(true)", "<PERSON><PERSON>(source .env)", "Bash(npx prisma db seed:*)", "<PERSON><PERSON>(mv:*)", "Bash(pnpm install:*)", "Bash(pnpm type-check:*)", "Bash(pnpm typecheck:*)", "Bash(pnpm dev:*)", "Bash(pnpm build:packages:*)", "Bash(pnpm --filter command-center dev)", "Bash(pnpm start:dev:*)", "Bash(pnpm --filter @luminar/amna dev)"], "deny": []}}