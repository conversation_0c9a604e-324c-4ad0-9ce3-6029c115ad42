import type { CorsOptions } from '@nestjs/common/interfaces/external/cors-options.interface'
import { ConfigService } from '@nestjs/config'

export const createCorsConfig = (configService: ConfigService): CorsOptions => {
  const environment = configService.get<string>('NODE_ENV', 'development')

  // Default L&D application origins
  const defaultOrigins = [
    'http://localhost:3000', // Command Center
    'http://localhost:3001', // Learning Dashboard
    'http://localhost:3002', // Skills Assessment
    'http://localhost:3003', // Talent Analytics
    'http://localhost:3004', // Admin Portal
    'https://command-center.luminar.app',
    'https://learn.luminar.app',
    'https://assess.luminar.app',
    'https://analytics.luminar.app',
    'https://admin.luminar.app',
  ]

  const customOrigins = configService.get<string>('CORS_ORIGINS', '').split(',').filter(Boolean)

  const allowedOrigins = [...defaultOrigins, ...customOrigins]

  const corsConfig: CorsOptions = {
    // Origin configuration
    origin: (origin, callback) => {
      // Allow requests with no origin (mobile apps, Postman, etc.)
      if (!origin) {
        return callback(null, true)
      }

      // In development, allow localhost with any port
      if (environment === 'development') {
        const localhostPattern = /^https?:\/\/localhost(:\d+)?$/
        const localhostIpPattern = /^https?:\/\/127\.0\.0\.1(:\d+)?$/

        if (localhostPattern.test(origin) || localhostIpPattern.test(origin)) {
          return callback(null, true)
        }
      }

      // Check against allowed origins
      if (allowedOrigins.includes(origin) || allowedOrigins.includes('*')) {
        return callback(null, true)
      }

      // Reject the request
      return callback(new Error(`Origin ${origin} not allowed by CORS policy`), false)
    },

    // Methods allowed
    methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],

    // Headers allowed for L&D applications
    allowedHeaders: [
      'Origin',
      'X-Requested-With',
      'Content-Type',
      'Accept',
      'Authorization',
      'X-API-Key',
      'X-Device-ID',
      'X-Session-ID',
      'X-Request-ID',
      'X-CSRF-Token',
      'X-App-ID',
      'X-Cross-App-Token',
      'X-User-Context',
      'X-Learning-Context',
      'X-Assessment-Context',
      'X-Analytics-Context',
      'X-Admin-Context',
      'X-Tenant-ID',
      'X-Organization-ID',
      'X-Client-Version',
    ],

    // Headers exposed to the client
    exposedHeaders: [
      'X-Total-Count',
      'X-Page-Count',
      'X-Per-Page',
      'X-Request-ID',
      'X-Rate-Limit-Limit',
      'X-Rate-Limit-Remaining',
      'X-Rate-Limit-Reset',
    ],

    // Allow cookies to be sent
    credentials: true,

    // Preflight cache duration (in seconds)
    maxAge: 86400, // 24 hours

    // Handle preflight requests
    preflightContinue: false,
    optionsSuccessStatus: 204,
  }

  // Additional security for production
  if (environment === 'production') {
    // More restrictive in production
    corsConfig.origin = (origin, callback) => {
      if (!origin) {
        // In production, be more strict about requests with no origin
        return callback(new Error('Origin header required'), false)
      }

      if (allowedOrigins.includes(origin)) {
        return callback(null, true)
      }

      return callback(new Error(`Origin ${origin} not allowed by CORS policy`), false)
    }
  }

  return corsConfig
}

// CORS configuration for specific routes
export const createApiCorsConfig = (configService: ConfigService): CorsOptions => {
  const baseCors = createCorsConfig(configService)

  return {
    ...baseCors,
    // API-specific CORS settings
    allowedHeaders: [...baseCors.allowedHeaders, 'X-API-Version', 'X-Client-Version'],
    exposedHeaders: [...baseCors.exposedHeaders, 'X-API-Version', 'X-Deprecation-Warning'],
  }
}

export const createWebSocketCorsConfig = (configService: ConfigService): CorsOptions => {
  const baseCors = createCorsConfig(configService)

  return {
    ...baseCors,
    // WebSocket-specific CORS settings
    methods: ['GET', 'POST'],
    allowedHeaders: ['Origin', 'Authorization', 'X-Device-ID', 'X-Session-ID'],
  }
}

// Utility function to validate origin
export const isOriginAllowed = (origin: string, configService: ConfigService): boolean => {
  const allowedOrigins = configService
    .get<string>('CORS_ORIGINS', 'http://localhost:3000')
    .split(',')
  const environment = configService.get<string>('NODE_ENV', 'development')

  if (allowedOrigins.includes('*') || allowedOrigins.includes(origin)) {
    return true
  }

  if (environment === 'development') {
    const localhostPattern = /^https?:\/\/(localhost|127\.0\.0\.1)(:\d+)?$/
    return localhostPattern.test(origin)
  }

  return false
}
