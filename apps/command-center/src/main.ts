import { ValidationPipe, VersioningType } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { NestFactory } from '@nestjs/core'
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger'
import { rateLimit } from 'express-rate-limit'
import session from 'express-session'
import helmet from 'helmet'
import { createClient } from 'redis'
import * as Sentry from '@sentry/node'
import { ProfilingIntegration } from '@sentry/profiling-node'
import { AppModule } from './app.module'
import { AllExceptionsFilter } from './common/filters/http-exception.filter'
import { ResponseInterceptor } from './common/interceptors/response.interceptor'
import { SecurityHeadersMiddleware } from './common/middleware/security-headers.middleware'
import { SanitizationPipe } from './common/validators/sanitization.pipe'
import {
  VersioningMiddleware,
  VersionValidationMiddleware,
} from './common/versioning/versioning.middleware'
import { createCorsConfig } from './config/cors.config'
import { createSecurityConfig } from './config/security.config'

// Initialize Sentry
Sentry.init({
  dsn: process.env.SENTRY_DSN,
  environment: process.env.SENTRY_ENVIRONMENT || process.env.NODE_ENV || 'development',
  release: process.env.SENTRY_RELEASE || '1.0.0',
  integrations: [
    new ProfilingIntegration(),
  ],
  tracesSampleRate: parseFloat(process.env.SENTRY_TRACES_SAMPLE_RATE || '0.1'),
  profilesSampleRate: parseFloat(process.env.SENTRY_PROFILES_SAMPLE_RATE || '0.1'),
  beforeSend(event, hint) {
    // Filter out health check errors
    if (event.request?.url?.includes('/health')) {
      return null
    }

    // Filter out expected errors
    const error = hint.originalException
    if (error && error instanceof Error) {
      // Don't send validation errors to Sentry
      if (error.message?.includes('Validation failed')) {
        return null
      }
      // Don't send authentication errors
      if (error.message?.includes('Unauthorized') || error.message?.includes('Forbidden')) {
        return null
      }
    }

    return event
  },
  beforeSendTransaction(transaction) {
    // Filter out health check transactions
    if (transaction.transaction?.includes('/health')) {
      return null
    }
    return transaction
  },
})

async function bootstrap() {
  const app = await NestFactory.create(AppModule, {
    logger: ['error', 'warn', 'log', 'debug', 'verbose'],
  })
  const configService = app.get(ConfigService)
  const securityConfig = createSecurityConfig(configService)

  // Advanced Security Headers
  app.use(
    new SecurityHeadersMiddleware(configService).use.bind(
      new SecurityHeadersMiddleware(configService)
    )
  )

  // Helmet security middleware with comprehensive configuration
  app.use(securityConfig.helmet)

  // Compression
  const compression = (await import('compression')).default
  app.use(compression())

  // Enhanced Global rate limiting
  app.use(
    rateLimit({
      windowMs: securityConfig.rateLimit.windowMs,
      max: securityConfig.rateLimit.maxRequests,
      message: securityConfig.rateLimit.message,
      standardHeaders: true,
      legacyHeaders: false,
      keyGenerator: (req) => {
        // Use IP + User ID for authenticated users
        const ip = req.ip || req.connection.remoteAddress || 'unknown'
        const userId = (req as any).user?.id || 'anonymous'
        return `${ip}:${userId}`
      },
      skip: (req) => {
        // Skip rate limiting for health checks and static assets
        return req.path === '/health' || req.path.startsWith('/static')
      },
    })
  )

  // Redis client for sessions
  const redisClient = createClient({
    socket: {
      host: configService.get('redis.host'),
      port: configService.get('redis.port'),
    },
  })

  await redisClient.connect()

  // Enhanced Session configuration with security
  const { RedisStore } = await import('connect-redis')
  app.use(
    session({
      store: new RedisStore({
        client: redisClient,
        prefix: 'sess:',
        ttl: 86400, // 24 hours
      }),
      secret: securityConfig.session.secret,
      resave: false,
      saveUninitialized: false,
      rolling: true, // Reset expiration on activity
      cookie: securityConfig.session.cookie,
      name: 'sessionId', // Don't use default session name
    })
  )

  // API Versioning
  app.enableVersioning({
    type: VersioningType.URI,
    defaultVersion: '1',
    prefix: 'v',
  })

  // Global pipes
  app.useGlobalPipes(
    new SanitizationPipe({
      trim: true,
      stripHtml: true,
      escape: true,
    }),
    new ValidationPipe({
      whitelist: true,
      transform: true,
      forbidNonWhitelisted: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
      stopAtFirstError: false,
      exceptionFactory: (errors) => {
        const messages = errors.reduce((acc, error) => {
          const property = error.property
          const constraints = error.constraints || {}
          acc[property] = Object.values(constraints)
          return acc
        }, {})
        return new Error(JSON.stringify(messages))
      },
    })
  )

  // Global filters
  app.useGlobalFilters(new AllExceptionsFilter())

  // Global interceptors
  app.useGlobalInterceptors(new ResponseInterceptor())

  // Request ID middleware
  app.use((req, res, next) => {
    req.id = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    res.setHeader('X-Request-ID', req.id)
    next()
  })

  // Set global API prefix (exclude health check)
  app.setGlobalPrefix('api', {
    exclude: ['/health']
  })

  // Enhanced CORS configuration
  app.enableCors(createCorsConfig(configService))

  // Swagger documentation
  const config = new DocumentBuilder()
    .setTitle('Command Center API')
    .setDescription('Comprehensive API for Command Center application')
    .setVersion('3.0')
    .addBearerAuth()
    .addApiKey({ type: 'apiKey', name: 'X-API-Key', in: 'header' })
    .addServer('http://localhost:3000', 'Development server')
    .addServer('https://api.commandcenter.com', 'Production server')
    .addTag('auth', 'Authentication endpoints')
    .addTag('users', 'User management')
    .addTag('files', 'File operations')
    .addTag('ai', 'AI and ML features')
    .addTag('vector', 'Vector operations')
    .addTag('websocket', 'WebSocket events')
    .setContact('API Support', 'https://commandcenter.com/support', '<EMAIL>')
    .setLicense('MIT', 'https://opensource.org/licenses/MIT')
    .build()

  const document = SwaggerModule.createDocument(app, config, {
    operationIdFactory: (controllerKey: string, methodKey: string) => methodKey,
    deepScanRoutes: true,
  })

  SwaggerModule.setup('api', app, document, {
    swaggerOptions: {
      persistAuthorization: true,
      docExpansion: 'none',
      filter: true,
      showRequestDuration: true,
      syntaxHighlight: {
        activate: true,
        theme: 'monokai',
      },
      tryItOutEnabled: true,
      requestInterceptor: (req) => {
        req.headers['X-Request-ID'] = `swagger_${Date.now()}`
        return req
      },
    },
    customCssUrl: 'https://cdnjs.cloudflare.com/ajax/libs/swagger-ui/4.15.5/swagger-ui.min.css',
    customJs: [
      'https://cdnjs.cloudflare.com/ajax/libs/swagger-ui/4.15.5/swagger-ui-bundle.js',
      'https://cdnjs.cloudflare.com/ajax/libs/swagger-ui/4.15.5/swagger-ui-standalone-preset.js',
    ],
  })

  // Health check endpoint
  app.getHttpAdapter().get('/health', (req, res) => {
    res.json({
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: configService.get('NODE_ENV'),
      version: '3.0.0',
    })
  })

  // Graceful shutdown
  const shutdown = async () => {
    console.log('Shutting down gracefully...')
    await app.close()
    await redisClient.quit()
    process.exit(0)
  }

  process.on('SIGTERM', shutdown)
  process.on('SIGINT', shutdown)

  const port = configService.get('PORT') || 3000
  await app.listen(port)

  console.log(`🚀 Application is running on: http://localhost:${port}`)
  console.log(`📚 API documentation available at: http://localhost:${port}/api`)
  console.log(`🏥 Health check available at: http://localhost:${port}/health`)
  console.log(`📊 Sentry monitoring enabled for environment: ${process.env.NODE_ENV}`)
}

bootstrap().catch((err) => {
  console.error('Failed to start application:', err)
  Sentry.captureException(err)
  process.exit(1)
})
