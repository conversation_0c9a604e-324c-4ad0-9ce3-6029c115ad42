import { Module } from '@nestjs/common'
import { ConfigModule } from '@nestjs/config'
import { CodeExecutorService } from './code-executor.service'
import { ExecutionMonitoringService } from './execution-monitoring.service'
import { ExecutionSecurityService } from './execution-security.service'

@Module({
  imports: [ConfigModule],
  providers: [CodeExecutorService, ExecutionSecurityService], // ExecutionMonitoringService temporarily disabled
  exports: [CodeExecutorService, ExecutionSecurityService], // ExecutionMonitoringService temporarily disabled
})
export class ExecutionModule {}
