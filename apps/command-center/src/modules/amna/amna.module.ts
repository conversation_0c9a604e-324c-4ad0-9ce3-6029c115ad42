import { HttpModule } from '@nestjs/axios'
import { BullModule } from '@nestjs/bull'
import { Global, Module } from '@nestjs/common'
import { ConfigModule, ConfigService } from '@nestjs/config'
import { EventEmitterModule } from '@nestjs/event-emitter'
// Agent imports
import { AgentService } from './agents/agent.service'
import { AgentOrchestratorService } from './agents/agent-orchestrator.service'
import { AmnaController } from './amna.controller'
import { AmnaGateway } from './amna.gateway'
import { AmnaService } from './amna.service'
// Configuration
import { amnaConfig } from './config/amna.config'
import { CodeExecutorService } from './execution/code-executor.service'
// Execution imports
import { ExecutionModule } from './execution/execution.module'
import { ExecutionMonitoringService } from './execution/execution-monitoring.service'
import { ExecutionSecurityService } from './execution/execution-security.service'
// LLM imports
import { LlmService } from './llm/llm.service'
import { OllamaService } from './llm/ollama.service'
import { OpenAiService } from './llm/openai.service'
import { StreamingHandlerService } from './llm/streaming-handler.service'
import { ContextManagerService } from './memory/context-manager.service'
import { EmbeddingPipelineService } from './memory/embedding-pipeline.service'
import { MemoryClusteringService } from './memory/memory-clustering.service'
// Memory imports
import { MemoryStoreService } from './memory/memory-store.service'
import { SemanticMemoryService } from './memory/semantic-memory.service'
import { VectorMemoryService } from './memory/vector-memory.service'
// Task imports
import { TaskService } from './tasks/task.service'
import { TaskExecutorService } from './tasks/task-executor.service'
import { TaskSchedulerService } from './tasks/task-scheduler.service'
import { DynamicSchemaService } from './tools/dynamic-schema.service'
import { FunctionToolService } from './tools/function-tool.service'
import { McpContext7Service } from './tools/mcp-context7.service'
import { McpExecutorService } from './tools/mcp-executor.service'
import { McpGoFastService } from './tools/mcp-gofast.service'
import { McpHealthService } from './tools/mcp-health.service'
import { McpMagicService } from './tools/mcp-magic.service'
import { McpMultifetchService } from './tools/mcp-multifetch.service'
import { McpPlaywrightService } from './tools/mcp-playwright.service'
import { McpSequentialService } from './tools/mcp-sequential.service'
import { McpSerenaService } from './tools/mcp-serena.service'
import { McpToolService } from './tools/mcp-tool.service'
import { ToolConverterService } from './tools/tool-converter.service'
import { ToolPerformanceService } from './tools/tool-performance.service'
// Tool imports
import { ToolRegistryService } from './tools/tool-registry.service'
import { ConditionalWorkflowEngine } from './workflows/conditional-workflow.engine'
import { StatefulWorkflowEngine } from './workflows/stateful-workflow.engine'
// Workflow imports
import { WorkflowService } from './workflows/workflow.service'
import { WorkflowPatternsService } from './workflows/workflow-patterns.service'
import { WorkflowRealtimeGateway } from './workflows/workflow-realtime.gateway'
import { WorkflowStateService } from './workflows/workflow-state.service'

@Global()
@Module({
  imports: [
    ConfigModule.forFeature(amnaConfig),
    EventEmitterModule.forRoot(),
    HttpModule,
    BullModule.registerQueueAsync(
      ...[
        'amna-agents',
        'amna-tasks',
        'amna-workflows',
        'amna-embeddings',
        'amna-clustering',
        'amna-performance',
      ].map((name) => ({
        name,
        imports: [ConfigModule],
        useFactory: (configService: ConfigService) => ({
          redis: {
            host: configService.get('REDIS_HOST', 'localhost'),
            port: configService.get('REDIS_PORT', 6379),
            password: configService.get('REDIS_PASSWORD') || undefined,
          },
        }),
        inject: [ConfigService],
      }))
    ),
    ExecutionModule,
  ],
  controllers: [AmnaController],
  providers: [
    AmnaService,
    AmnaGateway,
    // Agent services
    AgentService,
    AgentOrchestratorService,
    // Task services
    TaskService,
    TaskExecutorService,
    TaskSchedulerService,
    // Tool services
    ToolRegistryService,
    FunctionToolService,
    McpToolService,
    McpSerenaService,
    McpMagicService,
    McpPlaywrightService,
    McpMultifetchService,
    McpSequentialService,
    McpContext7Service,
    McpGoFastService,
    McpExecutorService,
    McpHealthService,
    ToolConverterService,
    DynamicSchemaService,
    ToolPerformanceService,
    // LLM services
    LlmService,
    OpenAiService,
    OllamaService,
    StreamingHandlerService,
    // Workflow services
    WorkflowService,
    StatefulWorkflowEngine,
    ConditionalWorkflowEngine,
    WorkflowRealtimeGateway,
    WorkflowPatternsService,
    WorkflowStateService,
    // Memory services
    MemoryStoreService,
    ContextManagerService,
    VectorMemoryService,
    SemanticMemoryService,
    EmbeddingPipelineService,
    MemoryClusteringService,
    // Execution services
    CodeExecutorService,
    ExecutionSecurityService,
    // ExecutionMonitoringService, // Temporarily disabled due to dependency issue
  ],
  exports: [
    AmnaService,
    AgentService,
    TaskService,
    ToolRegistryService,
    McpSerenaService,
    McpMagicService,
    McpPlaywrightService,
    McpMultifetchService,
    McpSequentialService,
    McpContext7Service,
    McpGoFastService,
    McpExecutorService,
    McpHealthService,
    DynamicSchemaService,
    ToolPerformanceService,
    LlmService,
    WorkflowService,
    StatefulWorkflowEngine,
    ConditionalWorkflowEngine,
    MemoryStoreService,
    SemanticMemoryService,
    EmbeddingPipelineService,
    MemoryClusteringService,
    CodeExecutorService,
    ExecutionSecurityService,
    // ExecutionMonitoringService, // Temporarily disabled due to dependency issue
  ],
})
export class AmnaModule {}
