import { Injectable, Logger } from '@nestjs/common'
import { EventEmitter2 } from '@nestjs/event-emitter'
import type { IAgent, IAgentResponse } from '../interfaces/agent.interface'
import { LlmService } from '../llm/llm.service'
import type { BaseAgent } from './base-agent'
import { SimpleAgent } from './simple-agent'
import { TaskAgent } from './task-agent'

export interface ICreateAgentOptions {
  name: string
  role?: string
  goal?: string
  backstory?: string
  instructions?: string
  type?: 'simple' | 'task'
  llmConfig?: any
  tools?: any[]
}

@Injectable()
export class AgentService {
  private readonly logger = new Logger(AgentService.name)
  private agents: Map<string, BaseAgent> = new Map()

  constructor(
    private readonly llmService: LlmService,
    private readonly eventEmitter: EventEmitter2
  ) {}

  /**
   * Create a new agent
   */
  async createAgent(options: ICreateAgentOptions): Promise<BaseAgent> {
    let agent: BaseAgent

    const config = {
      name: options.name,
      role: options.role,
      goal: options.goal,
      backstory: options.backstory,
      instructions: options.instructions,
      llmConfig: options.llmConfig,
      tools: options.tools || [],
    }

    switch (options.type || 'simple') {
      case 'task':
        agent = new TaskAgent(this.llmService, config)
        break
      case 'simple':
      default:
        agent = new SimpleAgent(this.llmService, config)
        break
    }

    this.agents.set(agent.id, agent)
    this.logger.log(`Created agent: ${agent.name} (${agent.id})`)

    this.eventEmitter.emit('agent.created', {
      agentId: agent.id,
      name: agent.name,
      type: options.type || 'simple',
    })

    return agent
  }

  /**
   * Get an agent by ID
   */
  getAgent(agentId: string): BaseAgent | undefined {
    return this.agents.get(agentId)
  }

  /**
   * Get all agents
   */
  getAllAgents(): BaseAgent[] {
    return Array.from(this.agents.values())
  }

  /**
   * Remove an agent
   */
  removeAgent(agentId: string): boolean {
    const agent = this.agents.get(agentId)
    if (agent) {
      this.agents.delete(agentId)
      this.logger.log(`Removed agent: ${agent.name} (${agentId})`)

      this.eventEmitter.emit('agent.removed', {
        agentId,
        name: agent.name,
      })

      return true
    }
    return false
  }

  /**
   * Execute an agent with the given input
   */
  async executeAgent(agentId: string, input: string): Promise<IAgentResponse> {
    const agent = this.agents.get(agentId)
    if (!agent) {
      throw new Error(`Agent with ID ${agentId} not found`)
    }

    this.logger.log(`Executing agent: ${agent.name} (${agentId})`)

    try {
      const response = await agent.execute(input)

      this.eventEmitter.emit('agent.executed', {
        agentId,
        name: agent.name,
        input,
        response,
      })

      return response
    } catch (error) {
      this.logger.error(`Agent execution failed: ${error.message}`, error.stack)

      this.eventEmitter.emit('agent.error', {
        agentId,
        name: agent.name,
        error: error.message,
      })

      throw error
    }
  }

  /**
   * Update agent configuration
   */
  async updateAgent(agentId: string, updates: Partial<ICreateAgentOptions>): Promise<BaseAgent> {
    const agent = this.agents.get(agentId)
    if (!agent) {
      throw new Error(`Agent with ID ${agentId} not found`)
    }

    // Update agent properties
    if (updates.name !== undefined) agent.name = updates.name
    if (updates.role !== undefined) agent.role = updates.role
    if (updates.goal !== undefined) agent.goal = updates.goal
    if (updates.backstory !== undefined) agent.backstory = updates.backstory
    if (updates.instructions !== undefined) agent.instructions = updates.instructions
    if (updates.llmConfig !== undefined) agent.llmConfig = updates.llmConfig
    if (updates.tools !== undefined) agent.tools = updates.tools

    this.logger.log(`Updated agent: ${agent.name} (${agentId})`)

    this.eventEmitter.emit('agent.updated', {
      agentId,
      name: agent.name,
      updates,
    })

    return agent
  }

  /**
   * Get agent statistics
   */
  getAgentStats(agentId: string): any {
    const agent = this.agents.get(agentId)
    if (!agent) {
      throw new Error(`Agent with ID ${agentId} not found`)
    }

    return {
      id: agent.id,
      name: agent.name,
      role: agent.role,
      goal: agent.goal,
      toolCount: agent.tools.length,
      maxIterations: agent.maxIterations,
      allowDelegation: agent.allowDelegation,
      humanInputMode: agent.humanInputMode,
    }
  }

  /**
   * Clear all agents
   */
  clearAllAgents(): void {
    const count = this.agents.size
    this.agents.clear()
    this.logger.log(`Cleared all ${count} agents`)

    this.eventEmitter.emit('agents.cleared', {
      count,
    })
  }

  /**
   * Get agent count
   */
  getAgentCount(): number {
    return this.agents.size
  }

  /**
   * List all agent configurations
   */
  async listAgentConfigs(): Promise<IAgent[]> {
    return Array.from(this.agents.values()).map((agent) => ({
      id: agent.id,
      name: agent.name,
      role: agent.role,
      goal: agent.goal,
      backstory: agent.backstory,
      instructions: agent.instructions,
      tools: agent.tools,
      maxIterations: agent.maxIterations,
      allowDelegation: agent.allowDelegation,
      humanInputMode: agent.humanInputMode,
      llmConfig: agent.llmConfig,
    }))
  }
}
