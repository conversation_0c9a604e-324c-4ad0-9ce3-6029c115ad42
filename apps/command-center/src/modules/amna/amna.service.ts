import { Injectable, Logger } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { EventEmitter2 } from '@nestjs/event-emitter'

// Core services
import { AgentService } from './agents/agent.service'
import { AgentOrchestratorService } from './agents/agent-orchestrator.service'
// Interfaces
import type { IAgent, IAgentOrchestration } from './interfaces/agent.interface'
import type { ITask, ITaskSchedule } from './interfaces/task.interface'
import type { IWorkflow } from './interfaces/workflow.interface'
import { LlmService } from './llm/llm.service'
import { MemoryStoreService } from './memory/memory-store.service'
import { TaskService } from './tasks/task.service'
import { TaskExecutorService } from './tasks/task-executor.service'
import { TaskSchedulerService } from './tasks/task-scheduler.service'
import { ToolRegistryService } from './tools/tool-registry.service'
import { WorkflowService } from './workflows/workflow.service'

export enum AutomationMode {
  OFF = 'OFF',
  SEMI = 'SEMI',
  AUTO = 'AUTO',
}

export interface IAmnaConfig {
  automationMode: AutomationMode
  maxAutoActions: number
  requireApproval: string[]
  enabledFeatures: string[]
}

export interface IAmnaExecution {
  type: 'agent' | 'task' | 'workflow'
  name: string
  input: any
  config?: any
  automation?: {
    mode?: AutomationMode
    maxActions?: number
  }
}

@Injectable()
export class AmnaService {
  private readonly logger = new Logger(AmnaService.name)
  private automationMode: AutomationMode
  private executionHistory: any[] = []

  constructor(
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
    private readonly agentService: AgentService,
    private readonly agentOrchestrator: AgentOrchestratorService,
    private readonly taskService: TaskService,
    private readonly taskExecutor: TaskExecutorService,
    private readonly taskScheduler: TaskSchedulerService,
    private readonly toolRegistry: ToolRegistryService,
    private readonly llmService: LlmService,
    private readonly workflowService: WorkflowService,
    private readonly memoryStore: MemoryStoreService
  ) {
    this.automationMode = this.configService.get('amna.automation.mode') || AutomationMode.SEMI
    this.initializeAmna()
  }

  /**
   * Initialize AMNA
   */
  private async initializeAmna(): Promise<void> {
    this.logger.log('Initializing AMNA (AI Multi-Agent Nexus Architecture)...')

    // Load saved configurations
    await this.loadSavedConfigurations()

    // Register event handlers
    this.registerEventHandlers()

    // Initialize default tools
    this.initializeDefaultTools()

    this.logger.log('AMNA initialized successfully')
  }

  /**
   * Ultra-compressed execution method
   */
  public async execute(execution: IAmnaExecution): Promise<any> {
    const mode = execution.automation?.mode || this.automationMode

    // Check if approval needed
    if (mode === AutomationMode.SEMI && this.requiresApproval(execution)) {
      const approved = await this.requestApproval(execution)
      if (!approved) {
        throw new Error('Execution cancelled by user')
      }
    }

    // Track execution
    this.trackExecution(execution)

    try {
      switch (execution.type) {
        case 'agent':
          return await this.executeAgent(execution)
        case 'task':
          return await this.executeTask(execution)
        case 'workflow':
          return await this.executeWorkflow(execution)
        default:
          throw new Error(`Unknown execution type: ${execution.type}`)
      }
    } catch (error) {
      this.logger.error(`AMNA execution failed: ${error.message}`)
      throw error
    }
  }

  /**
   * Create and execute agent
   */
  private async executeAgent(execution: IAmnaExecution): Promise<any> {
    // Check if it's an orchestration
    if (execution.config?.agents && Array.isArray(execution.config.agents)) {
      const orchestration: IAgentOrchestration = {
        agents: execution.config.agents,
        mode: execution.config.mode || 'sequential',
        manager: execution.config.manager,
        maxRounds: execution.config.maxRounds,
        earlyTermination: execution.config.earlyTermination,
      }

      return await this.agentOrchestrator.orchestrate(
        orchestration,
        execution.input,
        execution.config.context
      )
    }

    // Single agent execution
    const agent = await this.agentService.createAgent({
      name: execution.name,
      ...execution.config,
    })

    return await this.agentService.executeAgent(agent.id, execution.input)
  }

  /**
   * Create and execute task
   */
  private async executeTask(execution: IAmnaExecution): Promise<any> {
    // Check if it's a task schedule
    if (execution.config?.tasks && Array.isArray(execution.config.tasks)) {
      const schedule: ITaskSchedule = {
        tasks: execution.config.tasks,
        dependencies: execution.config.dependencies,
        parallelExecution: execution.config.parallelExecution,
        maxConcurrent: execution.config.maxConcurrent,
        stopOnFailure: execution.config.stopOnFailure,
      }

      const scheduleId = await this.taskScheduler.scheduleTasks(schedule)
      return { scheduleId, status: 'scheduled' }
    }

    // Single task execution
    const task = await this.taskService.createTask({
      name: execution.name,
      ...execution.config,
    })

    return await this.taskExecutor.executeTask({
      taskId: task.id!,
      input: execution.input,
      context: execution.config?.context,
    })
  }

  /**
   * Create and execute workflow
   */
  private async executeWorkflow(execution: IAmnaExecution): Promise<any> {
    const workflow = await this.workflowService.createWorkflow({
      name: execution.name,
      ...execution.config,
    })

    return await this.workflowService.executeWorkflow({
      workflowId: workflow.id!,
      input: execution.input,
    })
  }

  /**
   * Quick agent creation
   */
  public async agent(name: string, instructions: string, tools?: string[]): Promise<any> {
    const toolInstances = tools
      ? tools.map((t) => this.toolRegistry.getTool(t)).filter((t) => t)
      : []

    const agent = await this.agentService.createAgent({
      name,
      instructions,
      tools: toolInstances,
    })

    return {
      execute: (input: string) => this.agentService.executeAgent(agent.id, input),
    }
  }

  /**
   * Quick task creation
   */
  public async task(
    name: string,
    description: string,
    expectedOutput: string,
    agent?: string | IAgent
  ): Promise<any> {
    const task = await this.taskService.createTask({
      name,
      description,
      expectedOutput,
      agent,
    })

    return {
      execute: (input?: string) =>
        this.taskExecutor.executeTask({
          taskId: task.id!,
          input,
        }),
    }
  }

  /**
   * Quick workflow creation
   */
  public async workflow(name: string, agents: IAgent[], tasks: ITask[]): Promise<any> {
    const workflow = await this.workflowService.createWorkflow({
      name,
      agents,
      tasks,
    })

    return {
      execute: (input?: any) =>
        this.workflowService.executeWorkflow({
          workflowId: workflow.id!,
          input,
        }),
    }
  }

  /**
   * Register a tool
   */
  public registerTool(name: string, fn: Function, description: string): void {
    this.toolRegistry.registerFunction(name, fn, description)
  }

  /**
   * Set automation mode
   */
  public setAutomationMode(mode: AutomationMode): void {
    this.automationMode = mode
    this.logger.log(`Automation mode set to: ${mode}`)

    this.eventEmitter.emit('amna.automation.modeChanged', { mode })
  }

  /**
   * Get automation mode
   */
  public getAutomationMode(): AutomationMode {
    return this.automationMode
  }

  /**
   * Get configuration
   */
  public getConfig(): IAmnaConfig {
    return {
      automationMode: this.automationMode,
      maxAutoActions: this.configService.get('amna.automation.maxAutoActions') || 10,
      requireApproval: this.configService.get('amna.automation.requireApproval') || [],
      enabledFeatures: this.configService.get('amna.features.enabled') || [],
    }
  }

  /**
   * Get available tools
   */
  public getTools(): any[] {
    return this.toolRegistry.getAllTools()
  }

  /**
   * Get agents
   */
  public async getAgents(): Promise<IAgent[]> {
    return await this.agentService.listAgentConfigs()
  }

  /**
   * Get tasks
   */
  public getTasks(): ITask[] {
    return this.taskService.getAllTasks()
  }

  /**
   * Get workflows
   */
  public async getWorkflows(): Promise<IWorkflow[]> {
    return await this.workflowService.listWorkflows()
  }

  /**
   * Get execution history
   */
  public getExecutionHistory(limit: number = 10): any[] {
    return this.executionHistory.slice(-limit)
  }

  /**
   * Load saved configurations
   */
  private async loadSavedConfigurations(): Promise<void> {
    try {
      // Load saved agents
      const agents = await this.agentService.listAgentConfigs()
      this.logger.log(`Loaded ${agents.length} saved agents`)

      // Load saved tasks
      await this.taskService.loadTasks()

      // Load saved workflows
      const workflows = await this.workflowService.listWorkflows()
      this.logger.log(`Loaded ${workflows.length} saved workflows`)
    } catch (error) {
      this.logger.error(`Failed to load saved configurations: ${error.message}`)
    }
  }

  /**
   * Register event handlers
   */
  private registerEventHandlers(): void {
    // Agent events
    this.eventEmitter.on('agent.completed', (event) => {
      this.logger.log(`Agent ${event.agentName} completed`)
    })

    // Task events
    this.eventEmitter.on('task.completed', (event) => {
      this.logger.log(`Task ${event.taskId} completed`)
    })

    // Workflow events
    this.eventEmitter.on('workflow.completed', (event) => {
      this.logger.log(`Workflow ${event.workflowId} completed`)
    })
  }

  /**
   * Initialize default tools
   */
  private initializeDefaultTools(): void {
    // Search tool
    this.registerTool(
      'search',
      async (args: { query: string }) => {
        // Implementation would use actual search
        return { results: [`Mock result for: ${args.query}`] }
      },
      'Search for information'
    )

    // Code execution tool (now with real implementation)
    this.registerTool(
      'execute_code',
      async (args: { code: string; language: string; timeout?: number; memory_limit?: string }) => {
        // Real secure code execution
        const { CodeExecutorService } = await import('./execution/code-executor.service.js')
        const codeExecutor = new CodeExecutorService(this.configService, this.eventEmitter)

        const result = await codeExecutor.executeCode({
          code: args.code,
          language: args.language,
          timeout: args.timeout,
          memory_limit: args.memory_limit,
          network_access: false, // Secure by default
        })

        return {
          success: result.success,
          output: result.output,
          stdout: result.stdout,
          stderr: result.stderr,
          execution_time: result.execution_time,
          exit_code: result.exit_code,
        }
      },
      'Execute code securely in a sandboxed Docker container'
    )
  }

  /**
   * Check if approval required
   */
  private requiresApproval(execution: IAmnaExecution): boolean {
    const requireApproval =
      this.configService.get<string[]>('amna.automation.requireApproval') || []

    return requireApproval.some(
      (pattern) => execution.name.includes(pattern) || execution.type === pattern
    )
  }

  /**
   * Request approval
   */
  private async requestApproval(execution: IAmnaExecution): Promise<boolean> {
    this.eventEmitter.emit('amna.approval.requested', execution)

    // In real implementation, would wait for user response
    // For now, auto-approve in SEMI mode
    return true
  }

  /**
   * Track execution
   */
  private trackExecution(execution: IAmnaExecution): void {
    const record = {
      ...execution,
      timestamp: new Date(),
      mode: this.automationMode,
    }

    this.executionHistory.push(record)

    // Keep only last 100 executions
    if (this.executionHistory.length > 100) {
      this.executionHistory = this.executionHistory.slice(-100)
    }

    this.eventEmitter.emit('amna.execution.tracked', record)
  }

  /**
   * Export configuration
   */
  public async exportConfig(): Promise<any> {
    return {
      version: '1.0.0',
      config: this.getConfig(),
      agents: await this.getAgents(),
      tasks: this.getTasks(),
      workflows: await this.getWorkflows(),
      tools: this.getTools().map((t) => ({
        name: t.name,
        description: t.description,
        category: t.category,
      })),
    }
  }

  /**
   * Import configuration
   */
  public async importConfig(config: any): Promise<void> {
    this.logger.log('Importing AMNA configuration...')

    // Import agents
    if (config.agents) {
      for (const agent of config.agents) {
        await this.agentService.createAgent(agent)
      }
    }

    // Import tasks
    if (config.tasks) {
      for (const task of config.tasks) {
        await this.taskService.createTask(task)
      }
    }

    // Import workflows
    if (config.workflows) {
      for (const workflow of config.workflows) {
        await this.workflowService.createWorkflow(workflow)
      }
    }

    this.logger.log('AMNA configuration imported successfully')
  }

  /**
   * Get statistics
   */
  public async getStats(): Promise<any> {
    const [agents, workflows] = await Promise.all([this.getAgents(), this.getWorkflows()])

    const tasks = this.getTasks()
    const tools = this.getTools()
    const history = this.getExecutionHistory(100)

    return {
      counts: {
        agents: agents.length,
        tasks: tasks.length,
        workflows: workflows.length,
        tools: tools.length,
        executions: history.length,
      },
      recentExecutions: history.slice(-5),
      automation: this.getConfig(),
    }
  }
}
