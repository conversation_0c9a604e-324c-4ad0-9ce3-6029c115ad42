import { Logger, UseGuards } from '@nestjs/common';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import {
  ConnectedSocket,
  MessageBody,
  type OnGatewayConnection,
  type OnGatewayDisconnect,
  type OnGatewayInit,
  SubscribeMessage,
  WebSocketGateway,
  WebSocketServer,
  WsException,
} from '@nestjs/websockets';
import type { Server, Socket } from 'socket.io';
import { WsAuthGuard } from '../../common/guards/ws-auth.guard';
import { AmnaService, type IAmnaExecution } from './amna.service';

interface ClientData {
  userId: string;
  sessionId: string;
  subscriptions: Set<string>;
}

@WebSocketGateway({
  namespace: '/amna',
  cors: {
    origin: '*',
    credentials: true,
  },
})
export class AmnaGateway
  implements OnGatewayInit, OnGatewayConnection, OnGatewayDisconnect
{
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(AmnaGateway.name);
  private clients: Map<string, ClientData> = new Map();

  constructor(
    private readonly amnaService: AmnaService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  afterInit(server: Server) {
    this.logger.log('AMNA WebSocket Gateway initialized');
  }

  async handleConnection(client: Socket) {
    this.logger.log(`Client connected: ${client.id}`);

    // Initialize client data
    const clientData: ClientData = {
      userId: client.handshake.auth?.userId || 'anonymous',
      sessionId: client.id,
      subscriptions: new Set(),
    };

    this.clients.set(client.id, clientData);

    // Send welcome message
    client.emit('connected', {
      sessionId: client.id,
      config: this.amnaService.getConfig(),
    });
  }

  handleDisconnect(client: Socket) {
    this.logger.log(`Client disconnected: ${client.id}`);
    this.clients.delete(client.id);
  }

  /**
   * Execute AMNA command
   */
  @SubscribeMessage('execute')
  @UseGuards(WsAuthGuard)
  async handleExecute(
    @MessageBody() data: IAmnaExecution,
    @ConnectedSocket() client: Socket,
  ): Promise<void> {
    try {
      this.logger.log(`Executing command: ${data.type} - ${data.name}`);

      // Send execution started event
      client.emit('execution:started', {
        type: data.type,
        name: data.name,
        timestamp: new Date(),
      });

      // Execute with streaming support
      if (data.config?.stream) {
        await this.executeWithStream(data, client);
      } else {
        const result = await this.amnaService.execute(data);

        client.emit('execution:completed', {
          result,
          timestamp: new Date(),
        });
      }
    } catch (error) {
      this.logger.error(`Execution failed: ${error.message}`);

      client.emit('execution:failed', {
        error: error.message,
        timestamp: new Date(),
      });
    }
  }

  /**
   * Execute with streaming
   */
  private async executeWithStream(
    execution: IAmnaExecution,
    client: Socket,
  ): Promise<void> {
    // Create a custom execution that streams results
    const streamId = `stream-${Date.now()}`;

    // Subscribe to stream events
    const streamHandler = (event: any) => {
      client.emit('stream:chunk', {
        streamId,
        chunk: event.chunk,
        timestamp: new Date(),
      });
    };

    this.eventEmitter.on(`stream.${streamId}`, streamHandler);

    try {
      // Execute with stream flag
      const result = await this.amnaService.execute({
        ...execution,
        config: {
          ...execution.config,
          streamId,
        },
      });

      client.emit('stream:completed', {
        streamId,
        result,
        timestamp: new Date(),
      });
    } finally {
      // Cleanup listener
      this.eventEmitter.off(`stream.${streamId}`, streamHandler);
    }
  }

  /**
   * Subscribe to events
   */
  @SubscribeMessage('subscribe')
  async handleSubscribe(
    @MessageBody() data: { events: string[] },
    @ConnectedSocket() client: Socket,
  ): Promise<void> {
    const clientData = this.clients.get(client.id);
    if (!clientData) return;

    // Add subscriptions
    data.events.forEach((event) => {
      clientData.subscriptions.add(event);
      client.join(`event:${event}`);
    });

    client.emit('subscribed', {
      events: data.events,
      timestamp: new Date(),
    });
  }

  /**
   * Unsubscribe from events
   */
  @SubscribeMessage('unsubscribe')
  async handleUnsubscribe(
    @MessageBody() data: { events: string[] },
    @ConnectedSocket() client: Socket,
  ): Promise<void> {
    const clientData = this.clients.get(client.id);
    if (!clientData) return;

    // Remove subscriptions
    data.events.forEach((event) => {
      clientData.subscriptions.delete(event);
      client.leave(`event:${event}`);
    });

    client.emit('unsubscribed', {
      events: data.events,
      timestamp: new Date(),
    });
  }

  /**
   * Get status
   */
  @SubscribeMessage('status')
  async handleStatus(@ConnectedSocket() client: Socket): Promise<void> {
    const stats = await this.amnaService.getStats();

    client.emit('status', {
      stats,
      timestamp: new Date(),
    });
  }

  /**
   * Set automation mode
   */
  @SubscribeMessage('setAutomationMode')
  @UseGuards(WsAuthGuard)
  async handleSetAutomationMode(
    @MessageBody() data: { mode: string },
    @ConnectedSocket() client: Socket,
  ): Promise<void> {
    try {
      this.amnaService.setAutomationMode(data.mode as any);

      // Broadcast to all clients
      this.server.emit('automationModeChanged', {
        mode: data.mode,
        timestamp: new Date(),
      });
    } catch (error) {
      throw new WsException(error.message);
    }
  }

  /**
   * Handle agent events
   */
  @OnEvent('agent.*')
  handleAgentEvent(event: any) {
    this.server.to('event:agent').emit('agent:event', event);
  }

  /**
   * Handle task events
   */
  @OnEvent('task.*')
  handleTaskEvent(event: any) {
    this.server.to('event:task').emit('task:event', event);
  }

  /**
   * Handle workflow events
   */
  @OnEvent('workflow.*')
  handleWorkflowEvent(event: any) {
    this.server.to('event:workflow').emit('workflow:event', event);
  }

  /**
   * Handle approval requests
   */
  @OnEvent('amna.approval.requested')
  handleApprovalRequest(execution: IAmnaExecution) {
    this.server.emit('approval:requested', {
      execution,
      timestamp: new Date(),
    });
  }

  /**
   * Respond to approval
   */
  @SubscribeMessage('approval:respond')
  async handleApprovalResponse(
    @MessageBody() data: { executionId: string; approved: boolean },
    @ConnectedSocket() client: Socket,
  ): Promise<void> {
    this.eventEmitter.emit('amna.approval.response', data);

    client.emit('approval:processed', {
      executionId: data.executionId,
      approved: data.approved,
      timestamp: new Date(),
    });
  }

  /**
   * Broadcast message to all clients
   */
  public broadcast(event: string, data: any): void {
    this.server.emit(event, data);
  }

  /**
   * Send message to specific client
   */
  public sendToClient(clientId: string, event: string, data: any): void {
    this.server.to(clientId).emit(event, data);
  }

  /**
   * Get connected clients count
   */
  public getConnectedClientsCount(): number {
    return this.clients.size;
  }
}
