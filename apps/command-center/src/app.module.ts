import { Module } from '@nestjs/common'
import { ConfigModule } from '@nestjs/config'
import { App<PERSON>ontroller } from './app.controller'
import { AppService } from './app.service'
import { CommonModule } from './common/common.module'
// import { PerformanceModule } from './modules/performance/performance.module';
// import { ScalabilityModule } from './modules/scalability/scalability.module';
// import { TestingModule } from './modules/testing/testing.module';
// import { DeploymentModule } from './modules/deployment/deployment.module';
import { AmnaModule } from './modules/amna/amna.module';
// import { TrainingModule } from './modules/training/training.module';
// import { VendorModule } from './modules/vendor/vendor.module';
// import { WinsModule } from './modules/wins/wins.module';
// import { EmailModule } from './modules/email/email.module';
// import { SystemMonitoringModule } from './modules/system-monitoring/system-monitoring.module';
// import { MonitoringModule } from './modules/monitoring/monitoring.module';
import configuration from './config/configuration'
import { QueueModule } from './modules/queue/queue.module'
import { VectorModule } from './modules/vector/vector.module'
import { PrismaModule } from './database/prisma/prisma.module'
import { HealthModule } from './health/health.module'
import { AuthModule } from './modules/auth/auth.module'
import { EnhancedCacheModule } from './modules/cache/cache.module'
import { LAndDModule } from './modules/l-and-d/l-and-d.module'
import { NotificationModule } from './modules/notifications/notification.module'
// import { DocumentsModule } from './modules/documents/documents.module';
// import { FilesModule } from './modules/files/files.module';
import { RedisModule } from './modules/redis/redis.module'
import { SyncModule } from './modules/sync/sync.module'
import { UsersModule } from './modules/users/users.module'

@Module({
  imports: [
    ConfigModule.forRoot({
      load: [configuration],
    }),
    PrismaModule,
    RedisModule,
    // EnhancedCacheModule, // Temporarily disabled
    // QueueModule, // Temporarily disabled due to circular dependency issue
    // VectorModule, // Temporarily disabled due to dependency issues
    // PerformanceModule, // Temporarily disabled for testing
    // ScalabilityModule, // Temporarily disabled for testing
    // TestingModule, // Temporarily disabled for testing
    // DeploymentModule, // Temporarily disabled for testing
    CommonModule,
    // LAndDModule, // Temporarily disabled
    // NotificationModule, // Temporarily disabled
    // SyncModule, // Temporarily disabled
    // HealthModule, // Temporarily disabled due to dependency issues
    UsersModule,
    AuthModule,
    // DocumentsModule, // Temporarily disabled for testing
    // FilesModule, // Temporarily disabled for testing
    AmnaModule,
    // TrainingModule, // Temporarily disabled for testing
    // VendorModule, // Temporarily disabled for testing
    // WinsModule, // Temporarily disabled for testing
    // EmailModule, // Temporarily disabled for testing
    // MonitoringModule, // Temporarily disabled for testing
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
