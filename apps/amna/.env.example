# AMNA Environment Configuration
# Copy this file to .env and update the values

# API Configuration
# The base URL for the Command Center API
VITE_API_BASE_URL=http://localhost:3000

# WebSocket URL for real-time features
VITE_WS_URL=ws://localhost:3000

# Feature Flags
# Enable WebSocket connections for real-time updates
VITE_ENABLE_WEBSOCKET=true

# Enable offline mode (stores data locally)
VITE_ENABLE_OFFLINE=false

# Environment
# Options: development, staging, production
VITE_ENV=development

# External Services (optional)
# Sentry DSN for error tracking
VITE_SENTRY_DSN=

# Analytics tracking ID
VITE_ANALYTICS_ID=

# Support email address
VITE_SUPPORT_EMAIL=<EMAIL>

# App Info
VITE_APP_NAME=AMNA
VITE_APP_VERSION=1.0.0