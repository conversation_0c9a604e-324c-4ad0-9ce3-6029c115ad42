import { type RenderOptions, render } from '@testing-library/react'
import type * as React from 'react'
import type { ReactElement } from 'react'
import { ToastProvider } from '@/components/ui/toast'

// Custom render function that includes common providers
function AllTheProviders({ children }: { children: React.ReactNode }) {
  return <ToastProvider>{children}</ToastProvider>
}

const customRender = (ui: ReactElement, options?: Omit<RenderOptions, 'wrapper'>) =>
  render(ui, { wrapper: AllTheProviders, ...options })

export * from '@testing-library/react'
export { customRender as render }
