import { HttpResponse, http } from 'msw'
import type { ApiErrorCode } from '@/api/errors'
import { server } from './mocks/server'

/**
 * Override a specific API endpoint for a test
 */
export function mockApiEndpoint<T>(
  method: 'get' | 'post' | 'put' | 'patch' | 'delete',
  path: string,
  response: T,
  status: number = 200
) {
  server.use(
    http[method](path, () => {
      return HttpResponse.json(response, { status })
    })
  )
}

/**
 * Mock an API error response
 */
export function mockApiError(
  method: 'get' | 'post' | 'put' | 'patch' | 'delete',
  path: string,
  code: ApiErrorCode,
  message: string,
  status: number = 500
) {
  server.use(
    http[method](path, () => {
      return HttpResponse.json(
        {
          error: {
            code,
            message,
          },
          status: 'error',
          timestamp: new Date().toISOString(),
        },
        { status }
      )
    })
  )
}

/**
 * Mock a network error
 */
export function mockNetworkError(
  method: 'get' | 'post' | 'put' | 'patch' | 'delete',
  path: string
) {
  server.use(
    http[method](path, () => {
      return HttpResponse.error()
    })
  )
}

/**
 * Mock a timeout
 */
export function mockTimeout(
  method: 'get' | 'post' | 'put' | 'patch' | 'delete',
  path: string,
  delay: number = 35000 // Longer than default timeout
) {
  server.use(
    http[method](path, async () => {
      await new Promise((resolve) => setTimeout(resolve, delay))
      return HttpResponse.json({})
    })
  )
}

/**
 * Wait for all pending API requests to complete
 */
export async function waitForApiCalls(): Promise<void> {
  // Wait a tick for any pending promises
  await new Promise((resolve) => setTimeout(resolve, 0))
}

/**
 * Create a mock file for upload testing
 */
export function createMockFile(
  name: string = 'test.png',
  size: number = 1024,
  type: string = 'image/png'
): File {
  const content = new Array(size).fill('a').join('')
  const blob = new Blob([content], { type })
  return new File([blob], name, { type })
}

/**
 * Mock localStorage for testing
 */
export class MockLocalStorage implements Storage {
  private store: Map<string, string> = new Map()

  get length(): number {
    return this.store.size
  }

  clear(): void {
    this.store.clear()
  }

  getItem(key: string): string | null {
    return this.store.get(key) || null
  }

  key(index: number): string | null {
    return Array.from(this.store.keys())[index] || null
  }

  removeItem(key: string): void {
    this.store.delete(key)
  }

  setItem(key: string, value: string): void {
    this.store.set(key, value)
  }
}

/**
 * Setup mock localStorage
 */
export function setupMockLocalStorage(): MockLocalStorage {
  const mockStorage = new MockLocalStorage()
  Object.defineProperty(window, 'localStorage', {
    value: mockStorage,
    writable: true,
  })
  return mockStorage
}

/**
 * Mock WebSocket for testing
 */
export class MockWebSocket {
  static CONNECTING = 0
  static OPEN = 1
  static CLOSING = 2
  static CLOSED = 3

  url: string
  readyState: number = MockWebSocket.CONNECTING
  onopen: ((event: Event) => void) | null = null
  onclose: ((event: CloseEvent) => void) | null = null
  onerror: ((event: Event) => void) | null = null
  onmessage: ((event: MessageEvent) => void) | null = null

  constructor(url: string) {
    this.url = url
    setTimeout(() => {
      this.readyState = MockWebSocket.OPEN
      this.onopen?.(new Event('open'))
    }, 0)
  }

  send(data: string): void {
    if (this.readyState !== MockWebSocket.OPEN) {
      throw new Error('WebSocket is not open')
    }
    // Echo back for testing
    setTimeout(() => {
      this.onmessage?.(new MessageEvent('message', { data }))
    }, 10)
  }

  close(): void {
    this.readyState = MockWebSocket.CLOSED
    this.onclose?.(new CloseEvent('close'))
  }

  // Mock server message
  mockServerMessage(data: unknown): void {
    this.onmessage?.(
      new MessageEvent('message', {
        data: JSON.stringify(data),
      })
    )
  }
}

/**
 * Setup mock WebSocket
 */
export function setupMockWebSocket(): typeof MockWebSocket {
  ;(global as unknown as { WebSocket: typeof MockWebSocket }).WebSocket = MockWebSocket
  return MockWebSocket
}
