/**
 * Motion Components Library
 *
 * Reusable motion components that wrap Framer Motion functionality
 * with our design tokens and variants for consistent animations.
 */

import {
  AnimatePresence,
  type HTMLMotionProps,
  MotionProps,
  motion,
  type Variants,
} from 'framer-motion'
import React from 'react'
import { cn } from '@/lib/utils'
import { duration, spring } from './tokens'
import { type VariantName, variants } from './variants'

// Base motion component props
interface BaseMotionProps {
  className?: string
  children: React.ReactNode
  variant?: VariantName
  custom?: any
  delay?: number
  disabled?: boolean
}

// Motion wrapper component - the foundation for all motion components
export const MotionWrapper = React.forwardRef<
  HTMLDivElement,
  BaseMotionProps & HTMLMotionProps<'div'>
>(({ className, children, variant, delay = 0, disabled = false, ...props }, ref) => {
  // Use appropriate variant or fallback to provided variants
  const motionVariants = variant ? variants[variant] : props.variants

  // Disable animations if disabled prop is true or user prefers reduced motion
  const shouldAnimate = !disabled && !window.matchMedia('(prefers-reduced-motion: reduce)').matches

  if (!shouldAnimate) {
    return (
      <div ref={ref} className={className}>
        {children}
      </div>
    )
  }

  return (
    <motion.div
      ref={ref}
      className={className}
      variants={motionVariants}
      initial="initial"
      animate="animate"
      exit="exit"
      transition={{ delay }}
      {...props}
    >
      {children}
    </motion.div>
  )
})

MotionWrapper.displayName = 'MotionWrapper'

// Page transition component
interface PageTransitionProps extends BaseMotionProps {
  direction?: 'left' | 'right'
}

export const PageTransition: React.FC<PageTransitionProps> = ({
  children,
  direction,
  className,
  ...props
}) => {
  return (
    <MotionWrapper
      className={cn('min-h-screen', className)}
      variant="page"
      custom={direction}
      {...props}
    >
      {children}
    </MotionWrapper>
  )
}

// Slide transition component
export const SlideTransition: React.FC<PageTransitionProps> = ({
  children,
  direction = 'right',
  className,
  ...props
}) => {
  return (
    <MotionWrapper className={className} variant="slide" custom={direction} {...props}>
      {children}
    </MotionWrapper>
  )
}

// Fade transition component
export const FadeTransition: React.FC<BaseMotionProps> = ({ children, className, ...props }) => {
  return (
    <MotionWrapper className={className} variant="fade" {...props}>
      {children}
    </MotionWrapper>
  )
}

// Scale transition component
export const ScaleTransition: React.FC<BaseMotionProps> = ({ children, className, ...props }) => {
  return (
    <MotionWrapper className={className} variant="scale" {...props}>
      {children}
    </MotionWrapper>
  )
}

// Interactive button component with pop animation
interface PopButtonProps extends BaseMotionProps {
  onClick?: () => void
  whileHover?: boolean
  whileTap?: boolean
}

export const PopButton: React.FC<PopButtonProps> = ({
  children,
  className,
  onClick,
  whileHover = true,
  whileTap = true,
  ...props
}) => {
  return (
    <motion.button
      className={className}
      onClick={onClick}
      variants={variants.pop}
      initial="initial"
      animate="animate"
      whileHover={whileHover ? 'hover' : undefined}
      whileTap={whileTap ? 'tap' : undefined}
      {...props}
    >
      {children}
    </motion.button>
  )
}

// Stagger container for animating lists
interface StaggerContainerProps extends BaseMotionProps {
  staggerDelay?: number
  as?: keyof React.JSX.IntrinsicElements
}

export const StaggerContainer: React.FC<StaggerContainerProps> = ({
  children,
  className,
  staggerDelay = 0.1,
  as: Component = 'div',
  ...props
}) => {
  const containerVariants: Variants = {
    initial: {},
    animate: {
      transition: {
        staggerChildren: staggerDelay,
        delayChildren: 0.1,
      },
    },
    exit: {
      transition: {
        staggerChildren: 0.05,
        staggerDirection: -1,
      },
    },
  }

  const MotionComponent = motion[Component as keyof typeof motion] as any

  return (
    <MotionComponent
      className={className}
      variants={containerVariants}
      initial="initial"
      animate="animate"
      exit="exit"
      {...props}
    >
      {children}
    </MotionComponent>
  )
}

// Stagger item component
export const StaggerItem: React.FC<BaseMotionProps> = ({ children, className, ...props }) => {
  return (
    <MotionWrapper className={className} variant="staggerItem" {...props}>
      {children}
    </MotionWrapper>
  )
}

// Presence component for conditional rendering with animations
interface PresenceProps {
  children: React.ReactNode
  mode?: 'wait' | 'sync' | 'popLayout'
  initial?: boolean
}

export const Presence: React.FC<PresenceProps> = ({ children, mode = 'wait', initial = true }) => {
  return (
    <AnimatePresence mode={mode} initial={initial}>
      {children}
    </AnimatePresence>
  )
}

// Loading spinner component
interface SpinnerProps {
  className?: string
  size?: 'sm' | 'md' | 'lg'
  color?: string
}

export const Spinner: React.FC<SpinnerProps> = ({
  className,
  size = 'md',
  color = 'currentColor',
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
  }

  return (
    <motion.div
      className={cn(
        'border-2 border-current border-r-transparent rounded-full',
        sizeClasses[size],
        className
      )}
      style={{ borderColor: color, borderRightColor: 'transparent' }}
      variants={variants.rotate}
      initial="initial"
      animate="animate"
    />
  )
}

// Floating element component
export const FloatingElement: React.FC<BaseMotionProps> = ({ children, className, ...props }) => {
  return (
    <MotionWrapper className={className} variant="floating" {...props}>
      {children}
    </MotionWrapper>
  )
}

// Pulse component for loading states
export const PulseElement: React.FC<BaseMotionProps> = ({ children, className, ...props }) => {
  return (
    <MotionWrapper className={className} variant="pulse" {...props}>
      {children}
    </MotionWrapper>
  )
}

// Bounce component for attention-grabbing
export const BounceElement: React.FC<BaseMotionProps> = ({ children, className, ...props }) => {
  return (
    <MotionWrapper className={className} variant="bounce" {...props}>
      {children}
    </MotionWrapper>
  )
}

// Shake component for error states
interface ShakeElementProps extends BaseMotionProps {
  trigger?: boolean
}

export const ShakeElement: React.FC<ShakeElementProps> = ({
  children,
  className,
  trigger = false,
  ...props
}) => {
  return (
    <motion.div
      className={className}
      variants={variants.shake}
      animate={trigger ? 'animate' : 'initial'}
      {...props}
    >
      {children}
    </motion.div>
  )
}

// Smooth layout component for layout animations
interface LayoutProps extends BaseMotionProps {
  layoutId?: string
}

export const LayoutElement: React.FC<LayoutProps> = ({
  children,
  className,
  layoutId,
  ...props
}) => {
  return (
    <motion.div
      className={className}
      layout
      layoutId={layoutId}
      transition={spring.smooth}
      {...props}
    >
      {children}
    </motion.div>
  )
}

// Gesture-enabled component
interface GestureElementProps extends BaseMotionProps {
  onSwipeLeft?: () => void
  onSwipeRight?: () => void
  onSwipeUp?: () => void
  onSwipeDown?: () => void
  dragable?: boolean
}

export const GestureElement: React.FC<GestureElementProps> = ({
  children,
  className,
  onSwipeLeft,
  onSwipeRight,
  onSwipeUp,
  onSwipeDown,
  dragable = false,
  ...props
}) => {
  const handleDragEnd = (event: any, info: any) => {
    const { offset, velocity } = info
    const swipeThreshold = 50
    const velocityThreshold = 500

    // Detect swipe direction
    if (Math.abs(offset.x) > Math.abs(offset.y)) {
      // Horizontal swipe
      if (offset.x > swipeThreshold || velocity.x > velocityThreshold) {
        onSwipeRight?.()
      } else if (offset.x < -swipeThreshold || velocity.x < -velocityThreshold) {
        onSwipeLeft?.()
      }
    } else {
      // Vertical swipe
      if (offset.y > swipeThreshold || velocity.y > velocityThreshold) {
        onSwipeDown?.()
      } else if (offset.y < -swipeThreshold || velocity.y < -velocityThreshold) {
        onSwipeUp?.()
      }
    }
  }

  return (
    <motion.div
      className={className}
      drag={dragable}
      dragConstraints={{ left: 0, right: 0, top: 0, bottom: 0 }}
      dragElastic={0.2}
      onDragEnd={handleDragEnd}
      whileDrag={{ scale: 1.05 }}
      {...props}
    >
      {children}
    </motion.div>
  )
}
