interface CacheEntry {
  data: any
  timestamp: number
  ttl: number
}

export class CacheService {
  private static instance: CacheService
  private cache: Map<string, CacheEntry> = new Map()
  private cleanupInterval: NodeJS.Timeout

  private constructor() {
    // Run cleanup every minute
    this.cleanupInterval = setInterval(() => {
      this.cleanup()
    }, 60000)
  }

  static getInstance(): CacheService {
    if (!CacheService.instance) {
      CacheService.instance = new CacheService()
    }
    return CacheService.instance
  }

  async set(key: string, data: any, ttl: number = 3600): Promise<void> {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl: ttl * 1000, // Convert to milliseconds
    })
  }

  async get(key: string): Promise<any | null> {
    const entry = this.cache.get(key)

    if (!entry) {
      return null
    }

    // Check if expired
    if (this.isExpired(entry)) {
      this.cache.delete(key)
      return null
    }

    return entry.data
  }

  async delete(key: string): Promise<void> {
    this.cache.delete(key)
  }

  async has(key: string): Promise<boolean> {
    const entry = this.cache.get(key)

    if (!entry) {
      return false
    }

    if (this.isExpired(entry)) {
      this.cache.delete(key)
      return false
    }

    return true
  }

  async keys(pattern?: string): Promise<string[]> {
    const allKeys = Array.from(this.cache.keys())

    if (!pattern) {
      return allKeys
    }

    // Simple pattern matching (supports * wildcard)
    const regex = new RegExp('^' + pattern.replace(/\*/g, '.*') + '$')
    return allKeys.filter((key) => regex.test(key))
  }

  async clear(): Promise<void> {
    this.cache.clear()
  }

  async size(): Promise<number> {
    return this.cache.size
  }

  // Batch operations
  async mset(items: Array<{ key: string; value: any; ttl?: number }>): Promise<void> {
    for (const item of items) {
      await this.set(item.key, item.value, item.ttl)
    }
  }

  async mget(keys: string[]): Promise<any[]> {
    return Promise.all(keys.map((key) => this.get(key)))
  }

  async mdel(keys: string[]): Promise<void> {
    for (const key of keys) {
      this.cache.delete(key)
    }
  }

  // Utility methods
  async getOrSet(key: string, factory: () => Promise<any>, ttl?: number): Promise<any> {
    const cached = await this.get(key)

    if (cached !== null) {
      return cached
    }

    const value = await factory()
    await this.set(key, value, ttl)
    return value
  }

  async invalidatePattern(pattern: string): Promise<void> {
    const keys = await this.keys(pattern)
    await this.mdel(keys)
  }

  // Statistics
  getStats(): {
    size: number
    hitRate: number
    memoryUsage: number
  } {
    return {
      size: this.cache.size,
      hitRate: this.calculateHitRate(),
      memoryUsage: this.estimateMemoryUsage(),
    }
  }

  private isExpired(entry: CacheEntry): boolean {
    return Date.now() > entry.timestamp + entry.ttl
  }

  private cleanup(): void {
    for (const [key, entry] of this.cache.entries()) {
      if (this.isExpired(entry)) {
        this.cache.delete(key)
      }
    }
  }

  private calculateHitRate(): number {
    // This is a simplified implementation
    // In production, you'd track hits and misses
    return 0
  }

  private estimateMemoryUsage(): number {
    // Rough estimate of memory usage
    let size = 0

    for (const entry of this.cache.values()) {
      size += JSON.stringify(entry).length
    }

    return size
  }

  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval)
    }
    this.cache.clear()
  }
}
