import { EventEmitter } from 'events'
import { io, type Socket } from 'socket.io-client'

export interface WebSocketConfig {
  url: string
  reconnectInterval?: number
  maxReconnectAttempts?: number
  auth?: any
}

export interface Message {
  type: string
  data: any
  timestamp: Date
  id?: string
}

export class WebSocketManager extends EventEmitter {
  private static instance: WebSocketManager
  private socket: Socket | null = null
  private config: WebSocketConfig
  private reconnectAttempts = 0
  private messageQueue: Message[] = []
  private isConnecting = false
  private pingInterval: NodeJS.Timeout | null = null

  private constructor(config: WebSocketConfig) {
    super()
    this.config = {
      reconnectInterval: 5000,
      maxReconnectAttempts: 10,
      ...config,
    }
  }

  static getInstance(config: WebSocketConfig): WebSocketManager {
    if (!WebSocketManager.instance) {
      WebSocketManager.instance = new WebSocketManager(config)
    }
    return WebSocketManager.instance
  }

  async connect(): Promise<void> {
    if (this.socket?.connected || this.isConnecting) {
      return
    }

    this.isConnecting = true

    try {
      this.socket = io(this.config.url, {
        transports: ['websocket'],
        auth: this.config.auth,
        reconnection: false, // Handle reconnection manually
      })

      this.setupEventListeners()

      await new Promise<void>((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Connection timeout'))
        }, 30000)

        this.socket!.once('connect', () => {
          clearTimeout(timeout)
          resolve()
        })

        this.socket!.once('connect_error', (error) => {
          clearTimeout(timeout)
          reject(error)
        })
      })

      this.isConnecting = false
      this.reconnectAttempts = 0
      this.startPingInterval()
      this.processMessageQueue()

      this.emit('connected')
    } catch (error) {
      this.isConnecting = false
      this.handleConnectionError(error)
      throw error
    }
  }

  disconnect(): void {
    if (this.socket) {
      this.stopPingInterval()
      this.socket.disconnect()
      this.socket = null
      this.emit('disconnected')
    }
  }

  isConnected(): boolean {
    return this.socket?.connected || false
  }

  async send(type: string, data: any): Promise<void> {
    const message: Message = {
      type,
      data,
      timestamp: new Date(),
      id: this.generateMessageId(),
    }

    if (this.isConnected()) {
      this.socket!.emit(type, message)
      this.emit('message.sent', message)
    } else {
      // Queue message for later
      this.messageQueue.push(message)
      this.emit('message.queued', message)

      // Try to reconnect
      this.reconnect()
    }
  }

  on(event: string, listener: (...args: any[]) => void): this {
    super.on(event, listener)

    // Also subscribe to socket events if connected
    if (
      this.socket &&
      !['connected', 'disconnected', 'error', 'message.sent', 'message.queued'].includes(event)
    ) {
      this.socket.on(event, listener)
    }

    return this
  }

  off(event: string, listener: (...args: any[]) => void): this {
    super.off(event, listener)

    // Also unsubscribe from socket events
    if (this.socket) {
      this.socket.off(event, listener)
    }

    return this
  }

  private setupEventListeners(): void {
    if (!this.socket) return

    this.socket.on('connect', () => {
      console.log('WebSocket connected')
      this.reconnectAttempts = 0
    })

    this.socket.on('disconnect', (reason) => {
      console.log('WebSocket disconnected:', reason)
      this.stopPingInterval()
      this.emit('disconnected', reason)

      if (reason === 'io server disconnect') {
        // Server disconnected us, don't auto-reconnect
        return
      }

      // Auto-reconnect
      this.reconnect()
    })

    this.socket.on('error', (error) => {
      console.error('WebSocket error:', error)
      this.emit('error', error)
    })

    this.socket.on('pong', () => {
      this.emit('pong')
    })

    // Handle integration-specific events
    this.socket.on('integration.update', (data) => {
      this.emit('integration.update', data)
    })

    this.socket.on('activity.update', (data) => {
      this.emit('activity.update', data)
    })

    this.socket.on('sync.status', (data) => {
      this.emit('sync.status', data)
    })

    this.socket.on('chat.response', (data) => {
      this.emit('chat.response', data)
    })

    this.socket.on('preferences.updated', (data) => {
      this.emit('preferences.updated', data)
    })

    // Forward all events to local emitter
    this.socket.onAny((event, ...args) => {
      this.emit(event, ...args)
    })
  }

  private async reconnect(): Promise<void> {
    if (this.isConnecting || this.isConnected()) {
      return
    }

    if (this.reconnectAttempts >= this.config.maxReconnectAttempts!) {
      this.emit('max.reconnect.attempts')
      return
    }

    this.reconnectAttempts++
    const delay = Math.min(
      this.config.reconnectInterval! * 2 ** (this.reconnectAttempts - 1),
      30000
    )

    console.log(`Reconnecting in ${delay}ms (attempt ${this.reconnectAttempts})`)

    setTimeout(() => {
      this.connect().catch(console.error)
    }, delay)
  }

  private handleConnectionError(error: any): void {
    console.error('Connection error:', error)
    this.emit('error', error)

    if (this.reconnectAttempts < this.config.maxReconnectAttempts!) {
      this.reconnect()
    }
  }

  private processMessageQueue(): void {
    if (this.messageQueue.length === 0) return

    console.log(`Processing ${this.messageQueue.length} queued messages`)

    const messages = [...this.messageQueue]
    this.messageQueue = []

    for (const message of messages) {
      this.socket!.emit(message.type, message)
      this.emit('message.sent', message)
    }
  }

  private startPingInterval(): void {
    this.pingInterval = setInterval(() => {
      if (this.isConnected()) {
        this.socket!.emit('ping')
      }
    }, 30000)
  }

  private stopPingInterval(): void {
    if (this.pingInterval) {
      clearInterval(this.pingInterval)
      this.pingInterval = null
    }
  }

  private generateMessageId(): string {
    return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  getQueueSize(): number {
    return this.messageQueue.length
  }

  clearQueue(): void {
    this.messageQueue = []
  }

  async waitForConnection(timeout: number = 10000): Promise<void> {
    if (this.isConnected()) return

    return new Promise((resolve, reject) => {
      const timer = setTimeout(() => {
        this.off('connected', handleConnect)
        reject(new Error('Connection timeout'))
      }, timeout)

      const handleConnect = () => {
        clearTimeout(timer)
        resolve()
      }

      this.once('connected', handleConnect)

      if (!this.isConnecting) {
        this.connect().catch(reject)
      }
    })
  }
}
