import { AMNAWidget, AuthProvider, IntegrationProvider, ThemeProvider } from '@luminar/shared-ui'
import { createRootRoute, Outlet } from '@tanstack/react-router'
import { TanStackRouterDevtools } from '@tanstack/react-router-devtools'
import { ScrollToTop } from '../components/ui/scroll-to-top'

export const Route = createRootRoute({
  component: () => (
    <AuthProvider>
      <ThemeProvider>
        <IntegrationProvider>
          <div className="min-h-screen bg-background">
            <Outlet />

            <div className="slide-in-from-right-4 fade-in animate-in delay-1200 duration-400">
              <ScrollToTop />
            </div>

            {/* AMNAWidget for cross-app integration */}
            <AMNAWidget
              position="bottom-right"
              context={{ app: 'amna', environment: 'production' }}
              showBadge={true}
            />
          </div>

          {/* Development tools */}
          {process.env.NODE_ENV === 'development' && <TanStackRouterDevtools />}
        </IntegrationProvider>
      </ThemeProvider>
    </AuthProvider>
  ),
})
