import type { Message } from '@/components/chat/ChatMessages'

// Mock chat API for development and testing
export const mockChatAPI = {
  async sendMessage(content: string): Promise<Message> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 1000))
    
    const responses = [
      "That's an interesting point. Let me help you with that.",
      "I understand what you're asking. Here's what I think...",
      "Great question! Based on the information available...",
      "Let me break this down for you step by step.",
      "I can definitely help you with that. Here's my suggestion...",
      "That's a common question. The best approach would be...",
      "I see what you mean. Let me provide some clarity on this.",
      "Thanks for asking! Here's what you need to know...",
    ]
    
    const randomResponse = responses[Math.floor(Math.random() * responses.length)]
    
    return {
      id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      content: randomResponse,
      role: 'assistant',
      timestamp: new Date(),
      user: {
        id: 'assistant',
        name: 'AMNA Assistant',
        avatar: '/avatars/amna-assistant.png'
      },
      reactions: [],
      isEdited: false,
      metadata: {
        confidence: 0.85 + Math.random() * 0.15,
        processingTime: Math.floor(Math.random() * 500) + 100
      }
    }
  },

  async getMessages(chatId: string): Promise<Message[]> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300))
    
    // Return empty array for new chats
    return []
  },

  async deleteMessage(messageId: string): Promise<void> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 200))
    
    // Mock successful deletion
    console.log(`Mock: Deleted message ${messageId}`)
  },

  async editMessage(messageId: string, newContent: string): Promise<Message> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300))
    
    // Return updated message
    return {
      id: messageId,
      content: newContent,
      role: 'user',
      timestamp: new Date(),
      user: {
        id: 'user',
        name: 'You',
        avatar: '/avatars/user.png'
      },
      reactions: [],
      isEdited: true,
      metadata: {
        editedAt: new Date()
      }
    }
  },

  async addReaction(messageId: string, emoji: string, userId: string): Promise<void> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 100))
    
    console.log(`Mock: Added reaction ${emoji} to message ${messageId} by user ${userId}`)
  },

  async removeReaction(messageId: string, reactionId: string): Promise<void> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 100))
    
    console.log(`Mock: Removed reaction ${reactionId} from message ${messageId}`)
  }
}

// Export individual functions for easier testing
export const {
  sendMessage,
  getMessages,
  deleteMessage,
  editMessage,
  addReaction,
  removeReaction
} = mockChatAPI
