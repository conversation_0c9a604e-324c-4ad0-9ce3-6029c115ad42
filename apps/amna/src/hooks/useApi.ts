import { useCallback, useEffect, useRef, useState } from 'react'
import { ApiError } from '@/api/errors'

interface UseApiOptions<T> {
  immediate?: boolean
  onSuccess?: (data: T) => void
  onError?: (error: ApiError) => void
  retryOnError?: boolean
  retryCount?: number
  cacheKey?: string
  cacheDuration?: number
}

interface UseApiReturn<T, P extends any[] = any[]> {
  data: T | null
  error: ApiError | null
  isLoading: boolean
  execute: (...params: P) => Promise<T>
  reset: () => void
  retry: () => Promise<T | null>
}

// Simple in-memory cache
const apiCache = new Map<string, { data: any; timestamp: number }>()

export function useApi<T, P extends any[] = any[]>(
  apiFunction: (...params: P) => Promise<T>,
  options: UseApiOptions<T> = {}
): UseApiReturn<T, P> {
  const {
    immediate = false,
    onSuccess,
    onError,
    retryOnError = false,
    retryCount = 3,
    cacheKey,
    cacheDuration = 5 * 60 * 1000, // 5 minutes default
  } = options

  const [data, setData] = useState<T | null>(null)
  const [error, setError] = useState<ApiError | null>(null)
  const [isLoading, setIsLoading] = useState(false)

  const lastParamsRef = useRef<P>()
  const isMountedRef = useRef(true)
  const retryCountRef = useRef(0)

  useEffect(() => {
    return () => {
      isMountedRef.current = false
    }
  }, [])

  const execute = useCallback(
    async (...params: P): Promise<T> => {
      lastParamsRef.current = params

      // Check cache first
      if (cacheKey) {
        const cached = apiCache.get(cacheKey)
        if (cached && Date.now() - cached.timestamp < cacheDuration) {
          setData(cached.data)
          setError(null)
          return cached.data
        }
      }

      setIsLoading(true)
      setError(null)

      try {
        const result = await apiFunction(...params)

        if (!isMountedRef.current) return result

        setData(result)
        retryCountRef.current = 0

        // Cache the result
        if (cacheKey) {
          apiCache.set(cacheKey, { data: result, timestamp: Date.now() })
        }

        onSuccess?.(result)
        return result
      } catch (err: any) {
        if (!isMountedRef.current) throw err

        const apiError =
          err instanceof ApiError
            ? err
            : new ApiError('Unknown error occurred', 'UNKNOWN_ERROR', 500, err)

        setError(apiError)

        // Retry logic
        if (retryOnError && retryCountRef.current < retryCount && apiError.isRetryable()) {
          retryCountRef.current++
          return execute(...params)
        }

        onError?.(apiError)
        throw apiError
      } finally {
        if (isMountedRef.current) {
          setIsLoading(false)
        }
      }
    },
    [apiFunction, onSuccess, onError, retryOnError, retryCount, cacheKey, cacheDuration]
  )

  const retry = useCallback(async () => {
    if (lastParamsRef.current) {
      return execute(...lastParamsRef.current)
    }
    return null
  }, [execute])

  const reset = useCallback(() => {
    setData(null)
    setError(null)
    setIsLoading(false)
    retryCountRef.current = 0
  }, [])

  // Execute immediately if requested
  useEffect(() => {
    if (immediate && lastParamsRef.current === undefined) {
      execute(...([] as unknown as P))
    }
  }, [immediate, execute])

  return {
    data,
    error,
    isLoading,
    execute,
    reset,
    retry,
  }
}

// Specialized hook for paginated data
interface UsePaginatedApiOptions<T> extends UseApiOptions<T> {
  pageSize?: number
}

interface PaginatedData<T> {
  items: T[]
  total: number
  page: number
  pageSize: number
  hasMore: boolean
}

export function usePaginatedApi<T, P extends any[] = any[]>(
  apiFunction: (
    page: number,
    pageSize: number,
    ...params: P
  ) => Promise<{ items: T[]; total: number }>,
  options: UsePaginatedApiOptions<any> = {}
) {
  const { pageSize = 20, ...restOptions } = options

  const [page, setPage] = useState(1)
  const [allItems, setAllItems] = useState<T[]>([])

  const wrappedApiFunction = useCallback(
    async (...params: P) => {
      const result = await apiFunction(page, pageSize, ...params)
      return {
        items: result.items,
        total: result.total,
        page,
        pageSize,
        hasMore: page * pageSize < result.total,
      } as PaginatedData<T>
    },
    [apiFunction, page, pageSize]
  )

  const api = useApi<PaginatedData<T>, P>(wrappedApiFunction, {
    ...restOptions,
    onSuccess: (data) => {
      if (page === 1) {
        setAllItems(data.items)
      } else {
        setAllItems((prev) => [...prev, ...data.items])
      }
      restOptions.onSuccess?.(data)
    },
  })

  const loadMore = useCallback(async () => {
    if (api.data?.hasMore && !api.isLoading) {
      setPage((prev) => prev + 1)
    }
  }, [api.data?.hasMore, api.isLoading])

  const refresh = useCallback(async () => {
    setPage(1)
    setAllItems([])
    return api.retry()
  }, [api])

  return {
    ...api,
    items: allItems,
    page,
    hasMore: api.data?.hasMore ?? false,
    loadMore,
    refresh,
  }
}

// Specialized hook for mutations (POST, PUT, DELETE)
export function useMutation<T, P = any>(
  mutationFunction: (params: P) => Promise<T>,
  options: UseApiOptions<T> = {}
) {
  const api = useApi<T, [P]>((params: P) => mutationFunction(params), options)

  const mutate = useCallback(
    async (params: P) => {
      return api.execute(params)
    },
    [api]
  )

  return {
    ...api,
    mutate,
    isLoading: api.isLoading,
    isPending: api.isLoading, // Alias for compatibility
  }
}

// Clear cache utility
export function clearApiCache(pattern?: string) {
  if (pattern) {
    for (const key of apiCache.keys()) {
      if (key.includes(pattern)) {
        apiCache.delete(key)
      }
    }
  } else {
    apiCache.clear()
  }
}
