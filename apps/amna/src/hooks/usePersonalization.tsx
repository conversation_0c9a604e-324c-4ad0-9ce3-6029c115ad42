import * as React from 'react'
import type { PersonalizationSettings } from '@/components/chat/PersonalizationPanel'

const DEFAULT_SETTINGS: PersonalizationSettings = {
  theme: {
    mode: 'system',
    accentColor: '#3b82f6',
    borderRadius: 'medium',
    density: 'comfortable',
  },
  ai: {
    personality: 'friendly',
    responseStyle: 'detailed',
    codeStyle: 'commented',
    language: 'english',
  },
  interface: {
    showAnimations: true,
    soundEffects: false,
    compactMode: false,
    showAvatars: true,
    messageTimestamps: true,
    quickResponses: true,
  },
  accessibility: {
    reducedMotion: false,
    highContrast: false,
    largeText: false,
    screenReader: false,
  },
}

const STORAGE_KEY = 'amna-personalization-settings'

interface UsePersonalizationReturn {
  settings: PersonalizationSettings
  updateSettings: (settings: PersonalizationSettings) => void
  resetSettings: () => void
  isLoading: boolean
}

export function usePersonalization(): UsePersonalizationReturn {
  const [settings, setSettings] = React.useState<PersonalizationSettings>(DEFAULT_SETTINGS)
  const [isLoading, setIsLoading] = React.useState(true)

  // Load settings from localStorage on mount
  React.useEffect(() => {
    try {
      const savedSettings = localStorage.getItem(STORAGE_KEY)
      if (savedSettings) {
        const parsed = JSON.parse(savedSettings)
        // Merge with defaults to ensure all keys exist
        const mergedSettings = {
          theme: { ...DEFAULT_SETTINGS.theme, ...parsed.theme },
          ai: { ...DEFAULT_SETTINGS.ai, ...parsed.ai },
          interface: { ...DEFAULT_SETTINGS.interface, ...parsed.interface },
          accessibility: { ...DEFAULT_SETTINGS.accessibility, ...parsed.accessibility },
        }
        setSettings(mergedSettings)
      }
    } catch (error) {
      console.warn('Failed to load personalization settings:', error)
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Save settings to localStorage whenever they change
  const updateSettings = React.useCallback((newSettings: PersonalizationSettings) => {
    setSettings(newSettings)

    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(newSettings))
    } catch (error) {
      console.warn('Failed to save personalization settings:', error)
    }
  }, [])

  // Reset to default settings
  const resetSettings = React.useCallback(() => {
    setSettings(DEFAULT_SETTINGS)

    try {
      localStorage.removeItem(STORAGE_KEY)
    } catch (error) {
      console.warn('Failed to reset personalization settings:', error)
    }
  }, [])

  // Apply theme-related settings to the document
  React.useEffect(() => {
    if (isLoading) {
      return
    }

    const root = document.documentElement
    const body = document.body

    // Apply theme mode
    if (settings.theme.mode === 'dark') {
      root.classList.add('dark')
    } else if (settings.theme.mode === 'light') {
      root.classList.remove('dark')
    } else {
      // System preference
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
      const updateTheme = (e: MediaQueryListEvent | MediaQueryList) => {
        if (e.matches) {
          root.classList.add('dark')
        } else {
          root.classList.remove('dark')
        }
      }

      updateTheme(mediaQuery)
      mediaQuery.addEventListener('change', updateTheme)

      return () => mediaQuery.removeEventListener('change', updateTheme)
    }
  }, [settings.theme.mode, isLoading])

  // Apply CSS custom properties
  React.useEffect(() => {
    if (isLoading) {
      return
    }

    const root = document.documentElement

    // Apply accent color
    root.style.setProperty('--color-primary', settings.theme.accentColor)

    // Apply border radius
    const radiusValues = {
      none: '0px',
      small: '0.25rem',
      medium: '0.5rem',
      large: '1rem',
    }
    root.style.setProperty('--radius', radiusValues[settings.theme.borderRadius])
  }, [settings.theme.accentColor, settings.theme.borderRadius, isLoading])

  // Apply density classes
  React.useEffect(() => {
    if (isLoading) {
      return
    }

    const body = document.body

    // Remove existing density classes
    body.classList.remove('density-compact', 'density-comfortable', 'density-spacious')

    // Add current density class
    body.classList.add(`density-${settings.theme.density}`)
  }, [settings.theme.density, isLoading])

  // Apply interface settings classes
  React.useEffect(() => {
    if (isLoading) {
      return
    }

    const body = document.body

    // Animations
    if (!settings.interface.showAnimations || settings.accessibility.reducedMotion) {
      body.classList.add('no-animations')
    } else {
      body.classList.remove('no-animations')
    }

    // Compact mode
    if (settings.interface.compactMode) {
      body.classList.add('compact-mode')
    } else {
      body.classList.remove('compact-mode')
    }

    // High contrast
    if (settings.accessibility.highContrast) {
      body.classList.add('high-contrast')
    } else {
      body.classList.remove('high-contrast')
    }

    // Large text
    if (settings.accessibility.largeText) {
      body.classList.add('large-text')
    } else {
      body.classList.remove('large-text')
    }

    // Screen reader optimizations
    if (settings.accessibility.screenReader) {
      body.classList.add('screen-reader-optimized')
    } else {
      body.classList.remove('screen-reader-optimized')
    }
  }, [
    settings.interface.showAnimations,
    settings.interface.compactMode,
    settings.accessibility.reducedMotion,
    settings.accessibility.highContrast,
    settings.accessibility.largeText,
    settings.accessibility.screenReader,
    isLoading,
  ])

  // Respect prefers-reduced-motion if accessibility.reducedMotion is enabled
  React.useEffect(() => {
    if (isLoading) {
      return
    }

    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)')
    const updateMotion = (e: MediaQueryListEvent | MediaQueryList) => {
      if (e.matches && settings.accessibility.reducedMotion) {
        document.body.classList.add('no-animations')
      }
    }

    updateMotion(mediaQuery)
    mediaQuery.addEventListener('change', updateMotion)

    return () => mediaQuery.removeEventListener('change', updateMotion)
  }, [settings.accessibility.reducedMotion, isLoading])

  return {
    settings,
    updateSettings,
    resetSettings,
    isLoading,
  }
}
