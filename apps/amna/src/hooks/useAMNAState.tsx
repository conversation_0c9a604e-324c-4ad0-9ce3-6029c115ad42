import { useCallback, useEffect, useState } from 'react'

// Simplified types for the hook
export interface ChatMessage {
  id: string
  content: string
  role: 'user' | 'assistant' | 'system'
  timestamp: Date
  metadata?: Record<string, unknown>
}

export interface AMNAState {
  // Conversation state
  conversationId: string | null
  messages: ChatMessage[]
  isLoading: boolean
  error: string | null

  // Connection state
  isConnected: boolean
  connectionStatus: 'connecting' | 'connected' | 'disconnected' | 'error'
}

export interface AMNAActions {
  // Message actions
  sendMessage: (message: string) => Promise<void>
  startConversation: (conversationId?: string) => Promise<void>
  endConversation: () => Promise<void>
  clearMessages: () => void
}

export interface SendMessageOptions {
  includeContext?: boolean
  userId?: string
}

// Simplified AMNA hook
export function useAMNAState() {
  const [state, setState] = useState<AMNAState>({
    conversationId: null,
    messages: [],
    isLoading: false,
    error: null,
    isConnected: false,
    connectionStatus: 'disconnected',
  })

  // Initialize connection
  useEffect(() => {
    setState(prev => ({
      ...prev,
      connectionStatus: 'connecting'
    }))

    // Simulate connection
    const timer = setTimeout(() => {
      setState(prev => ({
        ...prev,
        isConnected: true,
        connectionStatus: 'connected'
      }))
    }, 1000)

    return () => clearTimeout(timer)
  }, [])

  const sendMessage = useCallback(async (message: string) => {
    if (!state.isConnected) {
      setState(prev => ({ ...prev, error: 'Not connected' }))
      return
    }

    setState(prev => ({ ...prev, isLoading: true, error: null }))

    try {
      const userMessage: ChatMessage = {
        id: `msg_${Date.now()}_user`,
        content: message,
        role: 'user',
        timestamp: new Date(),
      }

      setState(prev => ({
        ...prev,
        messages: [...prev.messages, userMessage]
      }))

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))

      const assistantMessage: ChatMessage = {
        id: `msg_${Date.now()}_assistant`,
        content: `I received your message: "${message}". This is a simplified response.`,
        role: 'assistant',
        timestamp: new Date(),
      }

      setState(prev => ({
        ...prev,
        messages: [...prev.messages, assistantMessage],
        isLoading: false
      }))
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Unknown error',
        isLoading: false
      }))
    }
  }, [state.isConnected])

  const startConversation = useCallback(async (conversationId?: string) => {
    setState(prev => ({
      ...prev,
      conversationId: conversationId || `conv_${Date.now()}`,
      messages: [],
      error: null
    }))
  }, [])

  const endConversation = useCallback(async () => {
    setState(prev => ({
      ...prev,
      conversationId: null,
      messages: [],
      error: null
    }))
  }, [])

  const clearMessages = useCallback(() => {
    setState(prev => ({
      ...prev,
      messages: [],
      error: null
    }))
  }, [])

  const actions: AMNAActions = {
    sendMessage,
    startConversation,
    endConversation,
    clearMessages,
  }

  return {
    state,
    actions,
  }
}
