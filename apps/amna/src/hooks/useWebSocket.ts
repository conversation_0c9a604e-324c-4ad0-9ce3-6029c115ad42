import { useCallback, useEffect, useRef, useState } from 'react'
import { env } from '@/config/env'
import { useAuth } from './useAuth'

interface WebSocketMessage {
  type: string
  payload: any
  timestamp: string
  id?: string
}

interface UseWebSocketOptions {
  reconnect?: boolean
  reconnectInterval?: number
  reconnectAttempts?: number
  onOpen?: () => void
  onClose?: () => void
  onError?: (error: Event) => void
  onMessage?: (message: WebSocketMessage) => void
}

export function useWebSocket(url?: string, options: UseWebSocketOptions = {}) {
  const {
    reconnect = true,
    reconnectInterval = 5000,
    reconnectAttempts = 5,
    onOpen,
    onClose,
    onError,
    onMessage,
  } = options

  const { isAuthenticated, user } = useAuth()
  const [isConnected, setIsConnected] = useState(false)
  const [lastMessage, setLastMessage] = useState<WebSocketMessage | null>(null)
  const wsRef = useRef<WebSocket | null>(null)
  const reconnectCountRef = useRef(0)
  const reconnectTimeoutRef = useRef<NodeJS.Timeout>()

  const connect = useCallback(() => {
    if (!isAuthenticated || !env.enableWebSocket) {
      return
    }

    try {
      const wsUrl = url || env.wsUrl
      const token = localStorage.getItem('auth_token')
      const fullUrl = `${wsUrl}?token=${token}`

      wsRef.current = new WebSocket(fullUrl)

      wsRef.current.onopen = () => {
        console.log('WebSocket connected')
        setIsConnected(true)
        reconnectCountRef.current = 0
        onOpen?.()
      }

      wsRef.current.onclose = () => {
        console.log('WebSocket disconnected')
        setIsConnected(false)
        onClose?.()

        // Attempt to reconnect
        if (reconnect && reconnectCountRef.current < reconnectAttempts) {
          reconnectCountRef.current++
          console.log(`Reconnecting... (attempt ${reconnectCountRef.current})`)

          reconnectTimeoutRef.current = setTimeout(() => {
            connect()
          }, reconnectInterval)
        }
      }

      wsRef.current.onerror = (error) => {
        console.error('WebSocket error:', error)
        onError?.(error)
      }

      wsRef.current.onmessage = (event) => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data)
          setLastMessage(message)
          onMessage?.(message)
        } catch (err) {
          console.error('Failed to parse WebSocket message:', err)
        }
      }
    } catch (err) {
      console.error('Failed to create WebSocket connection:', err)
    }
  }, [
    isAuthenticated,
    url,
    reconnect,
    reconnectInterval,
    reconnectAttempts,
    onOpen,
    onClose,
    onError,
    onMessage,
  ])

  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current)
    }

    if (wsRef.current) {
      wsRef.current.close()
      wsRef.current = null
    }
  }, [])

  const sendMessage = useCallback((type: string, payload: any) => {
    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
      const message: WebSocketMessage = {
        type,
        payload,
        timestamp: new Date().toISOString(),
        id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      }

      wsRef.current.send(JSON.stringify(message))
      return true
    }

    console.warn('WebSocket is not connected')
    return false
  }, [])

  // Auto-connect when authenticated
  useEffect(() => {
    if (isAuthenticated && env.enableWebSocket) {
      connect()
    }

    return () => {
      disconnect()
    }
  }, [isAuthenticated, connect, disconnect])

  return {
    isConnected,
    lastMessage,
    sendMessage,
    connect,
    disconnect,
  }
}

// Specialized hook for AMNA real-time features
export function useAmnaWebSocket() {
  const [agentStatus, setAgentStatus] = useState<Record<string, string>>({})
  const [taskUpdates, setTaskUpdates] = useState<any[]>([])
  const [chatMessages, setChatMessages] = useState<any[]>([])

  const handleMessage = useCallback((message: WebSocketMessage) => {
    switch (message.type) {
      case 'agent.status':
        setAgentStatus((prev) => ({
          ...prev,
          [message.payload.agentId]: message.payload.status,
        }))
        break

      case 'task.update':
        setTaskUpdates((prev) => [...prev, message.payload])
        break

      case 'chat.message':
        setChatMessages((prev) => [...prev, message.payload])
        break

      case 'workflow.progress':
        // Handle workflow progress updates
        break

      default:
        console.log('Unhandled WebSocket message type:', message.type)
    }
  }, [])

  const { isConnected, sendMessage } = useWebSocket(undefined, {
    onMessage: handleMessage,
  })

  const subscribeToAgent = useCallback(
    (agentId: string) => {
      return sendMessage('subscribe', { channel: `agent.${agentId}` })
    },
    [sendMessage]
  )

  const subscribeToTask = useCallback(
    (taskId: string) => {
      return sendMessage('subscribe', { channel: `task.${taskId}` })
    },
    [sendMessage]
  )

  const subscribeToWorkflow = useCallback(
    (workflowId: string) => {
      return sendMessage('subscribe', { channel: `workflow.${workflowId}` })
    },
    [sendMessage]
  )

  const sendChatMessage = useCallback(
    (conversationId: string, message: string) => {
      return sendMessage('chat.send', { conversationId, message })
    },
    [sendMessage]
  )

  return {
    isConnected,
    agentStatus,
    taskUpdates,
    chatMessages,
    subscribeToAgent,
    subscribeToTask,
    subscribeToWorkflow,
    sendChatMessage,
  }
}
