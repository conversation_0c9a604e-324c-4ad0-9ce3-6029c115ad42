import { act, renderHook, waitFor } from '@testing-library/react'
import type * as React from 'react'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import { ToastProvider } from '@/components/ui/toast'
import { mockChatAPI } from '@/utils/mockChatAPI'
import { useChat } from './useChat'

// Mock the mockChatAPI
vi.mock('~/utils/mockChatAPI', () => ({
  mockChatAPI: {
    sendMessage: vi.fn(),
    getChatHistory: vi.fn(),
    saveChatHistory: vi.fn(),
  },
}))

// Mock the logger
vi.mock('~/utils/logger', () => ({
  log: {
    error: vi.fn(),
    userAction: vi.fn(),
  },
}))

// Mock navigator.onLine
Object.defineProperty(navigator, 'onLine', {
  writable: true,
  value: true,
})

const wrapper = ({ children }: { children: React.ReactNode }) => (
  <ToastProvider>{children}</ToastProvider>
)

describe('useChat', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(mockChatAPI.getChatHistory).mockReturnValue([])
    vi.mocked(mockChatAPI.sendMessage).mockResolvedValue({
      content: 'Test response',
      sources: [],
      followUpQuestions: [],
    })
  })

  it('should initialize with empty messages', () => {
    const { result } = renderHook(() => useChat(), { wrapper })

    expect(result.current.messages).toEqual([])
    expect(result.current.isLoading).toBe(false)
    expect(result.current.error).toBe(null)
  })

  it('should load existing messages when chatId is provided', () => {
    const existingMessages = [
      {
        id: '1',
        content: 'Hello',
        role: 'user' as const,
        timestamp: new Date(),
      },
    ]

    vi.mocked(mockChatAPI.getChatHistory).mockReturnValue(existingMessages)

    const { result } = renderHook(() => useChat('chat-1'), { wrapper })

    expect(result.current.messages).toEqual(existingMessages)
    expect(mockChatAPI.getChatHistory).toHaveBeenCalledWith('chat-1')
  })

  it('should send message successfully', async () => {
    const { result } = renderHook(() => useChat(), { wrapper })

    await act(async () => {
      await result.current.sendMessage('Hello')
    })

    await waitFor(() => {
      expect(result.current.messages).toHaveLength(2) // user + assistant message
    })

    expect(result.current.messages[0].content).toBe('Hello')
    expect(result.current.messages[0].role).toBe('user')
    expect(result.current.messages[1].content).toBe('Test response')
    expect(result.current.messages[1].role).toBe('assistant')
  })

  it('should handle loading state during message sending', async () => {
    const { result } = renderHook(() => useChat(), { wrapper })

    // Mock a delayed response
    vi.mocked(mockChatAPI.sendMessage).mockImplementation(
      () =>
        new Promise((resolve) =>
          setTimeout(
            () =>
              resolve({
                content: 'Test response',
                sources: [],
                followUpQuestions: [],
              }),
            100
          )
        )
    )

    act(() => {
      result.current.sendMessage('Hello')
    })

    expect(result.current.isLoading).toBe(true)

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false)
    })
  })

  it('should handle API errors', async () => {
    vi.mocked(mockChatAPI.sendMessage).mockRejectedValue(new Error('API Error'))

    const { result } = renderHook(() => useChat(), { wrapper })

    await act(async () => {
      await result.current.sendMessage('Hello')
    })

    await waitFor(() => {
      expect(result.current.error).toBeTruthy()
    })

    expect(result.current.messages).toHaveLength(0) // Message should be removed on error
  })

  it('should clear error when clearError is called', async () => {
    vi.mocked(mockChatAPI.sendMessage).mockRejectedValue(new Error('API Error'))

    const { result } = renderHook(() => useChat(), { wrapper })

    await act(async () => {
      await result.current.sendMessage('Hello')
    })

    await waitFor(() => {
      expect(result.current.error).toBeTruthy()
    })

    act(() => {
      result.current.clearError()
    })

    expect(result.current.error).toBe(null)
  })

  it('should not send empty messages', async () => {
    const { result } = renderHook(() => useChat(), { wrapper })

    await act(async () => {
      await result.current.sendMessage('')
    })

    expect(result.current.messages).toHaveLength(0)
    expect(mockChatAPI.sendMessage).not.toHaveBeenCalled()
  })

  it('should not send messages when offline', async () => {
    // Mock navigator.onLine
    Object.defineProperty(navigator, 'onLine', {
      writable: true,
      value: false,
    })

    const { result } = renderHook(() => useChat(), { wrapper })

    await act(async () => {
      await result.current.sendMessage('Hello')
    })

    expect(result.current.messages).toHaveLength(0)
    expect(result.current.error).toBeTruthy()
    expect(mockChatAPI.sendMessage).not.toHaveBeenCalled()

    // Restore navigator.onLine
    Object.defineProperty(navigator, 'onLine', {
      writable: true,
      value: true,
    })
  })

  it('should save chat history when chatId is provided', async () => {
    const { result } = renderHook(() => useChat('chat-1'), { wrapper })

    await act(async () => {
      await result.current.sendMessage('Hello')
    })

    await waitFor(() => {
      expect(mockChatAPI.saveChatHistory).toHaveBeenCalledWith(
        'chat-1',
        expect.arrayContaining([
          expect.objectContaining({ content: 'Hello', role: 'user' }),
          expect.objectContaining({
            content: 'Test response',
            role: 'assistant',
          }),
        ])
      )
    })
  })

  it('should handle retry functionality', async () => {
    vi.mocked(mockChatAPI.sendMessage).mockRejectedValueOnce(new Error('Network Error'))

    const { result } = renderHook(() => useChat(), { wrapper })

    // First attempt should fail
    await act(async () => {
      await result.current.sendMessage('Hello')
    })

    await waitFor(() => {
      expect(result.current.error).toBeTruthy()
    })

    // Reset mock to succeed
    vi.mocked(mockChatAPI.sendMessage).mockResolvedValueOnce({
      content: 'Test response',
      sources: [],
      followUpQuestions: [],
    })

    // Retry should work
    await act(async () => {
      await result.current.retryLastMessage()
    })

    await waitFor(() => {
      expect(result.current.messages).toHaveLength(2)
    })
  })
})
