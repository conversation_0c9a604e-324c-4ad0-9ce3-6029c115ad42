import { useNavigate } from '@tanstack/react-router'
import { createContext, type ReactNode, useCallback, useContext, useEffect, useState } from 'react'
import { AuthService } from '@/api/services/authService'

interface User {
  id: string
  email: string
  firstName: string
  lastName: string
  roles: string[]
  permissions: string[]
}

interface LoginCredentials {
  email: string
  password: string
  rememberMe?: boolean
}

interface AuthContextType {
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
  login: (credentials: LoginCredentials) => Promise<void>
  logout: () => Promise<void>
  refreshAuth: () => Promise<void>
  error: string | null
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

interface AuthProviderProps {
  children: ReactNode
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const navigate = useNavigate()

  // Initialize auth state from stored token
  useEffect(() => {
    const initAuth = async () => {
      if (AuthService.isAuthenticated()) {
        try {
          // Try to get current user from the server
          const currentUser = await AuthService.getCurrentUser()
          if (currentUser) {
            setUser(currentUser as User)
          }
        } catch (err) {
          // If getting user fails, try to refresh the token
          try {
            await AuthService.refreshToken()
            const currentUser = await AuthService.getCurrentUser()
            if (currentUser) {
              setUser(currentUser as User)
            }
          } catch (refreshErr) {
            // Refresh failed, user will need to login again
            console.error('Auth initialization failed:', refreshErr)
          }
        }
      }
      setIsLoading(false)
    }

    initAuth()
  }, [])

  const login = useCallback(
    async (credentials: LoginCredentials) => {
      setError(null)
      try {
        const response = await AuthService.login({
          email: credentials.email,
          password: credentials.password,
        })
        setUser(response.user as User)
        navigate('/dashboard')
      } catch (err: unknown) {
        const errorMessage = err instanceof Error ? err.message : 'Login failed'
        setError(errorMessage)
        throw err
      }
    },
    [navigate]
  )

  const logout = useCallback(async () => {
    try {
      await AuthService.logout()
    } catch (err) {
      // Even if logout API fails, clear local state
      console.error('Logout API failed:', err)
    } finally {
      setUser(null)
      navigate('/login')
    }
  }, [navigate])

  const refreshAuth = useCallback(async () => {
    try {
      await AuthService.refreshToken()
      const currentUser = await AuthService.getCurrentUser()
      if (currentUser) {
        setUser(currentUser as User)
      }
    } catch (err) {
      setUser(null)
      throw err
    }
  }, [])

  // Set up automatic token refresh
  useEffect(() => {
    const refreshInterval = setInterval(
      async () => {
        if (user) {
          try {
            await refreshAuth()
          } catch (err) {
            console.error('Token refresh failed:', err)
          }
        }
      },
      10 * 60 * 1000
    ) // Refresh every 10 minutes

    return () => clearInterval(refreshInterval)
  }, [user, refreshAuth])

  const value: AuthContextType = {
    user,
    isAuthenticated: !!user,
    isLoading,
    login,
    logout,
    refreshAuth,
    error,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

// Guard component for protected routes
interface RequireAuthProps {
  children: ReactNode
  requiredRoles?: string[]
  requiredPermissions?: string[]
}

export function RequireAuth({ children, requiredRoles, requiredPermissions }: RequireAuthProps) {
  const { user, isAuthenticated, isLoading } = useAuth()
  const navigate = useNavigate()

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      navigate('/login', { replace: true })
    }
  }, [isAuthenticated, isLoading, navigate])

  if (isLoading) {
    return <div>Loading...</div>
  }

  if (!isAuthenticated || !user) {
    return null
  }

  // Check role requirements
  if (requiredRoles && requiredRoles.length > 0) {
    const hasRequiredRole = requiredRoles.some(role => user.roles?.includes(role))
    if (!hasRequiredRole) {
      return <div>Access Denied: Insufficient role privileges</div>
    }
  }

  // Check permission requirements
  if (requiredPermissions && requiredPermissions.length > 0) {
    const userPermissions = user.permissions || []
    const hasRequiredPermissions = requiredPermissions.every((perm) =>
      userPermissions.includes(perm)
    )
    if (!hasRequiredPermissions) {
      return <div>Access Denied: Insufficient permissions</div>
    }
  }

  return <>{children}</>
}
