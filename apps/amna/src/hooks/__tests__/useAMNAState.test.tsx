import { act, renderHook, waitFor } from '@testing-library/react'
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import { useAMNAState } from '../useAMNAState'

describe('useAMNAState', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  it('should initialize with default state', () => {
    const { result } = renderHook(() => useAMNAState())

    expect(result.current.state).toEqual({
      conversationId: null,
      messages: [],
      isLoading: false,
      error: null,
      isConnected: false,
      connectionStatus: 'disconnected',
    })
  })

  it('should connect automatically', async () => {
    const { result } = renderHook(() => useAMNAState())

    expect(result.current.state.connectionStatus).toBe('disconnected')

    await waitFor(() => {
      expect(result.current.state.isConnected).toBe(true)
      expect(result.current.state.connectionStatus).toBe('connected')
    })
  })

  it('should send a message', async () => {
    const { result } = renderHook(() => useAMNAState())

    // Wait for connection
    await waitFor(() => {
      expect(result.current.state.isConnected).toBe(true)
    })

    await act(async () => {
      await result.current.actions.sendMessage('Hello')
    })

    expect(result.current.state.messages).toHaveLength(2)
    expect(result.current.state.messages[0].content).toBe('Hello')
    expect(result.current.state.messages[0].role).toBe('user')
    expect(result.current.state.messages[1].role).toBe('assistant')
  })

  it('should start a conversation', async () => {
    const { result } = renderHook(() => useAMNAState())

    await act(async () => {
      await result.current.actions.startConversation('test-conv-id')
    })

    expect(result.current.state.conversationId).toBe('test-conv-id')
    expect(result.current.state.messages).toHaveLength(0)
  })

  it('should end a conversation', async () => {
    const { result } = renderHook(() => useAMNAState())

    // Start a conversation first
    await act(async () => {
      await result.current.actions.startConversation('test-conv-id')
    })

    expect(result.current.state.conversationId).toBe('test-conv-id')

    // End the conversation
    await act(async () => {
      await result.current.actions.endConversation()
    })

    expect(result.current.state.conversationId).toBeNull()
    expect(result.current.state.messages).toHaveLength(0)
  })

  it('should clear messages', async () => {
    const { result } = renderHook(() => useAMNAState())

    // Wait for connection and send a message
    await waitFor(() => {
      expect(result.current.state.isConnected).toBe(true)
    })

    await act(async () => {
      await result.current.actions.sendMessage('Hello')
    })

    expect(result.current.state.messages.length).toBeGreaterThan(0)

    // Clear messages
    act(() => {
      result.current.actions.clearMessages()
    })

    expect(result.current.state.messages).toHaveLength(0)
  })

  it('should handle send message error when not connected', async () => {
    const { result } = renderHook(() => useAMNAState())

    // Try to send message before connection
    await act(async () => {
      await result.current.actions.sendMessage('Hello')
    })

    expect(result.current.state.error).toBe('Not connected')
  })
})
