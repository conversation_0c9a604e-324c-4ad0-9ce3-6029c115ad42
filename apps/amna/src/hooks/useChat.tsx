import * as React from 'react'
import type { Message, Reaction, Thread } from '@/components/chat/ChatMessages'
import { useToast } from '@/components/ui/toast'
import { log } from '@/utils/logger'
import { mockChatAPI } from '@/utils/mockChatAPI'

interface UseChatReturn {
  messages: Message[]
  threads: Thread[]
  sendMessage: (content: string) => Promise<void>
  addReaction: (messageId: string, emoji: string) => void
  removeReaction: (messageId: string, reactionId: string) => void
  replyToMessage: (messageId: string) => void
  replyToThread: (threadId: string, message: string) => void
  toggleThread: (threadId: string) => void
  scrollToMessage: (messageId: string) => void
  editMessage: (messageId: string, newContent: string) => void
  deleteMessage: (messageId: string) => void
  lastMessage: Message | null
  isLoading: boolean
  error: string | null
  retryLastMessage: () => Promise<void>
  clearError: () => void
  isRetrying: boolean
}

export function useChat(chatId?: string): UseChatReturn {
  const [messages, setMessages] = React.useState<Message[]>([])
  const [threads, setThreads] = React.useState<Thread[]>([])
  const [isLoading, setIsLoading] = React.useState(false)
  const [isRetrying, setIsRetrying] = React.useState(false)
  const [error, setError] = React.useState<string | null>(null)
  const [lastFailedMessage, setLastFailedMessage] = React.useState<string | null>(null)
  const { addToast } = useToast()

  // Network status detection
  const [isOnline, setIsOnline] = React.useState(navigator.onLine)

  React.useEffect(() => {
    const handleOnline = () => setIsOnline(true)
    const handleOffline = () => setIsOnline(false)

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [])

  // Load existing chat messages if chatId is provided
  React.useEffect(() => {
    if (chatId) {
      // TODO: Load messages from storage/API
      const savedMessages = mockChatAPI.getChatHistory(chatId)
      setMessages(savedMessages)
    }
  }, [chatId])

  // Clear error function
  const clearError = React.useCallback(() => {
    setError(null)
  }, [])

  // Determine error type and message
  const getErrorInfo = React.useCallback(
    (error: unknown) => {
      if (!isOnline) {
        return {
          type: 'network',
          message: 'You appear to be offline. Please check your internet connection and try again.',
          retry: true,
        }
      }

      if (error instanceof Error) {
        if (error.message.includes('timeout') || error.message.includes('network')) {
          return {
            type: 'network',
            message: 'Network timeout. Please try again.',
            retry: true,
          }
        }

        if (error.message.includes('rate limit')) {
          return {
            type: 'rate_limit',
            message: 'Too many requests. Please wait a moment and try again.',
            retry: true,
          }
        }

        if (error.message.includes('server') || error.message.includes('500')) {
          return {
            type: 'server',
            message: 'Server error. Please try again in a moment.',
            retry: true,
          }
        }

        return {
          type: 'unknown',
          message: error.message,
          retry: false,
        }
      }

      return {
        type: 'unknown',
        message: 'An unexpected error occurred. Please try again.',
        retry: true,
      }
    },
    [isOnline]
  )

  // Retry logic with exponential backoff
  const retryWithBackoff = React.useCallback(
    async (fn: () => Promise<void>, retries: number = 3, baseDelay: number = 1000) => {
      for (let i = 0; i < retries; i++) {
        try {
          await fn()
          return
        } catch (error) {
          if (i === retries - 1) {
            throw error
          }

          const delay = baseDelay * 2 ** i + Math.random() * 1000
          await new Promise((resolve) => setTimeout(resolve, delay))
        }
      }
    },
    []
  )

  const sendMessage = React.useCallback(
    async (content: string) => {
      if (!content.trim()) {
        return
      }

      // Check network status
      if (!isOnline) {
        const errorInfo = getErrorInfo(new Error('offline'))
        setError(errorInfo.message)
        addToast({
          type: 'error',
          title: 'Network Error',
          description: errorInfo.message,
          duration: 5000,
        })
        return
      }

      const userMessage: Message = {
        id: Date.now().toString(),
        content: content.trim(),
        role: 'user',
        timestamp: new Date(),
      }

      setMessages((prev) => [...prev, userMessage])
      setIsLoading(true)
      setError(null)
      setLastFailedMessage(content.trim())

      const sendApiMessage = async () => {
        // Add timeout wrapper
        const timeoutPromise = new Promise<never>((_, reject) => {
          setTimeout(() => reject(new Error('Request timeout')), 30000)
        })

        const apiPromise = mockChatAPI.sendMessage(content)

        const response = await Promise.race([apiPromise, timeoutPromise])

        const assistantMessage: Message = {
          id: (Date.now() + 1).toString(),
          content: response.content,
          role: 'assistant',
          timestamp: new Date(),
          sources: response.sources,
          followUpQuestions: response.followUpQuestions,
        }

        setMessages((prev) => [...prev, assistantMessage])

        // Save to chat history if we have a chatId
        if (chatId) {
          mockChatAPI.saveChatHistory(chatId, [userMessage, assistantMessage])
        }

        // Clear last failed message on success
        setLastFailedMessage(null)
      }

      try {
        await retryWithBackoff(sendApiMessage, 2, 1000)
      } catch (err) {
        const errorInfo = getErrorInfo(err)

        log.error('Error sending message', {
          error: err instanceof Error ? err.message : 'Unknown error',
          chatId,
          messageLength: content.length,
          errorType: errorInfo.type,
          canRetry: errorInfo.retry,
        })

        setError(errorInfo.message)

        // Remove the user message if it failed completely
        setMessages((prev) => prev.filter((msg) => msg.id !== userMessage.id))

        addToast({
          type: 'error',
          title: 'Message Failed',
          description: errorInfo.retry
            ? `${errorInfo.message} Use the retry button to try again.`
            : errorInfo.message,
          duration: errorInfo.retry ? 7000 : 5000,
        })
      } finally {
        setIsLoading(false)
      }
    },
    [chatId, isOnline, getErrorInfo, retryWithBackoff, addToast]
  )

  // Retry last failed message
  const retryLastMessage = React.useCallback(async () => {
    if (!lastFailedMessage) {
      return
    }

    setIsRetrying(true)
    setError(null)

    try {
      await sendMessage(lastFailedMessage)
    } catch {
      // Error is already handled in sendMessage
    } finally {
      setIsRetrying(false)
    }
  }, [lastFailedMessage, sendMessage])

  // Add reaction to message
  const addReaction = React.useCallback((messageId: string, emoji: string) => {
    const newReaction: Reaction = {
      id: Date.now().toString(),
      emoji,
      userId: 'current-user', // Mock current user
      username: 'You',
      timestamp: new Date(),
    }

    setMessages((prev) =>
      prev.map((message) =>
        message.id === messageId
          ? { ...message, reactions: [...(message.reactions || []), newReaction] }
          : message
      )
    )

    log.userAction('reaction-added', messageId, { emoji })
  }, [])

  // Remove reaction from message
  const removeReaction = React.useCallback((messageId: string, reactionId: string) => {
    setMessages((prev) =>
      prev.map((message) =>
        message.id === messageId
          ? { ...message, reactions: message.reactions?.filter((r) => r.id !== reactionId) }
          : message
      )
    )

    log.userAction('reaction-removed', messageId, { reactionId })
  }, [])

  // Reply to message (create a new thread)
  const replyToMessage = React.useCallback(
    (messageId: string) => {
      // Create a new thread if one doesn't exist
      const existingThread = threads.find((thread) => thread.parentMessageId === messageId)

      if (!existingThread) {
        const newThread: Thread = {
          id: `thread-${Date.now()}`,
          parentMessageId: messageId,
          messages: [],
          isExpanded: true,
        }

        setThreads((prev) => [...prev, newThread])

        // Update the parent message to indicate it has replies
        setMessages((prev) =>
          prev.map((message) =>
            message.id === messageId
              ? { ...message, threadId: newThread.id, replyCount: 0 }
              : message
          )
        )
      } else {
        // Just expand the existing thread
        setThreads((prev) =>
          prev.map((thread) =>
            thread.id === existingThread.id ? { ...thread, isExpanded: true } : thread
          )
        )
      }

      log.userAction('reply-to-message', messageId)
    },
    [threads]
  )

  // Reply to thread
  const replyToThread = React.useCallback(
    (threadId: string, messageContent: string) => {
      const newThreadMessage: Message = {
        id: `thread-msg-${Date.now()}`,
        content: messageContent,
        role: 'user',
        timestamp: new Date(),
        parentMessageId: threadId,
      }

      // Add message to thread
      setThreads((prev) =>
        prev.map((thread) =>
          thread.id === threadId
            ? { ...thread, messages: [...thread.messages, newThreadMessage] }
            : thread
        )
      )

      // Update reply count on parent message
      const thread = threads.find((t) => t.id === threadId)
      if (thread) {
        setMessages((prev) =>
          prev.map((message) =>
            message.id === thread.parentMessageId
              ? { ...message, replyCount: (message.replyCount || 0) + 1 }
              : message
          )
        )
      }

      // Generate mock assistant response to thread
      setTimeout(() => {
        const assistantResponse: Message = {
          id: `thread-msg-${Date.now() + 1}`,
          content: `Thanks for the follow-up! Let me address your point about "${messageContent.slice(0, 50)}${messageContent.length > 50 ? '...' : ''}".`,
          role: 'assistant',
          timestamp: new Date(),
          parentMessageId: threadId,
        }

        setThreads((prev) =>
          prev.map((thread) =>
            thread.id === threadId
              ? { ...thread, messages: [...thread.messages, assistantResponse] }
              : thread
          )
        )

        if (thread) {
          setMessages((prev) =>
            prev.map((message) =>
              message.id === thread.parentMessageId
                ? { ...message, replyCount: (message.replyCount || 0) + 1 }
                : message
            )
          )
        }
      }, 1000)

      log.userAction('reply-to-thread', threadId, { messageLength: messageContent.length })
    },
    [threads]
  )

  // Toggle thread expansion
  const toggleThread = React.useCallback((threadId: string) => {
    setThreads((prev) =>
      prev.map((thread) =>
        thread.id === threadId ? { ...thread, isExpanded: !thread.isExpanded } : thread
      )
    )

    log.userAction('toggle-thread', threadId)
  }, [])

  // Scroll to specific message
  const scrollToMessage = React.useCallback((messageId: string) => {
    const messageElement = document.querySelector(`[data-message-id="${messageId}"]`)
    if (messageElement) {
      messageElement.scrollIntoView({
        behavior: 'smooth',
        block: 'center',
      })

      // Add highlight effect
      messageElement.classList.add('highlight-message')
      setTimeout(() => {
        messageElement.classList.remove('highlight-message')
      }, 2000)
    }

    log.userAction('scroll-to-message', messageId)
  }, [])

  // Edit message
  const editMessage = React.useCallback(
    (messageId: string, newContent: string) => {
      setMessages((prev) =>
        prev.map((message) =>
          message.id === messageId ? { ...message, content: newContent, isEdited: true } : message
        )
      )

      addToast({
        type: 'success',
        title: 'Message edited',
        description: 'Your message has been updated.',
        duration: 3000,
      })

      log.userAction('message-edited', messageId, { contentLength: newContent.length })
    },
    [addToast]
  )

  // Delete message (soft delete)
  const deleteMessage = React.useCallback(
    (messageId: string) => {
      setMessages((prev) =>
        prev.map((message) =>
          message.id === messageId
            ? { ...message, isDeleted: true, content: '[Message deleted]' }
            : message
        )
      )

      addToast({
        type: 'success',
        title: 'Message deleted',
        description: 'Your message has been removed.',
        duration: 3000,
      })

      log.userAction('message-deleted', messageId)
    },
    [addToast]
  )

  // Get the last assistant message for context
  const lastMessage = React.useMemo(() => {
    const assistantMessages = messages.filter((m) => m.role === 'assistant')
    return assistantMessages.length > 0 ? assistantMessages[assistantMessages.length - 1] : null
  }, [messages])

  return {
    messages,
    threads,
    sendMessage,
    addReaction,
    removeReaction,
    replyToMessage,
    replyToThread,
    toggleThread,
    scrollToMessage,
    editMessage,
    deleteMessage,
    lastMessage,
    isLoading,
    error,
    retryLastMessage,
    clearError,
    isRetrying,
  }
}
