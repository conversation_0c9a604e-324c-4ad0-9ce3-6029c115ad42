import { <PERSON><PERSON>, <PERSON>ader2, Send, User } from 'lucide-react'
import { useEffect, useRef, useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { useApi } from '@/hooks/useApi'
import { useAmnaWebSocket } from '@/hooks/useWebSocket'
import { AmnaService, type ChatMessage, type ChatRequest } from '@/services/api/amna.service'

export function AmnaChatInterface() {
  const [messages, setMessages] = useState<ChatMessage[]>([])
  const [inputMessage, setInputMessage] = useState('')
  const [selectedAgent, setSelectedAgent] = useState<string>('general')
  const [isTyping, setIsTyping] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  // WebSocket for real-time updates
  const { isConnected, chatMessages, sendChatMessage } = useAmnaWebSocket()

  // API hooks
  const { data: agents, execute: fetchAgents } = useApi(AmnaService.listAgents)
  const { execute: sendMessage, isLoading: isSending } = useApi(AmnaService.chat)
  const { data: history, execute: fetchHistory } = useApi(() => AmnaService.getChatHistory())

  // Load agents and history on mount
  useEffect(() => {
    fetchAgents()
    fetchHistory()
  }, [])

  // Update messages when history loads
  useEffect(() => {
    if (history) {
      setMessages(history)
    }
  }, [history])

  // Handle real-time messages from WebSocket
  useEffect(() => {
    if (chatMessages.length > 0) {
      const latestMessage = chatMessages[chatMessages.length - 1]
      setMessages((prev) => [...prev, latestMessage])
      setIsTyping(false)
    }
  }, [chatMessages])

  // Auto-scroll to bottom
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages])

  const handleSendMessage = async () => {
    if (!inputMessage.trim()) return

    const userMessage: ChatMessage = {
      role: 'user',
      content: inputMessage,
      timestamp: new Date().toISOString(),
    }

    // Add user message to UI
    setMessages((prev) => [...prev, userMessage])
    setInputMessage('')
    setIsTyping(true)

    try {
      // Send via WebSocket if connected, otherwise use HTTP
      if (isConnected) {
        sendChatMessage('current', inputMessage)
      } else {
        const request: ChatRequest = {
          messages: [...messages, userMessage],
          agentId: selectedAgent,
          stream: false,
        }

        const response = await sendMessage(request)

        const assistantMessage: ChatMessage = {
          role: 'assistant',
          content: response.response,
          timestamp: new Date().toISOString(),
          metadata: response.metadata,
        }

        setMessages((prev) => [...prev, assistantMessage])
      }
    } catch (error) {
      console.error('Failed to send message:', error)
      const errorMessage: ChatMessage = {
        role: 'system',
        content: 'Sorry, I encountered an error processing your message. Please try again.',
        timestamp: new Date().toISOString(),
      }
      setMessages((prev) => [...prev, errorMessage])
    } finally {
      setIsTyping(false)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  return (
    <Card className="flex flex-col h-[600px] w-full max-w-4xl mx-auto">
      {/* Header */}
      <div className="p-4 border-b bg-gray-50">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Bot className="w-6 h-6 text-blue-600" />
            <h2 className="text-lg font-semibold">AMNA Assistant</h2>
            {isConnected && (
              <span className="inline-flex items-center px-2 py-1 text-xs font-medium text-green-700 bg-green-100 rounded-full">
                Live
              </span>
            )}
          </div>

          {agents && agents.length > 0 && (
            <select
              value={selectedAgent}
              onChange={(e) => setSelectedAgent(e.target.value)}
              className="px-3 py-1 text-sm border rounded-md"
            >
              <option value="general">General Assistant</option>
              {agents.map((agent) => (
                <option key={agent.id} value={agent.id}>
                  {agent.name}
                </option>
              ))}
            </select>
          )}
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.length === 0 ? (
          <div className="text-center text-gray-500 mt-8">
            <Bot className="w-12 h-12 mx-auto mb-4 text-gray-300" />
            <p>Start a conversation with AMNA</p>
            <p className="text-sm mt-2">
              I can help with training, vendors, weekly wins, and more!
            </p>
          </div>
        ) : (
          messages.map((message, index) => (
            <div
              key={index}
              className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <div
                className={`flex max-w-[70%] ${message.role === 'user' ? 'flex-row-reverse' : 'flex-row'}`}
              >
                <div className={`flex-shrink-0 ${message.role === 'user' ? 'ml-3' : 'mr-3'}`}>
                  {message.role === 'user' ? (
                    <User className="w-8 h-8 p-1 bg-blue-600 text-white rounded-full" />
                  ) : (
                    <Bot className="w-8 h-8 p-1 bg-gray-600 text-white rounded-full" />
                  )}
                </div>
                <div
                  className={`px-4 py-2 rounded-lg ${
                    message.role === 'user'
                      ? 'bg-blue-600 text-white'
                      : message.role === 'system'
                        ? 'bg-red-100 text-red-800'
                        : 'bg-gray-100 text-gray-800'
                  }`}
                >
                  <p className="whitespace-pre-wrap">{message.content}</p>
                  {message.timestamp && (
                    <p
                      className={`text-xs mt-1 ${
                        message.role === 'user' ? 'text-blue-100' : 'text-gray-500'
                      }`}
                    >
                      {new Date(message.timestamp).toLocaleTimeString()}
                    </p>
                  )}
                </div>
              </div>
            </div>
          ))
        )}

        {isTyping && (
          <div className="flex justify-start">
            <div className="flex items-center space-x-2 px-4 py-2 bg-gray-100 rounded-lg">
              <Loader2 className="w-4 h-4 animate-spin" />
              <span className="text-sm text-gray-600">AMNA is thinking...</span>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <div className="p-4 border-t bg-gray-50">
        <div className="flex space-x-2">
          <textarea
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Type your message..."
            className="flex-1 px-4 py-2 border rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500"
            rows={1}
            disabled={isSending}
          />
          <Button
            onClick={handleSendMessage}
            disabled={isSending || !inputMessage.trim()}
            className="px-4"
          >
            {isSending ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              <Send className="w-4 h-4" />
            )}
          </Button>
        </div>

        <div className="mt-2 flex flex-wrap gap-2">
          <button
            onClick={() => setInputMessage('What training programs are available?')}
            className="text-xs px-2 py-1 bg-gray-200 rounded hover:bg-gray-300"
          >
            Available Training
          </button>
          <button
            onClick={() => setInputMessage('Show me my skill gaps')}
            className="text-xs px-2 py-1 bg-gray-200 rounded hover:bg-gray-300"
          >
            Skill Gaps
          </button>
          <button
            onClick={() => setInputMessage('Help me with my weekly wins submission')}
            className="text-xs px-2 py-1 bg-gray-200 rounded hover:bg-gray-300"
          >
            Weekly Wins
          </button>
          <button
            onClick={() => setInputMessage('Find vendors for technical training')}
            className="text-xs px-2 py-1 bg-gray-200 rounded hover:bg-gray-300"
          >
            Find Vendors
          </button>
        </div>
      </div>
    </Card>
  )
}
