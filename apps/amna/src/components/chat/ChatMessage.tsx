import { Bo<PERSON>, Check, Crown, ExternalLink, MessageSquare, User, X } from 'lucide-react'
import * as React from 'react'
import { But<PERSON> } from '@/components/ui/button'
import {
  FloatingElement,
  motion,
  PopButton,
  StaggerContainer,
  StaggerItem,
  spring,
  useInView,
  variants,
} from '@/lib/animations'
import { cn } from '@/lib/utils'
import { log } from '@/utils/logger'
import type { Message, Reaction, Thread } from './ChatMessages'
import { LinkPreviews } from './LinkPreview'
import { MessageActionsMenu } from './MessageActionsMenu'
import { MessageReactions } from './MessageReactions'
import { MessageThread } from './MessageThread'
import { RichMediaRenderer } from './RichMediaRenderer'
import { SourceCard } from './SourceCard'

interface ChatMessageProps {
  message: Message
  className?: string
  index?: number
  thread?: Thread
  onAddReaction?: (messageId: string, emoji: string) => void
  onRemoveReaction?: (messageId: string, reactionId: string) => void
  onReply?: (messageId: string) => void
  onReplyToThread?: (threadId: string, message: string) => void
  onToggleThread?: (threadId: string) => void
  onEdit?: (messageId: string, newContent: string) => void
  onDelete?: (messageId: string) => void
}

export const ChatMessage = React.memo(function ChatMessage({
  message,
  className,
  index = 0,
  thread,
  onAddReaction,
  onRemoveReaction,
  onReply,
  onReplyToThread,
  onToggleThread,
  onEdit,
  onDelete,
}: ChatMessageProps) {
  const isUser = message.role === 'user'
  const isAssistant = message.role === 'assistant'
  const ref = React.useRef(null)
  const isInView = useInView(ref, { once: true, margin: '-10px' })
  const [isEditing, setIsEditing] = React.useState(false)
  const [editContent, setEditContent] = React.useState(message.content)
  const editInputRef = React.useRef<HTMLTextAreaElement>(null)

  const handleFollowUpClick = React.useCallback(
    (question: string) => {
      log.userAction('follow-up-question-clicked', message.id, { question })
      // TODO: Handle follow-up question click
    },
    [message.id]
  )

  const handleEditStart = React.useCallback(() => {
    setIsEditing(true)
    setEditContent(message.content)
    // Focus input after state update
    setTimeout(() => editInputRef.current?.focus(), 100)
  }, [message.content])

  const handleEditSave = React.useCallback(() => {
    if (editContent.trim() && editContent !== message.content) {
      onEdit?.(message.id, editContent.trim())
    }
    setIsEditing(false)
  }, [editContent, message.content, message.id, onEdit])

  const handleEditCancel = React.useCallback(() => {
    setEditContent(message.content)
    setIsEditing(false)
  }, [message.content])

  const handleEditKeyDown = React.useCallback(
    (e: React.KeyboardEvent) => {
      if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault()
        handleEditSave()
      } else if (e.key === 'Escape') {
        handleEditCancel()
      }
    },
    [handleEditSave, handleEditCancel]
  )

  // Custom animation variants for messages
  const messageVariants = {
    hidden: {
      opacity: 0,
      y: 30,
      scale: 0.95,
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        ...spring.gentle,
        delay: index * 0.1,
      },
    },
  }

  return (
    <motion.div
      ref={ref}
      data-message-id={message.id}
      data-role={message.role}
      className={cn(
        'flex gap-4 transition-all duration-500',
        // Reduced padding for AI messages
        message.role === 'assistant' ? 'p-3' : 'p-4',
        isUser && 'flex-row-reverse',
        className
      )}
      variants={messageVariants}
      initial="hidden"
      animate={isInView ? 'visible' : 'hidden'}
      whileHover={{ scale: 1.01 }}
      layout
    >
      {/* Animated Avatar */}
      <motion.div
        className="flex-shrink-0"
        initial={{ scale: 0, rotate: -180 }}
        animate={{ scale: 1, rotate: 0 }}
        transition={{ ...spring.bouncy, delay: index * 0.1 + 0.2 }}
      >
        <motion.div
          className={cn(
            'w-8 h-8 rounded-full flex items-center justify-center',
            isUser ? 'bg-primary text-primary-foreground' : 'bg-secondary text-secondary-foreground'
          )}
          whileHover={{
            scale: 1.2,
            rotate: isUser ? 15 : -15,
            transition: spring.snappy,
          }}
          whileTap={{ scale: 0.9 }}
        >
          <motion.div
            animate={
              isAssistant
                ? {
                    scale: [1, 1.015, 1],
                    rotate: [0, 2, -2, 0],
                  }
                : {}
            }
            transition={
              isAssistant
                ? {
                    scale: {
                      duration: 3,
                      repeat: Infinity,
                      ease: [0.37, 0, 0.63, 1], // breathe easing
                    },
                    rotate: {
                      duration: 6,
                      repeat: Infinity,
                      ease: [0.645, 0.045, 0.355, 1], // float easing
                    },
                  }
                : {}
            }
          >
            {isUser ? <User className="h-4 w-4" /> : <Bot className="h-4 w-4" />}
          </motion.div>
        </motion.div>
      </motion.div>

      {/* Animated Message Content */}
      <motion.div
        className="flex-1 space-y-4"
        initial={{ opacity: 0, x: isUser ? 20 : -20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ ...spring.standard, delay: index * 0.1 + 0.3 }}
      >
        <motion.div
          className={cn(
            'rounded-lg shadow-sm relative group',
            // Reduced padding for AI messages
            message.role === 'assistant' ? 'px-3 py-2' : 'px-4 py-3',
            // Adaptive width based on content type
            message.messageType === 'code' ||
              message.messageType === 'file' ||
              message.messageType === 'table'
              ? 'max-w-[95%] w-full'
              : message.messageType === 'image'
                ? 'max-w-[90%]'
                : message.messageType === 'structured' || message.messageType === 'list'
                  ? 'max-w-[85%]'
                  : 'max-w-[80%]',
            message.role === 'user' ? 'bg-primary text-primary-foreground ml-auto' : 'bg-muted'
          )}
          whileHover={{
            scale: 1.005,
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.08)',
            transition: spring.flutter,
          }}
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ ...spring.gentle, delay: index * 0.1 + 0.4 }}
        >
          {/* AI Personality Badge (for assistant messages) */}
          {isAssistant && message.metadata?.personality && (
            <motion.div
              className="flex items-center gap-2 mb-2 p-1.5 bg-primary/5 rounded-md border border-primary/20"
              initial={{ opacity: 0, y: -10, scale: 0.9 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              transition={{ ...spring.bouncy, delay: index * 0.1 + 0.3 }}
            >
              <motion.div
                animate={{ rotate: [0, 10, -10, 0] }}
                transition={{ duration: 2, repeat: Infinity, ease: 'easeInOut' }}
              >
                <Crown className="h-3 w-3 text-primary" />
              </motion.div>
              <div className="text-xs">
                <span className="font-medium text-primary">
                  {message.metadata.personality.name}
                </span>
                <span className="text-muted-foreground mx-1">•</span>
                <span className="text-xs text-muted-foreground">
                  {message.metadata.personality.role}
                </span>
              </div>
            </motion.div>
          )}

          {/* Message Actions Menu */}
          {isUser && (onEdit || onDelete) && !message.isDeleted && (
            <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
              <MessageActionsMenu
                message={message}
                onEdit={handleEditStart}
                onDelete={onDelete}
                onReply={onReply}
              />
            </div>
          )}

          {/* Enhanced Rich Media Message Content */}
          {message.isDeleted ? (
            <motion.div
              className="text-muted-foreground italic text-sm"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={spring.gentle}
            >
              {message.content}
            </motion.div>
          ) : isEditing ? (
            <motion.div
              className="space-y-2"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={spring.gentle}
            >
              <textarea
                ref={editInputRef}
                value={editContent}
                onChange={(e) => setEditContent(e.target.value)}
                onKeyDown={handleEditKeyDown}
                className="w-full px-3 py-2 bg-background border border-border rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-primary/50 text-sm"
                rows={Math.min(editContent.split('\n').length + 1, 10)}
              />
              <div className="flex gap-2">
                <PopButton
                  className="px-3 py-1.5 bg-primary text-primary-foreground rounded-md text-sm hover:bg-primary/90"
                  onClick={handleEditSave}
                >
                  <Check className="h-3 w-3 mr-1" />
                  Save
                </PopButton>
                <PopButton
                  className="px-3 py-1.5 bg-muted text-foreground rounded-md text-sm hover:bg-muted/80"
                  onClick={handleEditCancel}
                >
                  <X className="h-3 w-3 mr-1" />
                  Cancel
                </PopButton>
              </div>
            </motion.div>
          ) : (
            <motion.div
              className={cn(
                'prose max-w-none',
                // Smaller text for AI messages
                message.role === 'assistant' ? 'prose-sm text-sm' : 'prose-sm',
                // Enhanced readability for different content types
                message.messageType === 'code' && 'overflow-x-auto',
                message.messageType === 'table' && 'overflow-x-auto',
                message.messageType === 'structured' && 'space-y-2',
                message.messageType === 'list' && 'space-y-1'
              )}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: index * 0.1 + 0.5, duration: 0.5 }}
            >
              <RichMediaRenderer
                content={message.content}
                messageType={message.messageType}
                metadata={message.metadata}
              />
              {message.isEdited && (
                <span className="text-xs text-muted-foreground ml-2">(edited)</span>
              )}
            </motion.div>
          )}

          {/* Link Previews */}
          {!isEditing && !message.isDeleted && <LinkPreviews content={message.content} />}

          {/* Animated Sources (only for assistant messages) */}
          {isAssistant && message.sources && message.sources.length > 0 && (
            <motion.div
              className="space-y-3"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ ...spring.gentle, delay: index * 0.1 + 0.7 }}
            >
              <motion.h4
                className="text-sm font-medium text-muted-foreground flex items-center gap-2"
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 + 0.8 }}
              >
                <motion.div
                  animate={{ rotate: [0, 15, 0] }}
                  transition={{ duration: 2, repeat: Infinity, ease: 'easeInOut' }}
                >
                  <ExternalLink className="h-3 w-3" />
                </motion.div>
                Sources
              </motion.h4>
              <StaggerContainer
                className="grid grid-cols-1 md:grid-cols-2 gap-3"
                staggerDelay={0.1}
              >
                {message.sources.map((source, sourceIndex) => (
                  <StaggerItem key={source.id}>
                    <motion.div whileHover={{ scale: 1.02, y: -2 }} whileTap={{ scale: 0.98 }}>
                      <SourceCard source={source} />
                    </motion.div>
                  </StaggerItem>
                ))}
              </StaggerContainer>
            </motion.div>
          )}

          {/* Animated Follow-up Questions (only for assistant messages) */}
          {isAssistant && message.followUpQuestions && message.followUpQuestions.length > 0 && (
            <motion.div
              className="space-y-3"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ ...spring.gentle, delay: index * 0.1 + 0.9 }}
            >
              <motion.h4
                className="text-xs font-medium text-muted-foreground flex items-center gap-2"
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 + 1.0 }}
              >
                <motion.div
                  animate={{
                    scale: [1, 1.2, 1],
                    rotate: [0, 10, 0],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: 'easeInOut',
                  }}
                >
                  <MessageSquare className="h-3 w-3" />
                </motion.div>
                Follow-up questions
              </motion.h4>
              <StaggerContainer className="flex flex-wrap gap-2" staggerDelay={0.08}>
                {message.followUpQuestions.map((question, questionIndex) => (
                  <StaggerItem key={questionIndex}>
                    <PopButton
                      className="text-xs h-auto py-1.5 px-2.5 rounded-full border border-border bg-background hover:bg-accent transition-colors"
                      onClick={() => handleFollowUpClick(question)}
                    >
                      {question}
                    </PopButton>
                  </StaggerItem>
                ))}
              </StaggerContainer>
            </motion.div>
          )}

          {/* Animated Timestamp */}
          <motion.div
            className="text-xs text-muted-foreground mt-2"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: index * 0.1 + 1.2, duration: 0.3 }}
          >
            {message.timestamp.toLocaleTimeString([], {
              hour: '2-digit',
              minute: '2-digit',
            })}
            {message.metadata?.personality && <span className="mx-2 text-primary/60">•</span>}
            {message.metadata?.personality && (
              <span className="text-primary/60 text-xs">
                via {message.metadata.personality.name}
              </span>
            )}
          </motion.div>

          {/* Message Reactions */}
          <MessageReactions
            reactions={message.reactions}
            messageId={message.id}
            onAddReaction={onAddReaction}
            onRemoveReaction={onRemoveReaction}
            onReply={onReply}
          />
        </motion.div>
      </motion.div>

      {/* Message Thread */}
      {thread && (
        <MessageThread
          thread={thread}
          onToggleExpanded={onToggleThread}
          onReplyToThread={onReplyToThread}
        />
      )}
    </motion.div>
  )
})
