import * as React from 'react'
import {
  AnimatePresence,
  motion,
  StaggerContainer,
  StaggerItem,
  useScroll,
  useTransform,
} from '@/lib/animations'
import { cn } from '@/lib/utils'
import { ChatMessage } from './ChatMessage'
import { TypingIndicator } from './TypingIndicator'

export interface Reaction {
  id: string
  emoji: string
  userId: string
  username: string
  timestamp: Date
}

export interface Thread {
  id: string
  parentMessageId: string
  messages: Message[]
  isExpanded?: boolean
}

export interface Message {
  id: string
  content: string
  role: 'user' | 'assistant'
  timestamp: Date
  sources?: Source[]
  followUpQuestions?: string[]
  messageType?: 'text' | 'code' | 'file' | 'image' | 'table' | 'list' | 'structured'
  reactions?: Reaction[]
  threadId?: string
  parentMessageId?: string
  replyCount?: number
  isEdited?: boolean
  isDeleted?: boolean
  metadata?: {
    language?: string
    fileName?: string
    fileSize?: string
    fileType?: string
    imageUrl?: string
    imageAlt?: string
    personality?: {
      id: string
      name: string
      role: string
      style: string
    }
  }
}

export interface Source {
  id: string
  title: string
  url: string
  snippet: string
  favicon?: string
}

interface ChatMessagesProps {
  messages: Message[]
  isLoading?: boolean
  className?: string
  threads?: Thread[]
  onAddReaction?: (messageId: string, emoji: string) => void
  onRemoveReaction?: (messageId: string, reactionId: string) => void
  onReply?: (messageId: string) => void
  onReplyToThread?: (threadId: string, message: string) => void
  onToggleThread?: (threadId: string) => void
  onEdit?: (messageId: string, newContent: string) => void
  onDelete?: (messageId: string) => void
}

export const ChatMessages = React.memo(function ChatMessages({
  messages,
  isLoading,
  className,
  threads = [],
  onAddReaction,
  onRemoveReaction,
  onReply,
  onReplyToThread,
  onToggleThread,
  onEdit,
  onDelete,
}: ChatMessagesProps) {
  const messagesEndRef = React.useRef<HTMLDivElement>(null)
  const containerRef = React.useRef<HTMLDivElement>(null)

  // Scroll progress for subtle animations
  const { scrollYProgress } = useScroll({ container: containerRef })
  const backgroundOpacity = useTransform(scrollYProgress, [0, 0.5], [0, 0.02])

  // Find thread for each message
  const getThreadForMessage = React.useCallback(
    (messageId: string) => {
      return threads.find((thread) => thread.parentMessageId === messageId)
    },
    [threads]
  )

  // Auto-scroll to bottom when new messages arrive with enhanced smooth scrolling
  React.useEffect(() => {
    const scrollToBottom = () => {
      if (messagesEndRef.current) {
        messagesEndRef.current.scrollIntoView({
          behavior: 'smooth',
          block: 'end',
          inline: 'nearest',
        })
      }
    }

    // Delay scroll to allow for message animations to complete
    const timeoutId = setTimeout(scrollToBottom, 300)
    return () => clearTimeout(timeoutId)
  }, [messages, isLoading])

  if (messages.length === 0 && !isLoading) {
    return null
  }

  return (
    <motion.div
      ref={containerRef}
      className={cn('space-y-6 p-4 relative overflow-y-auto', className)}
      style={{
        background: `linear-gradient(to bottom, transparent, rgba(var(--color-muted), ${backgroundOpacity}))`,
      }}
    >
      {/* Animated Messages Container */}
      <StaggerContainer className="space-y-4" staggerDelay={0.15}>
        <AnimatePresence initial={false}>
          {messages.map((message, index) => (
            <StaggerItem key={message.id}>
              <motion.div
                layout
                initial={{ opacity: 0, scale: 0.8, y: 50 }}
                animate={{ opacity: 1, scale: 1, y: 0 }}
                exit={{ opacity: 0, scale: 0.8, y: -20 }}
                transition={{
                  type: 'spring',
                  stiffness: 300,
                  damping: 25,
                  mass: 0.8,
                }}
              >
                <ChatMessage
                  message={message}
                  index={index}
                  thread={getThreadForMessage(message.id)}
                  onAddReaction={onAddReaction}
                  onRemoveReaction={onRemoveReaction}
                  onReply={onReply}
                  onReplyToThread={onReplyToThread}
                  onToggleThread={onToggleThread}
                  onEdit={onEdit}
                  onDelete={onDelete}
                />
              </motion.div>
            </StaggerItem>
          ))}
        </AnimatePresence>
      </StaggerContainer>

      {/* Animated Loading State with Typing Indicator */}
      <AnimatePresence>
        {isLoading && (
          <motion.div
            initial={{ opacity: 0, y: 20, scale: 0.9 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -20, scale: 0.9 }}
            transition={{
              type: 'spring',
              stiffness: 400,
              damping: 25,
            }}
          >
            <TypingIndicator />
          </motion.div>
        )}
      </AnimatePresence>

      {/* Scroll anchor */}
      <motion.div
        ref={messagesEndRef}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.5 }}
      />
    </motion.div>
  )
})
