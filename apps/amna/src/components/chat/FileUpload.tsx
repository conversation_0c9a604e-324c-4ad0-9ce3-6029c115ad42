import { Archive, File, FileText, Image, Music, Upload, Video, X } from 'lucide-react'
import * as React from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { AnimatePresence, motion, spring, variants } from '@/lib/animations'
import { cn } from '@/lib/utils'

export interface UploadedFile {
  id: string
  file: File
  preview?: string
  uploading?: boolean
  progress?: number
  error?: string
}

interface FileUploadProps {
  onFilesSelected: (files: UploadedFile[]) => void
  maxFiles?: number
  maxSize?: number // in MB
  acceptedTypes?: string[]
  disabled?: boolean
  className?: string
}

const fileTypeIcons: Record<string, React.FC<{ className?: string }>> = {
  image: Image,
  video: Video,
  audio: Music,
  text: FileText,
  archive: Archive,
  default: File,
}

const getFileTypeIcon = (mimeType: string) => {
  if (mimeType.startsWith('image/')) return fileTypeIcons.image
  if (mimeType.startsWith('video/')) return fileTypeIcons.video
  if (mimeType.startsWith('audio/')) return fileTypeIcons.audio
  if (mimeType.startsWith('text/')) return fileTypeIcons.text
  if (mimeType.includes('zip') || mimeType.includes('rar')) return fileTypeIcons.archive
  return fileTypeIcons.default
}

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / k ** i).toFixed(2)) + ' ' + sizes[i]
}

export const FileUpload = React.memo(function FileUpload({
  onFilesSelected,
  maxFiles = 5,
  maxSize = 10, // 10MB default
  acceptedTypes = ['image/*', 'application/pdf', '.doc', '.docx', '.txt'],
  disabled = false,
  className,
}: FileUploadProps) {
  const [isDragging, setIsDragging] = React.useState(false)
  const [uploadedFiles, setUploadedFiles] = React.useState<UploadedFile[]>([])
  const fileInputRef = React.useRef<HTMLInputElement>(null)
  const dragCounter = React.useRef(0)

  const processFiles = React.useCallback(
    (files: FileList) => {
      const newFiles: UploadedFile[] = []

      Array.from(files).forEach((file, index) => {
        if (uploadedFiles.length + newFiles.length >= maxFiles) {
          return
        }

        if (file.size > maxSize * 1024 * 1024) {
          newFiles.push({
            id: `${Date.now()}-${index}`,
            file,
            error: `File size exceeds ${maxSize}MB limit`,
          })
          return
        }

        const uploadedFile: UploadedFile = {
          id: `${Date.now()}-${index}`,
          file,
        }

        // Generate preview for images
        if (file.type.startsWith('image/')) {
          const reader = new FileReader()
          reader.onloadend = () => {
            setUploadedFiles((prev) =>
              prev.map((f) =>
                f.id === uploadedFile.id ? { ...f, preview: reader.result as string } : f
              )
            )
          }
          reader.readAsDataURL(file)
        }

        newFiles.push(uploadedFile)
      })

      const updatedFiles = [...uploadedFiles, ...newFiles]
      setUploadedFiles(updatedFiles)
      onFilesSelected(updatedFiles)
    },
    [uploadedFiles, maxFiles, maxSize, onFilesSelected]
  )

  const handleDragEnter = React.useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    dragCounter.current++
    if (e.dataTransfer.items && e.dataTransfer.items.length > 0) {
      setIsDragging(true)
    }
  }, [])

  const handleDragLeave = React.useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    dragCounter.current--
    if (dragCounter.current === 0) {
      setIsDragging(false)
    }
  }, [])

  const handleDragOver = React.useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
  }, [])

  const handleDrop = React.useCallback(
    (e: React.DragEvent) => {
      e.preventDefault()
      e.stopPropagation()
      setIsDragging(false)
      dragCounter.current = 0

      if (disabled) return

      const files = e.dataTransfer.files
      if (files && files.length > 0) {
        processFiles(files)
      }
    },
    [disabled, processFiles]
  )

  const handleFileSelect = React.useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const files = e.target.files
      if (files && files.length > 0) {
        processFiles(files)
      }
    },
    [processFiles]
  )

  const handleRemoveFile = React.useCallback(
    (fileId: string) => {
      const updatedFiles = uploadedFiles.filter((f) => f.id !== fileId)
      setUploadedFiles(updatedFiles)
      onFilesSelected(updatedFiles)
    },
    [uploadedFiles, onFilesSelected]
  )

  const handleButtonClick = React.useCallback(() => {
    fileInputRef.current?.click()
  }, [])

  return (
    <div className={cn('space-y-4', className)}>
      {/* Drop Zone */}
      <motion.div
        className={cn(
          'relative border-2 border-dashed rounded-lg p-6 transition-colors',
          isDragging ? 'border-primary bg-primary/5' : 'border-border hover:border-primary/50',
          disabled && 'opacity-50 cursor-not-allowed'
        )}
        onDragEnter={handleDragEnter}
        onDragLeave={handleDragLeave}
        onDragOver={handleDragOver}
        onDrop={handleDrop}
        animate={{
          scale: isDragging ? 1.02 : 1,
          borderColor: isDragging ? 'rgb(59, 130, 246)' : 'rgb(228, 228, 231)',
        }}
        transition={spring.gentle}
      >
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept={acceptedTypes.join(',')}
          onChange={handleFileSelect}
          disabled={disabled}
          className="hidden"
        />

        <div className="text-center space-y-3">
          <motion.div
            className="mx-auto w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center"
            animate={{
              scale: isDragging ? 1.2 : 1,
              rotate: isDragging ? 15 : 0,
            }}
            transition={spring.bouncy}
          >
            <Upload className="h-6 w-6 text-primary" />
          </motion.div>

          <div>
            <p className="text-sm font-medium">
              Drop files here or{' '}
              <button
                type="button"
                onClick={handleButtonClick}
                className="text-primary hover:underline"
                disabled={disabled}
              >
                browse
              </button>
            </p>
            <p className="text-xs text-muted-foreground mt-1">
              Max {maxFiles} files, up to {maxSize}MB each
            </p>
          </div>
        </div>

        {/* Drag Overlay */}
        <AnimatePresence>
          {isDragging && (
            <motion.div
              className="absolute inset-0 bg-primary/10 rounded-lg flex items-center justify-center"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={spring.snappy}
            >
              <motion.div
                animate={{
                  scale: [1, 1.1, 1],
                  rotate: [0, 5, -5, 0],
                }}
                transition={{
                  duration: 0.5,
                  repeat: Infinity,
                }}
              >
                <Upload className="h-12 w-12 text-primary" />
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>

      {/* Uploaded Files List */}
      <AnimatePresence>
        {uploadedFiles.length > 0 && (
          <motion.div
            className="space-y-2"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={spring.gentle}
          >
            {uploadedFiles.map((uploadedFile, index) => {
              const Icon = getFileTypeIcon(uploadedFile.file.type)

              return (
                <motion.div
                  key={uploadedFile.id}
                  className={cn(
                    'flex items-center gap-3 p-3 rounded-lg border bg-card',
                    uploadedFile.error && 'border-destructive/50 bg-destructive/5'
                  )}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  transition={{ ...spring.gentle, delay: index * 0.05 }}
                  layout
                >
                  {/* File Icon or Preview */}
                  {uploadedFile.preview ? (
                    <img
                      src={uploadedFile.preview}
                      alt={uploadedFile.file.name}
                      className="w-10 h-10 rounded object-cover"
                    />
                  ) : (
                    <div className="w-10 h-10 rounded bg-muted flex items-center justify-center">
                      <Icon className="h-5 w-5 text-muted-foreground" />
                    </div>
                  )}

                  {/* File Info */}
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium truncate">{uploadedFile.file.name}</p>
                    <p className="text-xs text-muted-foreground">
                      {uploadedFile.error || formatFileSize(uploadedFile.file.size)}
                    </p>
                  </div>

                  {/* Upload Progress */}
                  {uploadedFile.uploading && uploadedFile.progress !== undefined && (
                    <div className="w-16">
                      <div className="h-1 bg-muted rounded-full overflow-hidden">
                        <motion.div
                          className="h-full bg-primary"
                          initial={{ width: 0 }}
                          animate={{ width: `${uploadedFile.progress}%` }}
                          transition={spring.gentle}
                        />
                      </div>
                    </div>
                  )}

                  {/* Remove Button */}
                  <Button
                    size="sm"
                    variant="ghost"
                    className="h-8 w-8 p-0"
                    onClick={() => handleRemoveFile(uploadedFile.id)}
                    disabled={uploadedFile.uploading}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </motion.div>
              )
            })}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
})
