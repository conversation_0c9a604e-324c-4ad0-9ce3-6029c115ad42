import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ExternalLink, Loader2 } from 'lucide-react'
import * as React from 'react'
import { AnimatePresence, motion, spring } from '@/lib/animations'
import { cn } from '@/lib/utils'

export interface LinkMetadata {
  url: string
  title?: string
  description?: string
  image?: string
  siteName?: string
  favicon?: string
}

interface LinkPreviewProps {
  url: string
  className?: string
  onMetadataLoad?: (metadata: LinkMetadata) => void
}

// URL regex to detect links
export const URL_REGEX =
  /https?:\/\/(www\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_+.~#?&//=]*)/gi

// Mock function to simulate fetching link metadata
// In a real app, this would call a backend API that fetches and parses the URL
const fetchLinkMetadata = async (url: string): Promise<LinkMetadata> => {
  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 1000))

  // Mock data based on common sites
  if (url.includes('github.com')) {
    return {
      url,
      title: 'GitHub - Where the world builds software',
      description:
        'GitHub is where over 100 million developers shape the future of software, together.',
      image: 'https://github.githubassets.com/images/modules/site/social-cards/github-social.png',
      siteName: 'GitHub',
      favicon: 'https://github.com/favicon.ico',
    }
  } else if (url.includes('youtube.com') || url.includes('youtu.be')) {
    return {
      url,
      title: 'YouTube Video',
      description: 'Watch this video on YouTube',
      image: 'https://i.ytimg.com/vi/dQw4w9WgXcQ/maxresdefault.jpg',
      siteName: 'YouTube',
      favicon: 'https://www.youtube.com/favicon.ico',
    }
  } else if (url.includes('twitter.com') || url.includes('x.com')) {
    return {
      url,
      title: 'Post on X',
      description: 'View this post on X (formerly Twitter)',
      siteName: 'X',
      favicon: 'https://abs.twimg.com/favicons/twitter.ico',
    }
  }

  // Generic fallback
  const domain = new URL(url).hostname
  return {
    url,
    title: domain,
    description: `Visit ${domain}`,
    siteName: domain,
    favicon: `https://${domain}/favicon.ico`,
  }
}

export const LinkPreview = React.memo(function LinkPreview({
  url,
  className,
  onMetadataLoad,
}: LinkPreviewProps) {
  const [metadata, setMetadata] = React.useState<LinkMetadata | null>(null)
  const [loading, setLoading] = React.useState(true)
  const [error, setError] = React.useState(false)

  React.useEffect(() => {
    let cancelled = false

    const loadMetadata = async () => {
      try {
        setLoading(true)
        setError(false)
        const data = await fetchLinkMetadata(url)

        if (!cancelled) {
          setMetadata(data)
          onMetadataLoad?.(data)
        }
      } catch (err) {
        if (!cancelled) {
          setError(true)
        }
      } finally {
        if (!cancelled) {
          setLoading(false)
        }
      }
    }

    loadMetadata()

    return () => {
      cancelled = true
    }
  }, [url, onMetadataLoad])

  if (error) {
    return null // Don't show preview if there's an error
  }

  return (
    <AnimatePresence mode="wait">
      {loading ? (
        <motion.div
          key="loading"
          className={cn(
            'flex items-center gap-3 p-3 rounded-lg border border-border bg-card/50',
            className
          )}
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          transition={spring.snappy}
        >
          <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
          <span className="text-sm text-muted-foreground">Loading preview...</span>
        </motion.div>
      ) : metadata ? (
        <motion.a
          key="preview"
          href={url}
          target="_blank"
          rel="noopener noreferrer"
          className={cn(
            'block rounded-lg border border-border bg-card/50 overflow-hidden hover:bg-card transition-colors',
            className
          )}
          initial={{ opacity: 0, scale: 0.95, y: 10 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: 10 }}
          transition={spring.gentle}
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          <div className="flex gap-3">
            {metadata.image && (
              <motion.div
                className="relative w-32 h-32 flex-shrink-0 bg-muted"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.1 }}
              >
                <img
                  src={metadata.image}
                  alt={metadata.title || 'Link preview'}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    // Hide image if it fails to load
                    ;(e.target as HTMLElement).style.display = 'none'
                  }}
                />
              </motion.div>
            )}

            <div className="flex-1 p-3 min-w-0">
              <div className="flex items-start gap-2">
                <div className="flex-1 min-w-0">
                  {metadata.siteName && (
                    <motion.div
                      className="flex items-center gap-1.5 text-xs text-muted-foreground mb-1"
                      initial={{ opacity: 0, y: -5 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.2 }}
                    >
                      {metadata.favicon && (
                        <img
                          src={metadata.favicon}
                          alt=""
                          className="w-3 h-3"
                          onError={(e) => {
                            ;(e.target as HTMLElement).style.display = 'none'
                          }}
                        />
                      )}
                      <span>{metadata.siteName}</span>
                    </motion.div>
                  )}

                  <motion.h4
                    className="font-medium text-sm line-clamp-1 mb-1"
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.3 }}
                  >
                    {metadata.title}
                  </motion.h4>

                  {metadata.description && (
                    <motion.p
                      className="text-sm text-muted-foreground line-clamp-2"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 0.4 }}
                    >
                      {metadata.description}
                    </motion.p>
                  )}
                </div>

                <motion.div
                  initial={{ opacity: 0, scale: 0 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.5, ...spring.bouncy }}
                >
                  <ExternalLink className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                </motion.div>
              </div>
            </div>
          </div>
        </motion.a>
      ) : null}
    </AnimatePresence>
  )
})

// Component to render multiple link previews from text
interface LinkPreviewsProps {
  content: string
  className?: string
  maxPreviews?: number
}

export const LinkPreviews = React.memo(function LinkPreviews({
  content,
  className,
  maxPreviews = 3,
}: LinkPreviewsProps) {
  const links = React.useMemo(() => {
    const matches = content.match(URL_REGEX)
    if (!matches) return []

    // Remove duplicates and limit number
    const uniqueLinks = Array.from(new Set(matches))
    return uniqueLinks.slice(0, maxPreviews)
  }, [content, maxPreviews])

  if (links.length === 0) return null

  return (
    <div className={cn('space-y-2 mt-3', className)}>
      {links.map((url, index) => (
        <motion.div
          key={url}
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: index * 0.1, ...spring.gentle }}
        >
          <LinkPreview url={url} />
        </motion.div>
      ))}
    </div>
  )
})
