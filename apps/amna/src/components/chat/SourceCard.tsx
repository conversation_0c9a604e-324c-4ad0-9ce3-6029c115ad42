import { ExternalLink, Globe } from 'lucide-react'
import * as React from 'react'
import { Card } from '@/components/ui/card'
import { cn } from '@/lib/utils'
import type { Source } from './ChatMessages'

interface SourceCardProps {
  source: Source
  className?: string
}

export const SourceCard = React.memo(function SourceCard({ source, className }: SourceCardProps) {
  const handleClick = React.useCallback(() => {
    window.open(source.url, '_blank', 'noopener,noreferrer')
  }, [source.url])

  const getDomain = React.useCallback((url: string) => {
    try {
      return new URL(url).hostname.replace('www.', '')
    } catch {
      return url
    }
  }, [])

  const domain = React.useMemo(() => getDomain(source.url), [getDomain, source.url])

  return (
    <Card
      className={cn(
        'group p-3 cursor-pointer hover:bg-accent/50 hover:shadow-lg hover:scale-[1.02] transition-all duration-300 ease-out border-l-4 border-l-primary/20 hover:border-l-primary/60 animate-in fade-in slide-in-from-left-2',
        className
      )}
      onClick={handleClick}
    >
      <div className="space-y-2">
        {/* Header */}
        <div className="flex items-start justify-between gap-2">
          <div className="flex items-center gap-2 min-w-0">
            <div className="transition-transform duration-300 group-hover:scale-110">
              {source.favicon ? (
                <img
                  src={source.favicon}
                  alt=""
                  className="w-4 h-4 flex-shrink-0"
                  onError={(e) => {
                    e.currentTarget.style.display = 'none'
                  }}
                />
              ) : (
                <Globe className="w-4 h-4 flex-shrink-0 text-muted-foreground transition-colors duration-300 group-hover:text-primary" />
              )}
            </div>
            <span className="text-xs text-muted-foreground truncate transition-colors duration-300 group-hover:text-primary">
              {domain}
            </span>
          </div>
          <ExternalLink className="w-3 h-3 flex-shrink-0 text-muted-foreground transition-all duration-300 group-hover:text-primary group-hover:translate-x-0.5 group-hover:-translate-y-0.5" />
        </div>

        {/* Title */}
        <h4 className="font-medium text-sm line-clamp-2 leading-tight transition-colors duration-300 group-hover:text-foreground">
          {source.title}
        </h4>

        {/* Snippet */}
        <p className="text-xs text-muted-foreground line-clamp-3 leading-relaxed transition-colors duration-300 group-hover:text-foreground/80">
          {source.snippet}
        </p>
      </div>
    </Card>
  )
})
