import { <PERSON><PERSON><PERSON>, Ch<PERSON>ronDown, Clock, MessageSquare, Star, Zap } from 'lucide-react'
import * as React from 'react'
import {
  AnimatePresence,
  motion,
  <PERSON>Button,
  StaggerContainer,
  StaggerItem,
  spring,
} from '@/lib/animations'
import { cn } from '@/lib/utils'
import type { ChatTemplate } from './ChatTemplates'

interface QuickResponse {
  id: string
  text: string
  category: 'agreement' | 'question' | 'thanks' | 'clarification' | 'custom'
  shortcut?: string
  icon?: React.ComponentType<{ className?: string }>
  description?: string
  usageCount?: number
  color?: string
}

interface QuickResponsesProps {
  isVisible: boolean
  onSelectResponse: (response: string) => void
  onOpenTemplates?: () => void
  className?: string
  context?: {
    lastMessage?: string
    messageType?: string
    hasCode?: boolean
    hasError?: boolean
  }
}

const DEFAULT_QUICK_RESPONSES: QuickResponse[] = [
  // Agreement responses
  {
    id: 'agree-yes',
    text: 'Yes, that makes sense!',
    category: 'agreement',
    shortcut: 'y',
    usageCount: 45,
    color: 'text-green-600',
  },
  {
    id: 'agree-exactly',
    text: 'Exactly what I was looking for.',
    category: 'agreement',
    shortcut: 'e',
    usageCount: 32,
    color: 'text-green-600',
  },
  {
    id: 'agree-perfect',
    text: 'Perfect! Thank you.',
    category: 'agreement',
    shortcut: 'p',
    usageCount: 67,
    color: 'text-green-600',
  },

  // Questions
  {
    id: 'question-how',
    text: 'How does this work?',
    category: 'question',
    shortcut: 'h',
    usageCount: 28,
    color: 'text-blue-600',
  },
  {
    id: 'question-why',
    text: 'Why is this the best approach?',
    category: 'question',
    shortcut: 'w',
    usageCount: 19,
    color: 'text-blue-600',
  },
  {
    id: 'question-alternative',
    text: 'Are there alternative approaches?',
    category: 'question',
    shortcut: 'a',
    usageCount: 23,
    color: 'text-blue-600',
  },

  // Thanks
  {
    id: 'thanks-simple',
    text: 'Thank you!',
    category: 'thanks',
    shortcut: 't',
    usageCount: 89,
    color: 'text-purple-600',
  },
  {
    id: 'thanks-helpful',
    text: 'This is very helpful, thanks!',
    category: 'thanks',
    shortcut: 'th',
    usageCount: 56,
    color: 'text-purple-600',
  },
  {
    id: 'thanks-detailed',
    text: 'Thanks for the detailed explanation!',
    category: 'thanks',
    shortcut: 'td',
    usageCount: 34,
    color: 'text-purple-600',
  },

  // Clarification
  {
    id: 'clarify-more',
    text: 'Can you explain this in more detail?',
    category: 'clarification',
    shortcut: 'm',
    usageCount: 41,
    color: 'text-orange-600',
  },
  {
    id: 'clarify-example',
    text: 'Could you provide an example?',
    category: 'clarification',
    shortcut: 'ex',
    usageCount: 37,
    color: 'text-orange-600',
  },
  {
    id: 'clarify-confused',
    text: "I'm still confused about this part.",
    category: 'clarification',
    shortcut: 'c',
    usageCount: 22,
    color: 'text-orange-600',
  },
]

const CONTEXTUAL_RESPONSES = {
  code: [
    'Can you explain this code?',
    'Is this the most efficient approach?',
    'Are there any potential issues with this code?',
  ],
  error: [
    'How do I fix this error?',
    'What causes this error?',
    'Can you provide a corrected version?',
  ],
  long: [
    'Could you summarize the key points?',
    'What are the most important takeaways?',
    'Can you break this down into steps?',
  ],
}

export function QuickResponses({
  isVisible,
  onSelectResponse,
  onOpenTemplates,
  className,
  context,
}: QuickResponsesProps) {
  const [responses, setResponses] = React.useState<QuickResponse[]>(DEFAULT_QUICK_RESPONSES)
  const [hoveredResponse, setHoveredResponse] = React.useState<string | null>(null)
  const [showContextual, setShowContextual] = React.useState(true)
  const [isExpanded, setIsExpanded] = React.useState(false)

  // Get contextual suggestions based on the last message
  const contextualSuggestions = React.useMemo(() => {
    if (!context?.lastMessage || !showContextual) {
      return []
    }

    const suggestions: string[] = []

    // Check for code blocks
    if (context.hasCode || context.lastMessage.includes('```')) {
      suggestions.push(...CONTEXTUAL_RESPONSES.code)
    }

    // Check for errors
    if (context.hasError || context.lastMessage.toLowerCase().includes('error')) {
      suggestions.push(...CONTEXTUAL_RESPONSES.error)
    }

    // Check for long responses
    if (context.lastMessage.length > 500) {
      suggestions.push(...CONTEXTUAL_RESPONSES.long)
    }

    return suggestions.slice(0, 3) // Limit to 3 suggestions
  }, [context, showContextual])

  // Filter responses by category and usage
  const categorizedResponses = React.useMemo(() => {
    const categories = {
      popular: responses
        .filter((r) => (r.usageCount || 0) > 30)
        .sort((a, b) => (b.usageCount || 0) - (a.usageCount || 0))
        .slice(0, 4),
      agreement: responses.filter((r) => r.category === 'agreement').slice(0, 3),
      question: responses.filter((r) => r.category === 'question').slice(0, 3),
      thanks: responses.filter((r) => r.category === 'thanks').slice(0, 3),
      clarification: responses.filter((r) => r.category === 'clarification').slice(0, 3),
    }

    return categories
  }, [responses])

  const handleSelectResponse = React.useCallback(
    (response: QuickResponse | string) => {
      const text = typeof response === 'string' ? response : response.text

      // Update usage count if it's a quick response object
      if (typeof response === 'object') {
        setResponses((prev) =>
          prev.map((r) =>
            r.id === response.id ? { ...r, usageCount: (r.usageCount || 0) + 1 } : r
          )
        )
      }

      onSelectResponse(text)
    },
    [onSelectResponse]
  )

  // Keyboard shortcuts
  React.useEffect(() => {
    if (!isVisible) {
      return
    }

    const handleKeyDown = (e: KeyboardEvent) => {
      // Only handle shortcuts when focused on the chat input or quick responses
      const target = e.target as HTMLElement
      if (!target.closest('.chat-input') && !target.closest('.quick-responses')) {
        return
      }

      const shortcut = e.key.toLowerCase()
      const matchingResponse = responses.find((r) => r.shortcut === shortcut)

      if (matchingResponse && !e.ctrlKey && !e.metaKey && !e.altKey) {
        e.preventDefault()
        handleSelectResponse(matchingResponse)
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [isVisible, responses, handleSelectResponse])

  if (!isVisible) {
    return null
  }

  return (
    <motion.div
      className={cn(
        'quick-responses border-t border-border bg-muted/30 backdrop-blur-sm',
        className
      )}
      initial={{ height: 0, opacity: 0 }}
      animate={{ height: 'auto', opacity: 1 }}
      exit={{ height: 0, opacity: 0 }}
      transition={spring.gentle}
    >
      {/* Toggle Header */}
      <div className="px-4 py-3 border-b border-border/50">
        <div className="flex items-center justify-between">
          <PopButton
            className="flex items-center gap-2 text-sm font-medium text-foreground hover:text-primary transition-colors"
            onClick={() => setIsExpanded(!isExpanded)}
          >
            <motion.div
              animate={{ rotate: [0, 15, 0] }}
              transition={{ duration: 2, repeat: Infinity, ease: 'easeInOut' }}
            >
              <Zap className="h-4 w-4 text-primary" />
            </motion.div>
            <span>Quick Responses</span>
            <motion.div animate={{ rotate: isExpanded ? 180 : 0 }} transition={{ duration: 0.2 }}>
              <ChevronDown className="h-4 w-4" />
            </motion.div>
          </PopButton>

          <div className="flex items-center gap-2">
            {onOpenTemplates && (
              <PopButton
                className="text-xs text-muted-foreground hover:text-primary transition-colors"
                onClick={onOpenTemplates}
              >
                All Templates
                <ArrowRight className="h-3 w-3 ml-1" />
              </PopButton>
            )}
          </div>
        </div>
      </div>

      {/* Collapsible Content */}
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            className="p-4 space-y-4"
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={spring.gentle}
          >
            {/* Contextual Suggestions */}
            {contextualSuggestions.length > 0 && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={spring.gentle}
              >
                <div className="flex items-center gap-2 mb-2">
                  <MessageSquare className="h-3 w-3 text-muted-foreground" />
                  <span className="text-xs text-muted-foreground">Suggested for this message:</span>
                </div>
                <StaggerContainer className="flex flex-wrap gap-2" staggerDelay={0.05}>
                  {contextualSuggestions.map((suggestion, index) => (
                    <StaggerItem key={index}>
                      <motion.button
                        className="px-3 py-1.5 text-xs bg-primary/10 text-primary border border-primary/20 rounded-full hover:bg-primary/20 transition-colors"
                        onClick={() => handleSelectResponse(suggestion)}
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        {suggestion}
                      </motion.button>
                    </StaggerItem>
                  ))}
                </StaggerContainer>
              </motion.div>
            )}

            {/* Popular Responses */}
            {categorizedResponses.popular.length > 0 && (
              <div>
                <div className="flex items-center gap-2 mb-2">
                  <Star className="h-3 w-3 text-muted-foreground" />
                  <span className="text-xs text-muted-foreground">Most used:</span>
                </div>
                <StaggerContainer className="flex flex-wrap gap-2" staggerDelay={0.03}>
                  {categorizedResponses.popular.map((response) => (
                    <StaggerItem key={response.id}>
                      <motion.button
                        className={cn(
                          'group relative px-3 py-1.5 text-xs border rounded-full transition-all duration-200',
                          'bg-background hover:bg-accent border-border hover:border-primary/30',
                          response.color
                        )}
                        onClick={() => handleSelectResponse(response)}
                        onMouseEnter={() => setHoveredResponse(response.id)}
                        onMouseLeave={() => setHoveredResponse(null)}
                        whileHover={{ scale: 1.05, y: -1 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        {response.text}
                        {response.shortcut && (
                          <motion.span
                            className="ml-2 opacity-0 group-hover:opacity-100 transition-opacity text-muted-foreground"
                            initial={{ opacity: 0 }}
                            animate={{ opacity: hoveredResponse === response.id ? 1 : 0 }}
                          >
                            ({response.shortcut})
                          </motion.span>
                        )}
                      </motion.button>
                    </StaggerItem>
                  ))}
                </StaggerContainer>
              </div>
            )}

            {/* Categorized Responses */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {Object.entries(categorizedResponses).map(([category, categoryResponses]) => {
                if (category === 'popular' || categoryResponses.length === 0) {
                  return null
                }

                const categoryConfig = {
                  agreement: { icon: '*', color: 'text-green-600' },
                  question: { icon: '?', color: 'text-blue-600' },
                  thanks: { icon: '+', color: 'text-purple-600' },
                  clarification: { icon: '!', color: 'text-orange-600' },
                }[category]

                return (
                  <motion.div
                    key={category}
                    className="space-y-2"
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.1 }}
                  >
                    <div className="flex items-center gap-1">
                      <span className="text-xs">{categoryConfig?.icon}</span>
                      <span className="text-xs font-medium text-muted-foreground capitalize">
                        {category}
                      </span>
                    </div>
                    <StaggerContainer className="space-y-1" staggerDelay={0.02}>
                      {categoryResponses.map((response) => (
                        <StaggerItem key={response.id}>
                          <motion.button
                            className={cn(
                              'w-full text-left px-2 py-1 text-xs rounded hover:bg-accent transition-colors',
                              'text-muted-foreground hover:text-foreground'
                            )}
                            onClick={() => handleSelectResponse(response)}
                            whileHover={{ x: 2 }}
                            whileTap={{ scale: 0.98 }}
                          >
                            {response.text}
                          </motion.button>
                        </StaggerItem>
                      ))}
                    </StaggerContainer>
                  </motion.div>
                )
              })}
            </div>

            {/* Keyboard Hint */}
            <motion.div
              className="text-xs text-muted-foreground text-center pt-2 border-t border-border/50"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.5 }}
            >
              Hover to see keyboard shortcuts • Click to use response
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  )
}
