import type * as React from 'react'
import { cn } from '@/lib/utils'

interface ChatContainerProps {
  children: React.ReactNode
  className?: string
}

export function ChatContainer({ children, className }: ChatContainerProps) {
  return (
    <div
      className={cn(
        'flex flex-col h-full max-w-4xl lg:max-w-6xl xl:max-w-7xl mx-auto px-4 lg:px-6 xl:px-8',
        className
      )}
    >
      {children}
    </div>
  )
}
