import { Search, Smile, X } from 'lucide-react'
import * as React from 'react'
import { Button } from '@/components/ui/button'
import { AnimatePresence, motion, spring, variants } from '@/lib/animations'
import { cn } from '@/lib/utils'

interface EmojiPickerProps {
  isOpen: boolean
  onClose: () => void
  onSelectEmoji: (emoji: string) => void
  className?: string
}

const emojiCategories = {
  smileys: {
    name: 'Smileys & Emotion',
    emojis: [
      '😀',
      '😃',
      '😄',
      '😁',
      '😆',
      '😅',
      '🤣',
      '😂',
      '🙂',
      '🙃',
      '😉',
      '😊',
      '😇',
      '🥰',
      '😍',
      '🤩',
      '😘',
      '😗',
      '😚',
      '😙',
      '😋',
      '😛',
      '😜',
      '🤪',
      '😝',
      '🤑',
      '🤗',
      '🤭',
      '🤫',
      '🤔',
      '🤐',
      '🤨',
      '😐',
      '😑',
      '😶',
      '😏',
      '😒',
      '🙄',
      '😬',
      '🤥',
      '😌',
      '😔',
      '😪',
      '🤤',
      '😴',
      '😷',
      '🤒',
      '🤕',
      '🤢',
      '🤮',
      '🤧',
      '🥵',
      '🥶',
      '🥴',
      '😵',
      '🤯',
      '🤠',
      '🥳',
      '😎',
      '🤓',
      '🧐',
      '😕',
      '😟',
      '🙁',
      '☹️',
      '😮',
      '😯',
      '😲',
      '😳',
      '🥺',
      '😦',
      '😧',
      '😨',
      '😰',
      '😥',
      '😢',
      '😭',
      '😱',
      '😖',
      '😣',
      '😞',
      '😓',
      '😩',
      '😫',
      '🥱',
      '😤',
      '😡',
      '😠',
      '🤬',
      '😈',
      '👿',
      '💀',
      '☠️',
      '💩',
      '🤡',
      '👹',
      '👺',
      '👻',
      '👽',
      '👾',
      '🤖',
      '😺',
      '😸',
      '😹',
      '😻',
      '😼',
      '😽',
      '🙀',
      '😿',
      '😾',
    ],
  },
  gestures: {
    name: 'People & Body',
    emojis: [
      '👋',
      '🤚',
      '🖐️',
      '✋',
      '🖖',
      '👌',
      '🤌',
      '🤏',
      '✌️',
      '🤞',
      '🤟',
      '🤘',
      '🤙',
      '👈',
      '👉',
      '👆',
      '🖕',
      '👇',
      '☝️',
      '👍',
      '👎',
      '✊',
      '👊',
      '🤛',
      '🤜',
      '👏',
      '🙌',
      '👐',
      '🤲',
      '🤝',
      '🙏',
      '✍️',
      '💅',
      '🤳',
      '💪',
      '🦾',
      '🦿',
      '🦵',
      '🦶',
      '👂',
      '🦻',
      '👃',
      '🧠',
      '🫀',
      '🫁',
      '🦷',
      '🦴',
      '👀',
      '👁️',
      '👅',
      '👄',
      '💋',
      '🩸',
    ],
  },
  nature: {
    name: 'Animals & Nature',
    emojis: [
      '🐵',
      '🐒',
      '🦍',
      '🦧',
      '🐶',
      '🐕',
      '🦮',
      '🐕‍🦺',
      '🐩',
      '🐺',
      '🦊',
      '🦝',
      '🐱',
      '🐈',
      '🐈‍⬛',
      '🦁',
      '🐯',
      '🐅',
      '🐆',
      '🐴',
      '🐎',
      '🦄',
      '🦓',
      '🦌',
      '🦬',
      '🐮',
      '🐂',
      '🐃',
      '🐄',
      '🐷',
      '🐖',
      '🐗',
      '🐽',
      '🐏',
      '🐑',
      '🐐',
      '🐪',
      '🐫',
      '🦙',
      '🦒',
      '🐘',
      '🦣',
      '🦏',
      '🦛',
      '🐭',
      '🐁',
      '🐀',
      '🐹',
      '🐰',
      '🐇',
      '🐿️',
      '🦫',
      '🦔',
      '🦇',
      '🐻',
      '🐻‍❄️',
      '🐨',
      '🐼',
      '🦥',
      '🦦',
      '🦨',
      '🦘',
      '🦡',
      '🐾',
    ],
  },
  food: {
    name: 'Food & Drink',
    emojis: [
      '🍏',
      '🍎',
      '🍐',
      '🍊',
      '🍋',
      '🍌',
      '🍉',
      '🍇',
      '🍓',
      '🫐',
      '🍈',
      '🍒',
      '🍑',
      '🥭',
      '🍍',
      '🥥',
      '🥝',
      '🍅',
      '🍆',
      '🥑',
      '🥦',
      '🥬',
      '🥒',
      '🌶️',
      '🫑',
      '🌽',
      '🥕',
      '🫒',
      '🧄',
      '🧅',
      '🥔',
      '🍠',
      '🥐',
      '🥯',
      '🍞',
      '🥖',
      '🥨',
      '🧀',
      '🥚',
      '🍳',
      '🧈',
      '🥞',
      '🧇',
      '🥓',
      '🥩',
      '🍗',
      '🍖',
      '🦴',
      '🌭',
      '🍔',
      '🍟',
      '🍕',
      '🫓',
      '🥪',
      '🥙',
      '🧆',
      '🌮',
      '🌯',
      '🫔',
      '🥗',
      '🥘',
      '🫕',
      '🥫',
      '🍝',
      '🍜',
      '🍲',
      '🍛',
      '🍣',
      '🍱',
      '🥟',
      '🦪',
      '🍤',
      '🍙',
      '🍚',
      '🍘',
      '🍥',
      '🥠',
      '🥮',
      '🍢',
      '🍡',
      '🍧',
      '🍨',
      '🍦',
      '🥧',
      '🧁',
      '🍰',
      '🎂',
      '🍮',
      '🍭',
      '🍬',
      '🍫',
      '🍿',
      '🍩',
      '🍪',
      '🌰',
      '🥜',
      '🍯',
      '🥛',
      '🍼',
      '🫖',
      '☕',
      '🍵',
      '🧃',
      '🥤',
      '🧋',
      '🍶',
      '🍺',
      '🍻',
      '🥂',
      '🍷',
      '🥃',
      '🍸',
      '🍹',
      '🧉',
      '🍾',
      '🧊',
    ],
  },
  objects: {
    name: 'Objects',
    emojis: [
      '⌚',
      '📱',
      '📲',
      '💻',
      '⌨️',
      '🖥️',
      '🖨️',
      '🖱️',
      '🖲️',
      '🕹️',
      '🗜️',
      '💽',
      '💾',
      '💿',
      '📀',
      '📼',
      '📷',
      '📸',
      '📹',
      '🎥',
      '📽️',
      '🎞️',
      '📞',
      '☎️',
      '📟',
      '📠',
      '📺',
      '📻',
      '🎙️',
      '🎚️',
      '🎛️',
      '🧭',
      '⏱️',
      '⏲️',
      '⏰',
      '🕰️',
      '⌛',
      '⏳',
      '📡',
      '🔋',
      '🔌',
      '💡',
      '🔦',
      '🕯️',
      '🪔',
      '🧯',
      '🛢️',
      '💸',
      '💵',
      '💴',
      '💶',
      '💷',
      '🪙',
      '💰',
      '💳',
      '💎',
      '⚖️',
      '🪜',
      '🧰',
      '🪛',
      '🔧',
      '🔨',
      '⚒️',
      '🛠️',
      '⛏️',
      '🪚',
      '🔩',
      '⚙️',
      '🪤',
      '🧱',
      '⛓️',
      '🧲',
      '🔫',
      '💣',
      '🧨',
      '🪓',
      '🔪',
      '🗡️',
      '⚔️',
      '🛡️',
      '🚬',
      '⚰️',
      '🪦',
      '⚱️',
      '🏺',
      '🔮',
      '📿',
      '🧿',
      '💈',
      '⚗️',
      '🔭',
      '🔬',
      '🕳️',
      '🩹',
      '🩺',
      '💊',
      '💉',
      '🩸',
      '🧬',
      '🦠',
      '🧫',
      '🧪',
      '🌡️',
      '🧹',
      '🪠',
      '🧺',
      '🧻',
      '🚽',
      '🚰',
      '🚿',
    ],
  },
  symbols: {
    name: 'Symbols',
    emojis: [
      '❤️',
      '🧡',
      '💛',
      '💚',
      '💙',
      '💜',
      '🖤',
      '🤍',
      '🤎',
      '💔',
      '❣️',
      '💕',
      '💞',
      '💓',
      '💗',
      '💖',
      '💘',
      '💝',
      '💟',
      '☮️',
      '✝️',
      '☪️',
      '🕉️',
      '☸️',
      '✡️',
      '🔯',
      '🕎',
      '☯️',
      '☦️',
      '🛐',
      '⛎',
      '♈',
      '♉',
      '♊',
      '♋',
      '♌',
      '♍',
      '♎',
      '♏',
      '♐',
      '♑',
      '♒',
      '♓',
      '🆔',
      '⚛️',
      '🉑',
      '☢️',
      '☣️',
      '📴',
      '📳',
      '🈶',
      '🈚',
      '🈸',
      '🈺',
      '🈷️',
      '✴️',
      '🆚',
      '💮',
      '🉐',
      '㊙️',
      '㊗️',
      '🈴',
      '🈵',
      '🈹',
      '🈲',
      '🅰️',
      '🅱️',
      '🆎',
      '🆑',
      '🅾️',
      '🆘',
      '❌',
      '⭕',
      '🛑',
      '⛔',
      '📛',
      '🚫',
      '💯',
      '💢',
      '♨️',
      '🚷',
      '🚯',
      '🚳',
      '🚱',
      '🔞',
      '📵',
      '🚭',
      '❗',
      '❕',
      '❓',
      '❔',
      '‼️',
      '⁉️',
      '🔅',
      '🔆',
      '〽️',
      '⚠️',
      '🚸',
      '🔱',
      '⚜️',
      '🔰',
      '♻️',
      '✅',
      '🈯',
      '💹',
      '❇️',
      '✳️',
      '❎',
      '🌐',
      '💠',
      'Ⓜ️',
      '🌀',
      '💤',
      '🏧',
      '🚾',
      '♿',
      '🅿️',
      '🛗',
      '🈳',
      '🈂️',
      '🛂',
      '🛃',
      '🛄',
      '🛅',
      '🚹',
      '🚺',
      '🚼',
      '⚧️',
      '🚻',
      '🚮',
      '🎦',
      '📶',
      '🈁',
      '🔣',
      '🔤',
      '🔡',
      '🔠',
      '🆖',
      '🆗',
      '🆙',
      '🆒',
      '🆕',
      '🆓',
      '🔟',
    ],
  },
}

export const EmojiPicker = React.memo(function EmojiPicker({
  isOpen,
  onClose,
  onSelectEmoji,
  className,
}: EmojiPickerProps) {
  const [searchQuery, setSearchQuery] = React.useState('')
  const [selectedCategory, setSelectedCategory] =
    React.useState<keyof typeof emojiCategories>('smileys')
  const [recentEmojis, setRecentEmojis] = React.useState<string[]>(() => {
    // Load recent emojis from localStorage
    const stored = localStorage.getItem('recentEmojis')
    return stored ? JSON.parse(stored) : []
  })

  // Filter emojis based on search
  const filteredEmojis = React.useMemo(() => {
    if (!searchQuery) {
      return emojiCategories[selectedCategory].emojis
    }

    // Search across all categories
    const allEmojis: string[] = []
    Object.values(emojiCategories).forEach((category) => {
      allEmojis.push(...category.emojis)
    })

    // Simple search - in a real app, you'd want to search by emoji names/keywords
    return allEmojis.filter((emoji) => emoji.includes(searchQuery))
  }, [searchQuery, selectedCategory])

  const handleEmojiSelect = React.useCallback(
    (emoji: string) => {
      onSelectEmoji(emoji)

      // Update recent emojis
      const newRecent = [emoji, ...recentEmojis.filter((e) => e !== emoji)].slice(0, 20)
      setRecentEmojis(newRecent)
      localStorage.setItem('recentEmojis', JSON.stringify(newRecent))
    },
    [onSelectEmoji, recentEmojis]
  )

  const handleKeyDown = React.useCallback(
    (e: React.KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose()
      }
    },
    [onClose]
  )

  if (!isOpen) return null

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className={cn(
            'absolute bottom-full mb-2 w-80 bg-popover border border-border rounded-lg shadow-lg overflow-hidden',
            className
          )}
          initial={{ opacity: 0, scale: 0.95, y: 10 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: 10 }}
          transition={spring.snappy}
          onKeyDown={handleKeyDown}
        >
          {/* Header */}
          <div className="flex items-center gap-2 p-3 border-b border-border">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <input
                type="text"
                placeholder="Search emojis..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-9 pr-3 py-1.5 bg-muted rounded-md text-sm outline-none focus:ring-2 focus:ring-primary/50"
                autoFocus
              />
            </div>
            <Button size="sm" variant="ghost" className="h-8 w-8 p-0" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>

          {/* Categories */}
          <div className="flex gap-1 p-2 border-b border-border overflow-x-auto">
            {Object.entries(emojiCategories).map(([key, category]) => (
              <button
                key={key}
                onClick={() => setSelectedCategory(key as keyof typeof emojiCategories)}
                className={cn(
                  'px-3 py-1.5 text-xs font-medium rounded-md transition-colors whitespace-nowrap',
                  selectedCategory === key ? 'bg-primary text-primary-foreground' : 'hover:bg-muted'
                )}
              >
                {category.name}
              </button>
            ))}
          </div>

          {/* Recent Emojis */}
          {recentEmojis.length > 0 && !searchQuery && (
            <div className="p-2 border-b border-border">
              <p className="text-xs font-medium text-muted-foreground mb-2">Recently Used</p>
              <div className="grid grid-cols-10 gap-1">
                {recentEmojis.map((emoji, index) => (
                  <motion.button
                    key={`recent-${emoji}-${index}`}
                    onClick={() => handleEmojiSelect(emoji)}
                    className="w-8 h-8 flex items-center justify-center text-lg hover:bg-muted rounded transition-colors"
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ ...spring.snappy, delay: index * 0.02 }}
                    whileHover={{ scale: 1.2 }}
                    whileTap={{ scale: 0.9 }}
                  >
                    {emoji}
                  </motion.button>
                ))}
              </div>
            </div>
          )}

          {/* Emoji Grid */}
          <div className="h-64 overflow-y-auto p-2">
            {searchQuery && (
              <p className="text-xs font-medium text-muted-foreground mb-2">Search Results</p>
            )}
            <div className="grid grid-cols-10 gap-1">
              {filteredEmojis.map((emoji, index) => (
                <motion.button
                  key={`${selectedCategory}-${emoji}-${index}`}
                  onClick={() => handleEmojiSelect(emoji)}
                  className="w-8 h-8 flex items-center justify-center text-lg hover:bg-muted rounded transition-colors"
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ ...spring.snappy, delay: Math.min(index * 0.01, 0.3) }}
                  whileHover={{ scale: 1.2 }}
                  whileTap={{ scale: 0.9 }}
                >
                  {emoji}
                </motion.button>
              ))}
            </div>
            {filteredEmojis.length === 0 && (
              <p className="text-center text-muted-foreground text-sm mt-8">No emojis found</p>
            )}
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  )
})
