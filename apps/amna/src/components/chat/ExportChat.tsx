import { Download, File, FileImage, FileJson, FileText } from 'lucide-react'
import * as React from 'react'
import { Button } from '@/components/ui/button'
import { AnimatePresence, motion, spring } from '@/lib/animations'
import { cn } from '@/lib/utils'
import type { Message } from './ChatMessages'

interface ExportChatProps {
  messages: Message[]
  chatTitle?: string
  className?: string
}

type ExportFormat = 'json' | 'csv' | 'txt' | 'markdown'

const formatIcons: Record<ExportFormat, React.FC<{ className?: string }>> = {
  json: FileJson,
  csv: File,
  txt: FileText,
  markdown: FileImage,
}

export const ExportChat = React.memo(function ExportChat({
  messages,
  chatTitle = 'Chat History',
  className,
}: ExportChatProps) {
  const [isExporting, setIsExporting] = React.useState(false)
  const [selectedFormat, setSelectedFormat] = React.useState<ExportFormat | null>(null)

  // Format messages as JSON
  const exportAsJSON = React.useCallback(() => {
    const data = {
      title: chatTitle,
      exportDate: new Date().toISOString(),
      messageCount: messages.length,
      messages: messages.map((msg) => ({
        id: msg.id,
        role: msg.role,
        content: msg.content,
        timestamp: msg.timestamp,
        isEdited: msg.isEdited,
        isDeleted: msg.isDeleted,
        messageType: msg.messageType,
        reactions: msg.reactions?.map((r) => ({
          emoji: r.emoji,
          username: r.username,
          timestamp: r.timestamp,
        })),
      })),
    }

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
    downloadFile(blob, `${chatTitle}-${formatDate()}.json`)
  }, [messages, chatTitle])

  // Format messages as CSV
  const exportAsCSV = React.useCallback(() => {
    const headers = ['Timestamp', 'Role', 'Content', 'Message Type', 'Edited', 'Deleted']
    const rows = messages.map((msg) => [
      msg.timestamp.toISOString(),
      msg.role,
      `"${msg.content.replace(/"/g, '""')}"`, // Escape quotes in content
      msg.messageType || 'text',
      msg.isEdited ? 'Yes' : 'No',
      msg.isDeleted ? 'Yes' : 'No',
    ])

    const csv = [headers.join(','), ...rows.map((row) => row.join(','))].join('\n')

    const blob = new Blob([csv], { type: 'text/csv' })
    downloadFile(blob, `${chatTitle}-${formatDate()}.csv`)
  }, [messages, chatTitle])

  // Format messages as plain text
  const exportAsTXT = React.useCallback(() => {
    const text = [
      `${chatTitle}`,
      `Exported on: ${new Date().toLocaleString()}`,
      `Total messages: ${messages.length}`,
      '',
      '---',
      '',
      ...messages.map((msg) => {
        const timestamp = msg.timestamp.toLocaleString()
        const role = msg.role === 'user' ? 'User' : 'Assistant'
        const edited = msg.isEdited ? ' (edited)' : ''
        const deleted = msg.isDeleted ? ' [DELETED]' : ''

        return `[${timestamp}] ${role}${edited}${deleted}:\n${msg.content}\n`
      }),
    ].join('\n')

    const blob = new Blob([text], { type: 'text/plain' })
    downloadFile(blob, `${chatTitle}-${formatDate()}.txt`)
  }, [messages, chatTitle])

  // Format messages as Markdown
  const exportAsMarkdown = React.useCallback(() => {
    const markdown = [
      `# ${chatTitle}`,
      '',
      `**Exported on:** ${new Date().toLocaleString()}  `,
      `**Total messages:** ${messages.length}`,
      '',
      '---',
      '',
      ...messages.map((msg) => {
        const timestamp = msg.timestamp.toLocaleString()
        const role = msg.role === 'user' ? '**User**' : '**Assistant**'
        const edited = msg.isEdited ? ' *(edited)*' : ''
        const deleted = msg.isDeleted ? ' *[DELETED]*' : ''

        let content = msg.content

        // Format code blocks properly
        if (msg.messageType === 'code') {
          content = `\`\`\`\n${content}\n\`\`\``
        }

        return `### ${role} - ${timestamp}${edited}${deleted}\n\n${content}\n`
      }),
    ].join('\n')

    const blob = new Blob([markdown], { type: 'text/markdown' })
    downloadFile(blob, `${chatTitle}-${formatDate()}.md`)
  }, [messages, chatTitle])

  // Handle export based on format
  const handleExport = React.useCallback(
    async (format: ExportFormat) => {
      setIsExporting(true)
      setSelectedFormat(format)

      // Simulate export delay for animation
      await new Promise((resolve) => setTimeout(resolve, 500))

      try {
        switch (format) {
          case 'json':
            exportAsJSON()
            break
          case 'csv':
            exportAsCSV()
            break
          case 'txt':
            exportAsTXT()
            break
          case 'markdown':
            exportAsMarkdown()
            break
        }
      } finally {
        setIsExporting(false)
        setSelectedFormat(null)
      }
    },
    [exportAsJSON, exportAsCSV, exportAsTXT, exportAsMarkdown]
  )

  // Helper function to download file
  const downloadFile = (blob: Blob, filename: string) => {
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = filename
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  // Helper function to format date for filename
  const formatDate = () => {
    const date = new Date()
    return date.toISOString().split('T')[0]
  }

  const exportFormats: Array<{ format: ExportFormat; label: string; description: string }> = [
    {
      format: 'json',
      label: 'JSON',
      description: 'Complete structured data',
    },
    {
      format: 'csv',
      label: 'CSV',
      description: 'Spreadsheet compatible',
    },
    {
      format: 'txt',
      label: 'Text',
      description: 'Plain text format',
    },
    {
      format: 'markdown',
      label: 'Markdown',
      description: 'Formatted with styling',
    },
  ]

  return (
    <div className={cn('space-y-4', className)}>
      <div className="flex items-center gap-2 text-sm text-muted-foreground">
        <Download className="h-4 w-4" />
        <span>Export chat history</span>
      </div>

      <div className="grid grid-cols-2 gap-3">
        {exportFormats.map(({ format, label, description }) => {
          const Icon = formatIcons[format]
          const isSelected = selectedFormat === format

          return (
            <motion.button
              key={format}
              onClick={() => handleExport(format)}
              disabled={isExporting || messages.length === 0}
              className={cn(
                'relative p-4 rounded-lg border text-left transition-all',
                'hover:bg-accent hover:border-primary/50',
                'disabled:opacity-50 disabled:cursor-not-allowed',
                isSelected && 'border-primary bg-primary/5'
              )}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <motion.div
                className="flex items-start gap-3"
                animate={isSelected ? { x: 5 } : { x: 0 }}
                transition={spring.snappy}
              >
                <motion.div
                  animate={
                    isSelected
                      ? {
                          rotate: [0, -10, 10, 0],
                        }
                      : {}
                  }
                  transition={{ duration: 0.5 }}
                >
                  <Icon className="h-5 w-5 text-primary" />
                </motion.div>
                <div className="flex-1">
                  <h4 className="font-medium text-sm">{label}</h4>
                  <p className="text-xs text-muted-foreground mt-0.5">{description}</p>
                </div>
              </motion.div>

              {/* Loading overlay */}
              <AnimatePresence>
                {isExporting && isSelected && (
                  <motion.div
                    className="absolute inset-0 bg-background/80 rounded-lg flex items-center justify-center"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    transition={spring.snappy}
                  >
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{
                        duration: 1,
                        repeat: Infinity,
                        ease: 'linear',
                      }}
                    >
                      <Download className="h-5 w-5 text-primary" />
                    </motion.div>
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.button>
          )
        })}
      </div>

      {messages.length === 0 && (
        <p className="text-sm text-muted-foreground text-center py-4">No messages to export</p>
      )}
    </div>
  )
})
