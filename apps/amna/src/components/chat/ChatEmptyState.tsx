import { Code, Globe, Lightbulb, Search } from 'lucide-react'
import * as React from 'react'
import { Card } from '@/components/ui/card'

// Navigation will be handled by parent component

const suggestedPrompts = [
  {
    icon: Search,
    title: 'Search the web',
    prompt: 'What are the latest trends in React development?',
    category: 'Research',
  },
  {
    icon: Code,
    title: 'Analyze components',
    prompt: 'Extract reusable components from my current project structure',
    category: 'Development',
  },
  {
    icon: Lightbulb,
    title: 'Get suggestions',
    prompt: 'How can I improve the performance of my React application?',
    category: 'Optimization',
  },
  {
    icon: Globe,
    title: 'Learn something new',
    prompt: 'Explain the benefits of TanStack Start over Next.js',
    category: 'Learning',
  },
]

interface ChatEmptyStateProps {
  onSend?: (message: string) => void
}

export function ChatEmptyState({ onSend }: ChatEmptyStateProps) {
  const handlePromptClick = (prompt: string) => {
    onSend?.(prompt)
  }

  return (
    <div className="flex-1 flex flex-col justify-center p-6">
      <div className="max-w-2xl mx-auto w-full space-y-8">
        {/* Header */}
        <div className="text-center space-y-4">
          <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-primary/10 mb-4">
            <Search className="h-8 w-8 text-primary" />
          </div>
          <h1 className="text-3xl font-bold tracking-tight">What can I help you discover?</h1>
          <p className="text-lg text-muted-foreground">
            Ask me anything and I&apos;ll search the web, analyze your code, or help with
            development questions.
          </p>
        </div>

        {/* Suggested Prompts */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {suggestedPrompts.map((item, index) => {
            const Icon = item.icon
            return (
              <Card
                key={index}
                className="group p-4 cursor-pointer hover:bg-accent/50 hover:shadow-md hover:scale-[1.02] transition-all duration-300 ease-out border-dashed hover:border-solid animate-in fade-in slide-in-from-bottom-4"
                style={{ animationDelay: `${index * 100}ms` }}
                onClick={() => handlePromptClick(item.prompt)}
              >
                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0 transition-transform duration-300 group-hover:scale-110">
                    <Icon className="h-5 w-5 text-primary mt-0.5 transition-colors duration-300 group-hover:text-primary/80" />
                  </div>
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      <h3 className="font-medium text-sm transition-colors duration-300 group-hover:text-foreground">
                        {item.title}
                      </h3>
                      <span className="text-xs bg-secondary text-secondary-foreground px-2 py-0.5 rounded-full transition-all duration-300 group-hover:bg-primary group-hover:text-primary-foreground">
                        {item.category}
                      </span>
                    </div>
                    <p className="text-sm text-muted-foreground leading-relaxed transition-colors duration-300 group-hover:text-foreground/80">
                      {item.prompt}
                    </p>
                  </div>
                </div>
              </Card>
            )
          })}
        </div>

        {/* Footer Message */}
        <div className="text-center">
          <p className="text-sm text-muted-foreground">
            Click a suggestion above or use the input below to start chatting.
          </p>
        </div>
      </div>
    </div>
  )
}
