import { MessageSquare, Plus, Smile } from 'lucide-react'
import * as React from 'react'
import { Button } from '@/components/ui/button'
import {
  AnimatePresence,
  motion,
  PopButton,
  StaggerContainer,
  StaggerItem,
  spring,
} from '@/lib/animations'
import { cn } from '@/lib/utils'
import type { Reaction } from './ChatMessages'

interface MessageReactionsProps {
  reactions?: Reaction[]
  messageId: string
  onAddReaction?: (messageId: string, emoji: string) => void
  onRemoveReaction?: (messageId: string, reactionId: string) => void
  onReply?: (messageId: string) => void
  className?: string
}

const POPULAR_EMOJIS = ['*', '+', '-', '?', '!', '#', '@', '%']

export function MessageReactions({
  reactions = [],
  messageId,
  onAddReaction,
  onRemoveReaction,
  onReply,
  className,
}: MessageReactionsProps) {
  const [showEmojiPicker, setShowEmojiPicker] = React.useState(false)

  // Group reactions by emoji
  const groupedReactions = React.useMemo(() => {
    const grouped = new Map<string, { emoji: string; users: Reaction[]; count: number }>()

    reactions.forEach((reaction) => {
      const existing = grouped.get(reaction.emoji)
      if (existing) {
        existing.users.push(reaction)
        existing.count += 1
      } else {
        grouped.set(reaction.emoji, {
          emoji: reaction.emoji,
          users: [reaction],
          count: 1,
        })
      }
    })

    return Array.from(grouped.values()).sort((a, b) => b.count - a.count)
  }, [reactions])

  const handleEmojiClick = React.useCallback(
    (emoji: string) => {
      // Check if current user already reacted with this emoji
      const existingReaction = reactions.find(
        (r) => r.emoji === emoji && r.userId === 'current-user' // Mock current user
      )

      if (existingReaction) {
        onRemoveReaction?.(messageId, existingReaction.id)
      } else {
        onAddReaction?.(messageId, emoji)
      }

      setShowEmojiPicker(false)
    },
    [reactions, messageId, onAddReaction, onRemoveReaction]
  )

  const hasReactions = groupedReactions.length > 0

  return (
    <motion.div
      className={cn('flex items-center gap-1 mt-2', className)}
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={spring.gentle}
    >
      {/* Existing Reactions */}
      <AnimatePresence>
        {hasReactions && (
          <StaggerContainer className="flex items-center gap-1" staggerDelay={0.05}>
            {groupedReactions.map(({ emoji, users, count }) => {
              const currentUserReacted = users.some((u) => u.userId === 'current-user')

              return (
                <StaggerItem key={emoji}>
                  <motion.div
                    initial={{ scale: 0, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    exit={{ scale: 0, opacity: 0 }}
                    transition={spring.bouncy}
                  >
                    <PopButton
                      className={cn(
                        'h-7 px-2 py-1 text-xs rounded-full border transition-all duration-200',
                        'flex items-center gap-1 min-w-[2rem]',
                        currentUserReacted
                          ? 'bg-primary/10 border-primary/30 text-primary hover:bg-primary/20'
                          : 'bg-muted/50 border-border hover:bg-muted'
                      )}
                      onClick={() => handleEmojiClick(emoji)}
                      whileHover={{ scale: 1.008 }}
                      whileTap={{ scale: 0.992 }}
                      title={users.map((u) => u.username).join(', ')}
                    >
                      <motion.span
                        animate={currentUserReacted ? { scale: [1, 1.015, 1] } : {}}
                        transition={{ duration: 0.4, ease: [0.37, 0, 0.63, 1] }}
                      >
                        {emoji}
                      </motion.span>
                      {count > 1 && (
                        <motion.span
                          className="text-xs font-medium"
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          transition={{ delay: 0.1 }}
                        >
                          {count}
                        </motion.span>
                      )}
                    </PopButton>
                  </motion.div>
                </StaggerItem>
              )
            })}
          </StaggerContainer>
        )}
      </AnimatePresence>

      {/* Action Buttons */}
      <div className="flex items-center gap-1 ml-2">
        {/* Add Reaction Button */}
        <div className="relative">
          <PopButton
            className="h-7 w-7 p-0 rounded-full border border-dashed border-muted-foreground/30 hover:border-primary/50 hover:bg-primary/5 transition-all duration-200"
            onClick={() => setShowEmojiPicker(!showEmojiPicker)}
            whileHover={{ scale: 1.008 }}
            whileTap={{ scale: 0.992 }}
            title="Add reaction"
          >
            <motion.div animate={{ rotate: showEmojiPicker ? 45 : 0 }} transition={spring.snappy}>
              {showEmojiPicker ? <Plus className="h-3 w-3" /> : <Smile className="h-3 w-3" />}
            </motion.div>
          </PopButton>

          {/* Emoji Picker */}
          <AnimatePresence>
            {showEmojiPicker && (
              <motion.div
                className="absolute bottom-full mb-2 left-0 bg-background border border-border rounded-lg shadow-lg p-2 z-10"
                initial={{ opacity: 0, scale: 0.9, y: 10 }}
                animate={{ opacity: 1, scale: 1, y: 0 }}
                exit={{ opacity: 0, scale: 0.9, y: 10 }}
                transition={spring.bouncy}
              >
                <StaggerContainer className="grid grid-cols-4 gap-1" staggerDelay={0.02}>
                  {POPULAR_EMOJIS.map((emoji, index) => (
                    <StaggerItem key={emoji}>
                      <PopButton
                        className="h-8 w-8 p-0 text-lg hover:bg-accent rounded transition-colors"
                        onClick={() => handleEmojiClick(emoji)}
                        whileHover={{ scale: 1.015 }}
                        whileTap={{ scale: 0.985 }}
                      >
                        {emoji}
                      </PopButton>
                    </StaggerItem>
                  ))}
                </StaggerContainer>
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Reply Button */}
        <PopButton
          className="h-7 w-7 p-0 rounded-full hover:bg-accent transition-colors"
          onClick={() => onReply?.(messageId)}
          whileHover={{ scale: 1.008 }}
          whileTap={{ scale: 0.992 }}
          title="Reply to message"
        >
          <MessageSquare className="h-3 w-3" />
        </PopButton>
      </div>

      {/* Click outside to close emoji picker */}
      {showEmojiPicker && (
        <motion.div
          className="fixed inset-0 z-0"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          onClick={() => setShowEmojiPicker(false)}
        />
      )}
    </motion.div>
  )
}
