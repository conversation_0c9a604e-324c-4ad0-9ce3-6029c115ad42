import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Sun,
  User,
  Volume2,
  VolumeX,
  Zap,
} from 'lucide-react'
import * as React from 'react'
import { Button } from '@/components/ui/button'
import {
  AnimatePresence,
  motion,
  PopButton,
  StaggerContainer,
  StaggerItem,
  spring,
} from '@/lib/animations'
import { cn } from '@/lib/utils'

export interface PersonalizationSettings {
  theme: {
    mode: 'light' | 'dark' | 'system'
    accentColor: string
    borderRadius: 'none' | 'small' | 'medium' | 'large'
    density: 'compact' | 'comfortable' | 'spacious'
  }
  ai: {
    personality: 'professional' | 'friendly' | 'analytical' | 'creative' | 'mentor'
    responseStyle: 'concise' | 'detailed' | 'conversational'
    codeStyle: 'minimal' | 'commented' | 'educational'
    language: 'english' | 'spanish' | 'french' | 'german' | 'chinese'
  }
  interface: {
    showAnimations: boolean
    soundEffects: boolean
    compactMode: boolean
    showAvatars: boolean
    messageTimestamps: boolean
    quickResponses: boolean
  }
  accessibility: {
    reducedMotion: boolean
    highContrast: boolean
    largeText: boolean
    screenReader: boolean
  }
}

interface PersonalizationPanelProps {
  isOpen: boolean
  onClose: () => void
  settings: PersonalizationSettings
  onSettingsChange: (settings: PersonalizationSettings) => void
  className?: string
  renderMode?: 'modal' | 'inline'
}

const ACCENT_COLORS = [
  { name: 'Blue', value: '#3b82f6', class: 'bg-blue-500' },
  { name: 'Purple', value: '#8b5cf6', class: 'bg-purple-500' },
  { name: 'Green', value: '#10b981', class: 'bg-green-500' },
  { name: 'Orange', value: '#f59e0b', class: 'bg-orange-500' },
  { name: 'Red', value: '#ef4444', class: 'bg-red-500' },
  { name: 'Pink', value: '#ec4899', class: 'bg-pink-500' },
  { name: 'Indigo', value: '#6366f1', class: 'bg-indigo-500' },
  { name: 'Teal', value: '#14b8a6', class: 'bg-teal-500' },
]

const AI_PERSONALITIES = [
  {
    id: 'professional',
    name: 'Professional',
    description: 'Formal, precise, and business-focused responses',
    icon: '',
    traits: ['Formal tone', 'Structured answers', 'Business context'],
  },
  {
    id: 'friendly',
    name: 'Friendly',
    description: 'Warm, approachable, and conversational',
    icon: '',
    traits: ['Casual tone', 'Encouraging', 'Personal touch'],
  },
  {
    id: 'analytical',
    name: 'Analytical',
    description: 'Data-driven, logical, and methodical',
    icon: '',
    traits: ['Evidence-based', 'Detailed analysis', 'Systematic'],
  },
  {
    id: 'creative',
    name: 'Creative',
    description: 'Imaginative, innovative, and inspiring',
    icon: '',
    traits: ['Out-of-box thinking', 'Creative solutions', 'Inspiring'],
  },
  {
    id: 'mentor',
    name: 'Mentor',
    description: 'Teaching-focused, patient, and educational',
    icon: '',
    traits: ['Educational', 'Step-by-step', 'Patient guidance'],
  },
]

export function PersonalizationPanel({
  isOpen,
  onClose,
  settings,
  onSettingsChange,
  className,
  renderMode = 'modal',
}: PersonalizationPanelProps) {
  const [activeTab, setActiveTab] = React.useState<'theme' | 'ai' | 'interface' | 'accessibility'>(
    'theme'
  )

  const updateSettings = React.useCallback(
    (path: string, value: any) => {
      const pathParts = path.split('.')
      const newSettings = { ...settings }

      let current: any = newSettings
      for (let i = 0; i < pathParts.length - 1; i++) {
        current = current[pathParts[i]]
      }
      current[pathParts[pathParts.length - 1]] = value

      onSettingsChange(newSettings)
    },
    [settings, onSettingsChange]
  )

  const tabs = [
    { id: 'theme', label: 'Theme', icon: Palette },
    { id: 'ai', label: 'AI Behavior', icon: Bot },
    { id: 'interface', label: 'Interface', icon: Settings },
    { id: 'accessibility', label: 'Accessibility', icon: User },
  ]

  // Apply theme changes to CSS variables
  React.useEffect(() => {
    const root = document.documentElement

    // Apply accent color
    if (settings.theme.accentColor) {
      root.style.setProperty('--color-primary', settings.theme.accentColor)
    }

    // Apply border radius
    const radiusValues = {
      none: '0px',
      small: '0.25rem',
      medium: '0.5rem',
      large: '1rem',
    }
    root.style.setProperty('--radius', radiusValues[settings.theme.borderRadius])

    // Apply density
    const densityClass = `density-${settings.theme.density}`
    document.body.classList.remove('density-compact', 'density-comfortable', 'density-spacious')
    document.body.classList.add(densityClass)

    // Apply theme mode
    if (settings.theme.mode === 'dark') {
      document.documentElement.classList.add('dark')
    } else if (settings.theme.mode === 'light') {
      document.documentElement.classList.remove('dark')
    } else {
      // System preference
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
      if (prefersDark) {
        document.documentElement.classList.add('dark')
      } else {
        document.documentElement.classList.remove('dark')
      }
    }
  }, [settings.theme])

  const handleKeyDown = React.useCallback(
    (e: React.KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose()
      }
    },
    [onClose]
  )

  if (!isOpen && renderMode === 'modal') {
    return null
  }

  // Shared content component
  const PersonalizationContent = (
    <motion.div
      className={cn(
        'bg-background border border-border rounded-lg shadow-xl flex flex-col overflow-hidden',
        renderMode === 'modal' ? 'w-full max-w-4xl max-h-[85vh]' : 'h-full',
        className
      )}
      initial={renderMode === 'modal' ? { scale: 0.9, y: -20, opacity: 0 } : { opacity: 0 }}
      animate={renderMode === 'modal' ? { scale: 1, y: 0, opacity: 1 } : { opacity: 1 }}
      exit={renderMode === 'modal' ? { scale: 0.9, y: -20, opacity: 0 } : { opacity: 0 }}
      transition={spring.bouncy}
    >
      {/* Header */}
      <div className="p-6 border-b border-border">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <motion.div
              animate={{ rotate: [0, 180, 360] }}
              transition={{ duration: 3, repeat: Infinity, ease: 'linear' }}
            >
              <Settings className="h-6 w-6 text-primary" />
            </motion.div>
            <h2 className="text-xl font-semibold">Personalization</h2>
          </div>

          {renderMode === 'modal' && (
            <PopButton className="p-2 hover:bg-accent rounded-md" onClick={onClose}>
              <Settings className="h-4 w-4 rotate-45" />
            </PopButton>
          )}
        </div>

        {/* Tabs */}
        <div className="flex gap-1 mt-4 p-1 bg-muted rounded-lg">
          {tabs.map((tab) => {
            const Icon = tab.icon
            const isActive = activeTab === tab.id

            return (
              <motion.button
                key={tab.id}
                className={cn(
                  'flex items-center gap-2 px-3 py-2 rounded-md text-sm font-medium transition-all',
                  isActive
                    ? 'bg-background text-foreground shadow-sm'
                    : 'text-muted-foreground hover:text-foreground hover:bg-background/50'
                )}
                onClick={() => setActiveTab(tab.id as any)}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Icon className="h-4 w-4" />
                {tab.label}
              </motion.button>
            )
          })}
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-6">
        <AnimatePresence mode="wait">
          {activeTab === 'theme' && (
            <motion.div
              key="theme"
              className="space-y-6"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={spring.gentle}
            >
              {/* Theme Mode */}
              <div>
                <h3 className="text-lg font-semibold mb-3">Theme Mode</h3>
                <div className="grid grid-cols-3 gap-3">
                  {[
                    { id: 'light', label: 'Light', icon: Sun },
                    { id: 'dark', label: 'Dark', icon: Moon },
                    { id: 'system', label: 'System', icon: Monitor },
                  ].map((mode) => {
                    const Icon = mode.icon
                    const isSelected = settings.theme.mode === mode.id

                    return (
                      <motion.button
                        key={mode.id}
                        className={cn(
                          'flex flex-col items-center gap-2 p-4 rounded-lg border transition-all',
                          isSelected
                            ? 'border-primary bg-primary/10'
                            : 'border-border hover:border-primary/50'
                        )}
                        onClick={() => updateSettings('theme.mode', mode.id)}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <Icon className="h-6 w-6" />
                        <span className="text-sm font-medium">{mode.label}</span>
                      </motion.button>
                    )
                  })}
                </div>
              </div>

              {/* Accent Color */}
              <div>
                <h3 className="text-lg font-semibold mb-3">Accent Color</h3>
                <div className="grid grid-cols-4 gap-3">
                  {ACCENT_COLORS.map((color) => {
                    const isSelected = settings.theme.accentColor === color.value

                    return (
                      <motion.button
                        key={color.value}
                        className={cn(
                          'flex items-center gap-3 p-3 rounded-lg border transition-all',
                          isSelected
                            ? 'border-primary bg-primary/10'
                            : 'border-border hover:border-primary/50'
                        )}
                        onClick={() => updateSettings('theme.accentColor', color.value)}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <div className={cn('w-4 h-4 rounded-full', color.class)} />
                        <span className="text-sm">{color.name}</span>
                      </motion.button>
                    )
                  })}
                </div>
              </div>

              {/* Border Radius */}
              <div>
                <h3 className="text-lg font-semibold mb-3">Border Radius</h3>
                <div className="grid grid-cols-4 gap-3">
                  {[
                    { id: 'none', label: 'None', preview: 'square' },
                    { id: 'small', label: 'Small', preview: 'rounded-sm' },
                    { id: 'medium', label: 'Medium', preview: 'rounded-md' },
                    { id: 'large', label: 'Large', preview: 'rounded-lg' },
                  ].map((radius) => {
                    const isSelected = settings.theme.borderRadius === radius.id

                    return (
                      <motion.button
                        key={radius.id}
                        className={cn(
                          'flex flex-col items-center gap-2 p-4 rounded-lg border transition-all',
                          isSelected
                            ? 'border-primary bg-primary/10'
                            : 'border-border hover:border-primary/50'
                        )}
                        onClick={() => updateSettings('theme.borderRadius', radius.id)}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <div className={cn('w-8 h-8 bg-primary/20', radius.preview)} />
                        <span className="text-sm">{radius.label}</span>
                      </motion.button>
                    )
                  })}
                </div>
              </div>

              {/* Density */}
              <div>
                <h3 className="text-lg font-semibold mb-3">Interface Density</h3>
                <div className="grid grid-cols-3 gap-3">
                  {[
                    { id: 'compact', label: 'Compact', desc: 'More content, less spacing' },
                    { id: 'comfortable', label: 'Comfortable', desc: 'Balanced spacing' },
                    { id: 'spacious', label: 'Spacious', desc: 'More breathing room' },
                  ].map((density) => {
                    const isSelected = settings.theme.density === density.id

                    return (
                      <motion.button
                        key={density.id}
                        className={cn(
                          'flex flex-col items-start gap-1 p-4 rounded-lg border transition-all text-left',
                          isSelected
                            ? 'border-primary bg-primary/10'
                            : 'border-border hover:border-primary/50'
                        )}
                        onClick={() => updateSettings('theme.density', density.id)}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <span className="font-medium">{density.label}</span>
                        <span className="text-xs text-muted-foreground">{density.desc}</span>
                      </motion.button>
                    )
                  })}
                </div>
              </div>
            </motion.div>
          )}

          {activeTab === 'ai' && (
            <motion.div
              key="ai"
              className="space-y-6"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={spring.gentle}
            >
              {/* AI Personality */}
              <div>
                <h3 className="text-lg font-semibold mb-3">AI Personality</h3>
                <div className="grid grid-cols-1 gap-3">
                  {AI_PERSONALITIES.map((personality) => {
                    const isSelected = settings.ai.personality === personality.id

                    return (
                      <motion.button
                        key={personality.id}
                        className={cn(
                          'flex items-start gap-4 p-4 rounded-lg border transition-all text-left',
                          isSelected
                            ? 'border-primary bg-primary/10'
                            : 'border-border hover:border-primary/50'
                        )}
                        onClick={() => updateSettings('ai.personality', personality.id)}
                        whileHover={{ scale: 1.01 }}
                        whileTap={{ scale: 0.99 }}
                      >
                        <div className="flex-1">
                          <h4 className="font-medium">{personality.name}</h4>
                          <p className="text-sm text-muted-foreground mb-2">
                            {personality.description}
                          </p>
                          <div className="flex flex-wrap gap-1">
                            {personality.traits.map((trait) => (
                              <span key={trait} className="text-xs bg-muted px-2 py-1 rounded-full">
                                {trait}
                              </span>
                            ))}
                          </div>
                        </div>
                      </motion.button>
                    )
                  })}
                </div>
              </div>

              {/* Response Style */}
              <div>
                <h3 className="text-lg font-semibold mb-3">Response Style</h3>
                <div className="grid grid-cols-3 gap-3">
                  {[
                    { id: 'concise', label: 'Concise', desc: 'Brief and to the point' },
                    { id: 'detailed', label: 'Detailed', desc: 'Comprehensive explanations' },
                    {
                      id: 'conversational',
                      label: 'Conversational',
                      desc: 'Natural dialogue style',
                    },
                  ].map((style) => {
                    const isSelected = settings.ai.responseStyle === style.id

                    return (
                      <motion.button
                        key={style.id}
                        className={cn(
                          'flex flex-col items-start gap-1 p-4 rounded-lg border transition-all text-left',
                          isSelected
                            ? 'border-primary bg-primary/10'
                            : 'border-border hover:border-primary/50'
                        )}
                        onClick={() => updateSettings('ai.responseStyle', style.id)}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <span className="font-medium">{style.label}</span>
                        <span className="text-xs text-muted-foreground">{style.desc}</span>
                      </motion.button>
                    )
                  })}
                </div>
              </div>

              {/* Code Style */}
              <div>
                <h3 className="text-lg font-semibold mb-3">Code Examples</h3>
                <div className="grid grid-cols-3 gap-3">
                  {[
                    { id: 'minimal', label: 'Minimal', desc: 'Clean, uncommented code' },
                    { id: 'commented', label: 'Commented', desc: 'Code with explanations' },
                    { id: 'educational', label: 'Educational', desc: 'Step-by-step breakdown' },
                  ].map((style) => {
                    const isSelected = settings.ai.codeStyle === style.id

                    return (
                      <motion.button
                        key={style.id}
                        className={cn(
                          'flex flex-col items-start gap-1 p-4 rounded-lg border transition-all text-left',
                          isSelected
                            ? 'border-primary bg-primary/10'
                            : 'border-border hover:border-primary/50'
                        )}
                        onClick={() => updateSettings('ai.codeStyle', style.id)}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <span className="font-medium">{style.label}</span>
                        <span className="text-xs text-muted-foreground">{style.desc}</span>
                      </motion.button>
                    )
                  })}
                </div>
              </div>
            </motion.div>
          )}

          {activeTab === 'interface' && (
            <motion.div
              key="interface"
              className="space-y-6"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={spring.gentle}
            >
              {/* Interface Toggles */}
              <div className="space-y-4">
                {[
                  {
                    key: 'showAnimations',
                    label: 'Animations',
                    desc: 'Enable smooth animations and transitions',
                    icon: Zap,
                  },
                  {
                    key: 'soundEffects',
                    label: 'Sound Effects',
                    desc: 'Play sounds for notifications and actions',
                    icon: Volume2,
                  },
                  {
                    key: 'compactMode',
                    label: 'Compact Mode',
                    desc: 'Reduce spacing for more content',
                    icon: Settings,
                  },
                  {
                    key: 'showAvatars',
                    label: 'Show Avatars',
                    desc: 'Display user and AI avatars',
                    icon: User,
                  },
                  {
                    key: 'messageTimestamps',
                    label: 'Message Timestamps',
                    desc: 'Show when messages were sent',
                    icon: Settings,
                  },
                  {
                    key: 'quickResponses',
                    label: 'Quick Responses',
                    desc: 'Show contextual response suggestions',
                    icon: Zap,
                  },
                ].map((option) => {
                  const Icon = option.icon
                  const isEnabled =
                    settings.interface[option.key as keyof typeof settings.interface]

                  return (
                    <motion.div
                      key={option.key}
                      className="flex items-center justify-between p-4 rounded-lg border border-border hover:border-primary/50 transition-colors"
                      whileHover={{ scale: 1.01 }}
                    >
                      <div className="flex items-center gap-3">
                        <Icon className="h-5 w-5 text-muted-foreground" />
                        <div>
                          <h4 className="font-medium">{option.label}</h4>
                          <p className="text-sm text-muted-foreground">{option.desc}</p>
                        </div>
                      </div>

                      <motion.button
                        className={cn(
                          'relative w-11 h-6 rounded-full transition-colors',
                          isEnabled ? 'bg-primary' : 'bg-muted'
                        )}
                        onClick={() => updateSettings(`interface.${option.key}`, !isEnabled)}
                        whileTap={{ scale: 0.95 }}
                      >
                        <motion.div
                          className="w-4 h-4 bg-white rounded-full shadow-sm"
                          animate={{ x: isEnabled ? 26 : 2, y: 4 }}
                          transition={spring.snappy}
                        />
                      </motion.button>
                    </motion.div>
                  )
                })}
              </div>
            </motion.div>
          )}

          {activeTab === 'accessibility' && (
            <motion.div
              key="accessibility"
              className="space-y-6"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={spring.gentle}
            >
              {/* Accessibility Toggles */}
              <div className="space-y-4">
                {[
                  {
                    key: 'reducedMotion',
                    label: 'Reduced Motion',
                    desc: 'Minimize animations for better accessibility',
                  },
                  {
                    key: 'highContrast',
                    label: 'High Contrast',
                    desc: 'Increase contrast for better visibility',
                  },
                  {
                    key: 'largeText',
                    label: 'Large Text',
                    desc: 'Increase text size throughout the interface',
                  },
                  {
                    key: 'screenReader',
                    label: 'Screen Reader Support',
                    desc: 'Optimize for screen reader usage',
                  },
                ].map((option) => {
                  const isEnabled =
                    settings.accessibility[option.key as keyof typeof settings.accessibility]

                  return (
                    <motion.div
                      key={option.key}
                      className="flex items-center justify-between p-4 rounded-lg border border-border hover:border-primary/50 transition-colors"
                      whileHover={{ scale: 1.01 }}
                    >
                      <div>
                        <h4 className="font-medium">{option.label}</h4>
                        <p className="text-sm text-muted-foreground">{option.desc}</p>
                      </div>

                      <motion.button
                        className={cn(
                          'relative w-11 h-6 rounded-full transition-colors',
                          isEnabled ? 'bg-primary' : 'bg-muted'
                        )}
                        onClick={() => updateSettings(`accessibility.${option.key}`, !isEnabled)}
                        whileTap={{ scale: 0.95 }}
                      >
                        <motion.div
                          className="w-4 h-4 bg-white rounded-full shadow-sm"
                          animate={{ x: isEnabled ? 26 : 2, y: 4 }}
                          transition={spring.snappy}
                        />
                      </motion.button>
                    </motion.div>
                  )
                })}
              </div>

              {/* Accessibility Note */}
              <div className="p-4 bg-muted/50 rounded-lg">
                <h4 className="font-medium mb-2">Accessibility Features</h4>
                <p className="text-sm text-muted-foreground">
                  These settings help make the interface more accessible. Changes are applied
                  immediately and persist across sessions.
                </p>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Footer */}
      <motion.div
        className="p-4 border-t border-border bg-muted/30"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.3 }}
      >
        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <span>Settings are saved automatically</span>
          {renderMode === 'modal' && <span>Press Esc to close</span>}
        </div>
      </motion.div>
    </motion.div>
  )

  // Return appropriate wrapper based on render mode
  if (renderMode === 'inline') {
    return (
      <div className="h-full flex flex-col" onKeyDown={handleKeyDown}>
        {PersonalizationContent}
      </div>
    )
  }

  // Modal mode
  return (
    <motion.div
      className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={spring.gentle}
      onClick={(e) => e.target === e.currentTarget && onClose()}
      onKeyDown={handleKeyDown}
    >
      {PersonalizationContent}
    </motion.div>
  )
}
