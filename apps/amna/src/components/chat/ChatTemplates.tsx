import {
  Clock,
  Code,
  Edit3,
  <PERSON>Text,
  Hash,
  Lightbulb,
  MessageSquare,
  Plus,
  Star,
  Trash2,
} from 'lucide-react'
import * as React from 'react'
import { Button } from '@/components/ui/button'
import {
  AnimatePresence,
  motion,
  PopButton,
  StaggerContainer,
  StaggerItem,
  spring,
} from '@/lib/animations'
import { cn } from '@/lib/utils'

export interface ChatTemplate {
  id: string
  title: string
  content: string
  category: 'quick' | 'technical' | 'creative' | 'business' | 'custom'
  icon: React.ComponentType<{ className?: string }>
  description?: string
  keywords?: string[]
  usageCount?: number
  isFavorite?: boolean
  lastUsed?: Date
}

interface ChatTemplatesProps {
  isOpen: boolean
  onClose: () => void
  onSelectTemplate: (template: ChatTemplate) => void
  onCreateTemplate?: (template: Omit<ChatTemplate, 'id' | 'usageCount' | 'lastUsed'>) => void
  onEditTemplate?: (template: ChatTemplate) => void
  onDeleteTemplate?: (templateId: string) => void
  className?: string
  renderMode?: 'modal' | 'inline'
}

const DEFAULT_TEMPLATES: ChatTemplate[] = [
  // Quick Response Templates
  {
    id: 'quick-thanks',
    title: 'Thank you',
    content: 'Thank you for your help! This was very useful.',
    category: 'quick',
    icon: MessageSquare,
    description: 'Express gratitude',
    keywords: ['thanks', 'gratitude', 'appreciation'],
    usageCount: 42,
    isFavorite: true,
    lastUsed: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
  },
  {
    id: 'quick-clarify',
    title: 'Need clarification',
    content:
      'Could you please clarify what you mean by "{selection}"? I want to make sure I understand correctly.',
    category: 'quick',
    icon: MessageSquare,
    description: 'Ask for clarification',
    keywords: ['clarify', 'explain', 'understand'],
    usageCount: 28,
    lastUsed: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
  },
  {
    id: 'quick-more-info',
    title: 'Request more information',
    content:
      "This is helpful! Can you provide more details about {topic}? Specifically, I'd like to know about:\n\n1. \n2. \n3. ",
    category: 'quick',
    icon: MessageSquare,
    description: 'Ask for additional details',
    keywords: ['details', 'more', 'information'],
    usageCount: 35,
  },

  // Technical Templates
  {
    id: 'tech-debug',
    title: 'Debug assistance',
    content:
      "I'm experiencing an issue with my code. Here's what I'm trying to do:\n\n**Goal:** \n\n**Current code:**\n```\n\n```\n\n**Error message:**\n```\n\n```\n\n**What I've tried:**\n- \n\nCan you help me identify what's wrong?",
    category: 'technical',
    icon: Code,
    description: 'Get help with debugging code',
    keywords: ['debug', 'error', 'code', 'fix'],
    usageCount: 67,
    isFavorite: true,
  },
  {
    id: 'tech-review',
    title: 'Code review request',
    content:
      'Could you review this code and provide feedback on:\n\n- Code quality and best practices\n- Performance optimizations\n- Security considerations\n- Maintainability\n\n```{language}\n{code}\n```',
    category: 'technical',
    icon: Code,
    description: 'Request code review',
    keywords: ['review', 'feedback', 'quality'],
    usageCount: 23,
  },
  {
    id: 'tech-explain',
    title: 'Explain concept',
    content:
      "Can you explain {concept} in simple terms? I'm particularly interested in:\n\n- How it works\n- When to use it\n- Common pitfalls\n- Real-world examples\n\nPlease include code examples if applicable.",
    category: 'technical',
    icon: Code,
    description: 'Get technical explanations',
    keywords: ['explain', 'concept', 'understand'],
    usageCount: 45,
  },

  // Creative Templates
  {
    id: 'creative-brainstorm',
    title: 'Brainstorming session',
    content:
      "I need creative ideas for {project/topic}. Here's the context:\n\n**Goal:** \n**Target audience:** \n**Constraints:** \n**Current ideas:** \n\nCan you help me brainstorm some innovative approaches?",
    category: 'creative',
    icon: Lightbulb,
    description: 'Generate creative ideas',
    keywords: ['ideas', 'brainstorm', 'creative'],
    usageCount: 19,
  },
  {
    id: 'creative-writing',
    title: 'Writing assistance',
    content:
      "I'm working on {type of writing} and need help with:\n\n**Topic:** \n**Tone:** \n**Length:** \n**Target audience:** \n**Key points to cover:** \n- \n- \n\nCan you help me craft this content?",
    category: 'creative',
    icon: FileText,
    description: 'Get writing help',
    keywords: ['writing', 'content', 'draft'],
    usageCount: 31,
  },

  // Business Templates
  {
    id: 'business-analysis',
    title: 'Business analysis',
    content:
      "I need to analyze {business topic}. Here's what I'm looking at:\n\n**Background:** \n**Current situation:** \n**Goals:** \n**Challenges:** \n\nCan you help me:\n1. Identify key insights\n2. Suggest actionable recommendations\n3. Highlight potential risks",
    category: 'business',
    icon: Hash,
    description: 'Business analysis and insights',
    keywords: ['analysis', 'business', 'strategy'],
    usageCount: 15,
  },
]

export function ChatTemplates({
  isOpen,
  onClose,
  onSelectTemplate,
  onCreateTemplate,
  onEditTemplate,
  onDeleteTemplate,
  className,
  renderMode = 'modal',
}: ChatTemplatesProps) {
  const [templates, setTemplates] = React.useState<ChatTemplate[]>(DEFAULT_TEMPLATES)
  const [selectedCategory, setSelectedCategory] = React.useState<string>('all')
  const [searchQuery, setSearchQuery] = React.useState('')
  const [sortBy, setSortBy] = React.useState<'recent' | 'popular' | 'alphabetical'>('recent')
  const [isCreating, setIsCreating] = React.useState(false)

  const categories = [
    { id: 'all', label: 'All Templates', icon: MessageSquare },
    { id: 'quick', label: 'Quick Responses', icon: Clock },
    { id: 'technical', label: 'Technical', icon: Code },
    { id: 'creative', label: 'Creative', icon: Lightbulb },
    { id: 'business', label: 'Business', icon: Hash },
    { id: 'custom', label: 'Custom', icon: Star },
  ]

  // Filter and sort templates
  const filteredTemplates = React.useMemo(() => {
    const filtered = templates.filter((template) => {
      // Category filter
      if (selectedCategory !== 'all' && template.category !== selectedCategory) {
        return false
      }

      // Search filter
      if (searchQuery) {
        const query = searchQuery.toLowerCase()
        return (
          template.title.toLowerCase().includes(query) ||
          template.content.toLowerCase().includes(query) ||
          template.description?.toLowerCase().includes(query) ||
          template.keywords?.some((keyword) => keyword.toLowerCase().includes(query))
        )
      }

      return true
    })

    // Sort templates
    switch (sortBy) {
      case 'recent':
        filtered.sort((a, b) => {
          const aTime = a.lastUsed?.getTime() || 0
          const bTime = b.lastUsed?.getTime() || 0
          return bTime - aTime
        })
        break
      case 'popular':
        filtered.sort((a, b) => (b.usageCount || 0) - (a.usageCount || 0))
        break
      case 'alphabetical':
        filtered.sort((a, b) => a.title.localeCompare(b.title))
        break
    }

    // Favorites first
    filtered.sort((a, b) => {
      if (a.isFavorite && !b.isFavorite) {
        return -1
      }
      if (!a.isFavorite && b.isFavorite) {
        return 1
      }
      return 0
    })

    return filtered
  }, [templates, selectedCategory, searchQuery, sortBy])

  const handleSelectTemplate = React.useCallback(
    (template: ChatTemplate) => {
      // Update usage statistics
      setTemplates((prev) =>
        prev.map((t) =>
          t.id === template.id
            ? { ...t, usageCount: (t.usageCount || 0) + 1, lastUsed: new Date() }
            : t
        )
      )

      onSelectTemplate(template)
      onClose()
    },
    [onSelectTemplate, onClose]
  )

  const handleToggleFavorite = React.useCallback((templateId: string) => {
    setTemplates((prev) =>
      prev.map((t) => (t.id === templateId ? { ...t, isFavorite: !t.isFavorite } : t))
    )
  }, [])

  const formatLastUsed = React.useCallback((date?: Date) => {
    if (!date) {
      return 'Never used'
    }

    const now = new Date()
    const diff = now.getTime() - date.getTime()
    const minutes = Math.floor(diff / (1000 * 60))
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))

    if (minutes < 60) {
      return `${minutes}m ago`
    }
    if (hours < 24) {
      return `${hours}h ago`
    }
    if (days < 30) {
      return `${days}d ago`
    }
    return date.toLocaleDateString()
  }, [])

  // Keyboard navigation
  const handleKeyDown = React.useCallback(
    (e: React.KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose()
      }
    },
    [onClose]
  )

  if (!isOpen && renderMode === 'modal') {
    return null
  }

  // Shared content component
  const TemplatesContent = (
    <motion.div
      className={cn(
        'bg-background border border-border rounded-lg shadow-xl flex flex-col overflow-hidden',
        renderMode === 'modal' ? 'w-full max-w-4xl max-h-[80vh]' : 'h-full',
        className
      )}
      initial={renderMode === 'modal' ? { scale: 0.9, y: -20, opacity: 0 } : { opacity: 0 }}
      animate={renderMode === 'modal' ? { scale: 1, y: 0, opacity: 1 } : { opacity: 1 }}
      exit={renderMode === 'modal' ? { scale: 0.9, y: -20, opacity: 0 } : { opacity: 0 }}
      transition={spring.bouncy}
    >
      {/* Header */}
      <div className="p-6 border-b border-border">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <motion.div
              animate={{ rotate: [0, 10, 0] }}
              transition={{ duration: 2, repeat: Infinity, ease: 'easeInOut' }}
            >
              <MessageSquare className="h-6 w-6 text-primary" />
            </motion.div>
            <h2 className="text-xl font-semibold">Chat Templates</h2>
            <span className="text-sm text-muted-foreground">
              {filteredTemplates.length} template{filteredTemplates.length !== 1 ? 's' : ''}
            </span>
          </div>

          <div className="flex items-center gap-2">
            <PopButton
              className="flex items-center gap-2 px-3 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90"
              onClick={() => setIsCreating(true)}
            >
              <Plus className="h-4 w-4" />
              New Template
            </PopButton>

            {renderMode === 'modal' && (
              <PopButton className="p-2 hover:bg-accent rounded-md" onClick={onClose}>
                <Plus className="h-4 w-4 rotate-45" />
              </PopButton>
            )}
          </div>
        </div>

        {/* Search and Filters */}
        <div className="flex items-center gap-4">
          <div className="flex-1">
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="Search templates..."
              className="w-full px-3 py-2 bg-muted/30 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary/50"
            />
          </div>

          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as any)}
            className="px-3 py-2 bg-muted/30 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary/50"
          >
            <option value="recent">Recently Used</option>
            <option value="popular">Most Popular</option>
            <option value="alphabetical">A-Z</option>
          </select>
        </div>
      </div>

      <div className="flex flex-1 overflow-hidden">
        {/* Categories Sidebar */}
        <div className="w-48 border-r border-border p-4">
          <h3 className="text-sm font-medium text-muted-foreground mb-3 uppercase tracking-wide">
            Categories
          </h3>
          <StaggerContainer className="space-y-1" staggerDelay={0.05}>
            {categories.map((category) => {
              const Icon = category.icon
              const isSelected = selectedCategory === category.id
              const count =
                category.id === 'all'
                  ? templates.length
                  : templates.filter((t) => t.category === category.id).length

              return (
                <StaggerItem key={category.id}>
                  <motion.button
                    className={cn(
                      'w-full flex items-center gap-3 px-3 py-2 rounded-md text-sm transition-colors',
                      isSelected
                        ? 'bg-primary/10 text-primary border border-primary/20'
                        : 'hover:bg-accent text-muted-foreground hover:text-foreground'
                    )}
                    onClick={() => setSelectedCategory(category.id)}
                    whileHover={{ x: 2 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Icon className="h-4 w-4" />
                    <span className="flex-1 text-left">{category.label}</span>
                    <span className="text-xs">{count}</span>
                  </motion.button>
                </StaggerItem>
              )
            })}
          </StaggerContainer>
        </div>

        {/* Templates Grid */}
        <div className="flex-1 p-4 overflow-y-auto">
          <AnimatePresence>
            {filteredTemplates.length === 0 ? (
              <motion.div
                className="flex flex-col items-center justify-center h-64 text-muted-foreground"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
              >
                <MessageSquare className="h-8 w-8 mb-2" />
                <p>No templates found</p>
                <p className="text-sm">Try adjusting your search or filters</p>
              </motion.div>
            ) : (
              <StaggerContainer
                className="grid grid-cols-1 md:grid-cols-2 gap-4"
                staggerDelay={0.05}
              >
                {filteredTemplates.map((template) => {
                  const Icon = template.icon

                  return (
                    <StaggerItem key={template.id}>
                      <motion.div
                        className="group border border-border rounded-lg p-4 bg-card hover:shadow-lg transition-all duration-200 cursor-pointer"
                        whileHover={{
                          scale: 1.02,
                          borderColor: 'var(--color-primary)',
                        }}
                        whileTap={{ scale: 0.98 }}
                        onClick={() => handleSelectTemplate(template)}
                      >
                        {/* Template Header */}
                        <div className="flex items-start justify-between mb-3">
                          <div className="flex items-center gap-3">
                            <motion.div
                              className="p-2 bg-primary/10 rounded-md"
                              whileHover={{ scale: 1.1, rotate: 5 }}
                            >
                              <Icon className="h-4 w-4 text-primary" />
                            </motion.div>

                            <div>
                              <h3 className="font-medium text-foreground group-hover:text-primary transition-colors">
                                {template.title}
                              </h3>
                              {template.description && (
                                <p className="text-xs text-muted-foreground">
                                  {template.description}
                                </p>
                              )}
                            </div>
                          </div>

                          <div className="flex items-center gap-1">
                            <motion.button
                              className={cn(
                                'p-1 rounded hover:bg-accent',
                                template.isFavorite ? 'text-yellow-500' : 'text-muted-foreground'
                              )}
                              onClick={(e) => {
                                e.stopPropagation()
                                handleToggleFavorite(template.id)
                              }}
                              whileHover={{ scale: 1.1 }}
                              whileTap={{ scale: 0.9 }}
                            >
                              <Star
                                className={cn('h-3 w-3', template.isFavorite && 'fill-current')}
                              />
                            </motion.button>

                            {onEditTemplate && (
                              <motion.button
                                className="p-1 rounded hover:bg-accent text-muted-foreground opacity-0 group-hover:opacity-100 transition-opacity"
                                onClick={(e) => {
                                  e.stopPropagation()
                                  onEditTemplate(template)
                                }}
                                whileHover={{ scale: 1.1 }}
                                whileTap={{ scale: 0.9 }}
                              >
                                <Edit3 className="h-3 w-3" />
                              </motion.button>
                            )}

                            {onDeleteTemplate && template.category === 'custom' && (
                              <motion.button
                                className="p-1 rounded hover:bg-destructive/10 text-muted-foreground hover:text-destructive opacity-0 group-hover:opacity-100 transition-all"
                                onClick={(e) => {
                                  e.stopPropagation()
                                  onDeleteTemplate(template.id)
                                }}
                                whileHover={{ scale: 1.1 }}
                                whileTap={{ scale: 0.9 }}
                              >
                                <Trash2 className="h-3 w-3" />
                              </motion.button>
                            )}
                          </div>
                        </div>

                        {/* Template Preview */}
                        <div className="mb-3">
                          <p className="text-sm text-muted-foreground line-clamp-3 leading-relaxed">
                            {template.content}
                          </p>
                        </div>

                        {/* Template Stats */}
                        <div className="flex items-center justify-between text-xs text-muted-foreground">
                          <div className="flex items-center gap-4">
                            <span className="capitalize bg-muted px-2 py-1 rounded">
                              {template.category}
                            </span>
                            {template.usageCount && <span>{template.usageCount} uses</span>}
                          </div>
                          <span>{formatLastUsed(template.lastUsed)}</span>
                        </div>

                        {/* Keywords */}
                        {template.keywords && template.keywords.length > 0 && (
                          <div className="mt-2 flex flex-wrap gap-1">
                            {template.keywords.slice(0, 3).map((keyword) => (
                              <span
                                key={keyword}
                                className="text-xs bg-primary/5 text-primary px-2 py-0.5 rounded-full"
                              >
                                {keyword}
                              </span>
                            ))}
                          </div>
                        )}
                      </motion.div>
                    </StaggerItem>
                  )
                })}
              </StaggerContainer>
            )}
          </AnimatePresence>
        </div>
      </div>

      {/* Quick Actions Footer */}
      <motion.div
        className="p-4 border-t border-border bg-muted/30"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.3 }}
      >
        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <div className="flex items-center gap-4">
            <span>Click to use template</span>
            <span>* Favorite templates</span>
          </div>
          {renderMode === 'modal' && <span>Esc to close</span>}
        </div>
      </motion.div>
    </motion.div>
  )

  // Return appropriate wrapper based on render mode
  if (renderMode === 'inline') {
    return (
      <div className="h-full flex flex-col" onKeyDown={handleKeyDown}>
        {TemplatesContent}
      </div>
    )
  }

  // Modal mode
  return (
    <motion.div
      className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={spring.gentle}
      onClick={(e) => e.target === e.currentTarget && onClose()}
      onKeyDown={handleKeyDown}
    >
      {TemplatesContent}
    </motion.div>
  )
}
