import { CheckCircle, Code, Copy, Download, File, FileText, Image, Play } from 'lucide-react'
import * as React from 'react'
import { MotionButton } from '@/components/ui/button'
import {
  AnimatePresence,
  motion,
  PopButton,
  StaggerContainer,
  StaggerItem,
  spring,
} from '@/lib/animations'
import { cn } from '@/lib/utils'

interface RichMediaRendererProps {
  content: string
  messageType?: 'text' | 'code' | 'file' | 'image' | 'table' | 'list' | 'structured'
  metadata?: {
    language?: string
    fileName?: string
    fileSize?: string
    fileType?: string
    imageUrl?: string
    imageAlt?: string
  }
}

export function RichMediaRenderer({
  content,
  messageType = 'text',
  metadata,
}: RichMediaRendererProps) {
  switch (messageType) {
    case 'code':
      return <CodeBlockRenderer content={content} language={metadata?.language} />
    case 'file':
      return <FileRenderer content={content} metadata={metadata} />
    case 'image':
      return <ImageRenderer content={content} metadata={metadata} />
    case 'table':
      return <TableRenderer content={content} />
    case 'list':
      return <ListRenderer content={content} />
    case 'structured':
      return <StructuredRenderer content={content} />
    default:
      return <TextRenderer content={content} />
  }
}

function CodeBlockRenderer({
  content,
  language = 'javascript',
}: {
  content: string
  language?: string
}) {
  const [copied, setCopied] = React.useState(false)
  const [isExpanded, setIsExpanded] = React.useState(false)

  const codeBlocks = content.match(/```(\w+)?\n([\s\S]*?)```/g) || []
  const hasMultipleBlocks = codeBlocks.length > 1

  const handleCopy = async (code: string) => {
    try {
      await navigator.clipboard.writeText(code)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error('Failed to copy code:', err)
    }
  }

  const renderCodeBlock = (block: string, index: number) => {
    const match = block.match(/```(\w+)?\n([\s\S]*?)```/)
    if (!match) {
      return null
    }

    const [, blockLanguage, code] = match
    const cleanCode = code.trim()
    const displayLanguage = blockLanguage || language

    const lineCount = cleanCode.split('\n').length
    const shouldTruncate = lineCount > 20 && !isExpanded

    return (
      <motion.div
        key={index}
        className="relative group border border-border rounded-lg overflow-hidden bg-muted/30 my-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ ...spring.gentle, delay: index * 0.1 }}
      >
        {/* Code Header */}
        <motion.div
          className="flex items-center justify-between px-4 py-2 bg-muted/50 border-b border-border"
          whileHover={{ backgroundColor: 'rgba(var(--muted), 0.8)' }}
        >
          <div className="flex items-center gap-2">
            <motion.div
              animate={{ rotate: [0, 360] }}
              transition={{ duration: 2, repeat: Infinity, ease: 'linear' }}
              className="opacity-60"
            >
              <Code className="h-4 w-4" />
            </motion.div>
            <span className="text-sm font-medium text-muted-foreground">{displayLanguage}</span>
            <span className="text-xs text-muted-foreground/70">{lineCount} lines</span>
          </div>

          <div className="flex items-center gap-2">
            {shouldTruncate && (
              <MotionButton
                variant="ghost"
                size="sm"
                className="h-6 px-2 text-xs"
                onClick={() => setIsExpanded(!isExpanded)}
                animated
              >
                {isExpanded ? 'Collapse' : 'Expand'}
              </MotionButton>
            )}
            <MotionButton
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
              onClick={() => handleCopy(cleanCode)}
              animated
              ripple
            >
              <AnimatePresence mode="wait">
                {copied ? (
                  <motion.div
                    key="check"
                    initial={{ scale: 0, rotate: -90 }}
                    animate={{ scale: 1, rotate: 0 }}
                    exit={{ scale: 0, rotate: 90 }}
                    transition={spring.bouncy}
                  >
                    <CheckCircle className="h-3 w-3 text-green-500" />
                  </motion.div>
                ) : (
                  <motion.div
                    key="copy"
                    initial={{ scale: 0, rotate: -90 }}
                    animate={{ scale: 1, rotate: 0 }}
                    exit={{ scale: 0, rotate: 90 }}
                    transition={spring.bouncy}
                  >
                    <Copy className="h-3 w-3" />
                  </motion.div>
                )}
              </AnimatePresence>
            </MotionButton>
          </div>
        </motion.div>

        {/* Code Content */}
        <motion.div
          className="relative"
          animate={{
            height: shouldTruncate && !isExpanded ? 400 : 'auto',
          }}
          transition={spring.gentle}
        >
          <pre className="overflow-x-auto p-4 text-sm leading-relaxed">
            <code
              className={cn(
                'block',
                // Basic syntax highlighting classes
                displayLanguage === 'javascript' && 'language-javascript',
                displayLanguage === 'typescript' && 'language-typescript',
                displayLanguage === 'jsx' && 'language-jsx',
                displayLanguage === 'css' && 'language-css',
                displayLanguage === 'html' && 'language-html',
                displayLanguage === 'json' && 'language-json'
              )}
            >
              {shouldTruncate && !isExpanded
                ? cleanCode.split('\n').slice(0, 20).join('\n') + '\n...'
                : cleanCode}
            </code>
          </pre>

          {shouldTruncate && !isExpanded && (
            <motion.div
              className="absolute bottom-0 left-0 right-0 h-20 bg-gradient-to-t from-muted/80 to-transparent pointer-events-none"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.5 }}
            />
          )}
        </motion.div>

        {/* Execute Button for runnable code */}
        {(displayLanguage === 'javascript' || displayLanguage === 'typescript') && (
          <motion.div
            className="absolute top-2 right-12 opacity-0 group-hover:opacity-100 transition-opacity"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <MotionButton
              variant="secondary"
              size="sm"
              className="h-6 px-2 text-xs bg-green-100 text-green-700 hover:bg-green-200 dark:bg-green-900 dark:text-green-300"
              animated
            >
              <Play className="h-3 w-3 mr-1" />
              Run
            </MotionButton>
          </motion.div>
        )}
      </motion.div>
    )
  }

  // If content has code blocks, render them
  if (codeBlocks.length > 0) {
    const parts = content.split(/```(\w+)?\n[\s\S]*?```/)
    let blockIndex = 0

    return (
      <div className="space-y-2">
        {parts.map((part, index) => {
          if (part.trim() === '' || part.match(/^\w+$/)) {
            return null
          }

          if (codeBlocks.some((block) => block.includes(part))) {
            return renderCodeBlock(codeBlocks[blockIndex++], index)
          }

          return (
            <motion.div
              key={index}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: index * 0.1 }}
            >
              <TextRenderer content={part} />
            </motion.div>
          )
        })}
      </div>
    )
  }

  // If no code blocks found but messageType is code, wrap entire content
  return renderCodeBlock(`\`\`\`${language}\n${content}\n\`\`\``, 0)
}

function FileRenderer({ content, metadata }: { content: string; metadata?: any }) {
  const fileName = metadata?.fileName || 'document.txt'
  const fileSize = metadata?.fileSize || '1.2 KB'
  const fileType = metadata?.fileType || 'text'

  const getFileIcon = (type: string) => {
    if (type.includes('image')) {
      return <Image className="h-8 w-8 text-blue-500" />
    }
    if (type.includes('text') || type.includes('document')) {
      return <FileText className="h-8 w-8 text-green-500" />
    }
    return <File className="h-8 w-8 text-gray-500" />
  }

  return (
    <motion.div
      className="border border-border rounded-lg p-4 bg-muted/30 my-4 max-w-md"
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={spring.gentle}
      whileHover={{ scale: 1.02, borderColor: 'var(--color-primary)' }}
    >
      <div className="flex items-center gap-3">
        <motion.div whileHover={{ scale: 1.1, rotate: 5 }} transition={spring.snappy}>
          {getFileIcon(fileType)}
        </motion.div>

        <div className="flex-1 min-w-0">
          <motion.h4
            className="font-medium text-sm truncate"
            whileHover={{ color: 'var(--color-primary)' }}
          >
            {fileName}
          </motion.h4>
          <p className="text-xs text-muted-foreground">{fileSize}</p>
        </div>

        <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
          <MotionButton variant="ghost" size="sm" className="h-8 w-8 p-0" animated ripple>
            <Download className="h-4 w-4" />
          </MotionButton>
        </motion.div>
      </div>

      {content && (
        <motion.div
          className="mt-3 text-sm text-muted-foreground border-t border-border pt-3"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          {content}
        </motion.div>
      )}
    </motion.div>
  )
}

function ImageRenderer({ content, metadata }: { content: string; metadata?: any }) {
  const [imageLoaded, setImageLoaded] = React.useState(false)
  const imageUrl =
    metadata?.imageUrl || 'https://picsum.photos/400/300?random=' + Math.floor(Math.random() * 1000)
  const imageAlt = metadata?.imageAlt || 'Generated image'

  return (
    <motion.div
      className="my-4 rounded-lg overflow-hidden border border-border bg-muted/30"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={spring.gentle}
    >
      <div className="relative">
        <AnimatePresence>
          {!imageLoaded && (
            <motion.div
              className="absolute inset-0 flex items-center justify-center bg-muted animate-pulse"
              exit={{ opacity: 0 }}
              transition={{ duration: 0.3 }}
            >
              <Image className="h-12 w-12 text-muted-foreground" />
            </motion.div>
          )}
        </AnimatePresence>

        <motion.img
          src={imageUrl}
          alt={imageAlt}
          className="w-full h-auto max-h-96 object-cover"
          onLoad={() => setImageLoaded(true)}
          initial={{ opacity: 0 }}
          animate={{ opacity: imageLoaded ? 1 : 0 }}
          transition={{ duration: 0.5 }}
          whileHover={{ scale: 1.02 }}
        />
      </div>

      {content && (
        <motion.div
          className="p-3 bg-background/50"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <p className="text-sm text-muted-foreground">{content}</p>
        </motion.div>
      )}
    </motion.div>
  )
}

function TableRenderer({ content }: { content: string }) {
  // Parse markdown tables
  const lines = content.split('\n')
  const tableLines = lines.filter((line) => line.includes('|'))

  if (tableLines.length < 2) {
    return <TextRenderer content={content} />
  }

  const headers = tableLines[0]
    .split('|')
    .map((h) => h.trim())
    .filter((h) => h)
  const separatorLine = tableLines[1]
  const rows = tableLines.slice(2).map((line) =>
    line
      .split('|')
      .map((cell) => cell.trim())
      .filter((cell) => cell)
  )

  return (
    <motion.div
      className="my-4 rounded-lg border border-border overflow-hidden bg-background"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={spring.gentle}
    >
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-muted/50">
            <tr>
              <StaggerContainer className="contents" staggerDelay={0.05}>
                {headers.map((header, index) => (
                  <StaggerItem key={index}>
                    <motion.th
                      className="px-4 py-3 text-left text-sm font-medium text-muted-foreground border-b border-border"
                      whileHover={{ backgroundColor: 'rgba(var(--muted), 0.8)' }}
                    >
                      {header}
                    </motion.th>
                  </StaggerItem>
                ))}
              </StaggerContainer>
            </tr>
          </thead>
          <tbody>
            <StaggerContainer className="contents" staggerDelay={0.05}>
              {rows.map((row, rowIndex) => (
                <StaggerItem key={rowIndex}>
                  <motion.tr
                    className="border-b border-border last:border-b-0"
                    whileHover={{ backgroundColor: 'rgba(var(--muted), 0.3)' }}
                    transition={spring.gentle}
                  >
                    {row.map((cell, cellIndex) => (
                      <motion.td
                        key={cellIndex}
                        className="px-4 py-3 text-sm"
                        initial={{ opacity: 0, x: -10 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: (rowIndex * row.length + cellIndex) * 0.02 }}
                      >
                        {cell}
                      </motion.td>
                    ))}
                  </motion.tr>
                </StaggerItem>
              ))}
            </StaggerContainer>
          </tbody>
        </table>
      </div>
    </motion.div>
  )
}

function ListRenderer({ content }: { content: string }) {
  const lines = content.split('\n')
  const listItems = lines.filter((line) => line.match(/^[-*+]\s+|^\d+\.\s+/))

  if (listItems.length === 0) {
    return <TextRenderer content={content} />
  }

  return (
    <motion.div
      className="my-4"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={spring.gentle}
    >
      <StaggerContainer className="space-y-2" staggerDelay={0.1}>
        {listItems.map((item, index) => {
          const isOrdered = item.match(/^\d+\.\s+/)
          const cleanItem = item.replace(/^[-*+]\s+|^\d+\.\s+/, '')

          return (
            <StaggerItem key={index}>
              <motion.div
                className="flex items-start gap-3 p-2 rounded-md hover:bg-muted/30 transition-colors"
                whileHover={{ x: 4 }}
                transition={spring.snappy}
              >
                <motion.div
                  className="flex-shrink-0 mt-1"
                  animate={{
                    scale: [1, 1.2, 1],
                    rotate: [0, 5, 0],
                  }}
                  transition={{
                    delay: index * 0.1,
                    duration: 0.5,
                  }}
                >
                  {isOrdered ? (
                    <span className="inline-flex items-center justify-center w-6 h-6 text-xs font-medium bg-primary/10 text-primary rounded-full">
                      {index + 1}
                    </span>
                  ) : (
                    <span className="w-2 h-2 bg-primary rounded-full mt-2" />
                  )}
                </motion.div>
                <span className="text-sm flex-1">{cleanItem}</span>
              </motion.div>
            </StaggerItem>
          )
        })}
      </StaggerContainer>
    </motion.div>
  )
}

function StructuredRenderer({ content }: { content: string }) {
  // Enhanced markdown parsing for structured content
  const parseContent = (text: string) => {
    // Split by sections (## headers)
    const sections = text.split(/(?=^## )/gm).filter((section) => section.trim())

    return sections.map((section, index) => {
      const lines = section.split('\n')
      const header = lines[0]?.replace(/^## /, '') || ''
      const body = lines.slice(1).join('\n')

      return { header, body, index }
    })
  }

  const sections = parseContent(content)

  return (
    <motion.div
      className="space-y-6 my-4"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={spring.gentle}
    >
      <StaggerContainer className="space-y-4" staggerDelay={0.2}>
        {sections.map(({ header, body, index }) => (
          <StaggerItem key={index}>
            <motion.div
              className="border border-border rounded-lg p-4 bg-gradient-to-br from-background to-muted/20"
              whileHover={{
                borderColor: 'var(--color-primary)',
                boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
              }}
              transition={spring.gentle}
            >
              {header && (
                <motion.h3
                  className="text-lg font-semibold mb-3 text-foreground"
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  {header}
                </motion.h3>
              )}
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 + 0.1 }}
              >
                <RichMediaRenderer content={body} />
              </motion.div>
            </motion.div>
          </StaggerItem>
        ))}
      </StaggerContainer>
    </motion.div>
  )
}

function TextRenderer({ content }: { content: string }) {
  // Enhanced text rendering with markdown support
  const renderMarkdown = (text: string) => {
    return text
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      .replace(/`(.*?)`/g, '<code class="px-1 py-0.5 bg-muted rounded text-sm">$1</code>')
      .replace(/\n/g, '<br>')
  }

  return (
    <motion.div
      className="prose prose-sm max-w-none"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={spring.gentle}
      dangerouslySetInnerHTML={{ __html: renderMarkdown(content) }}
    />
  )
}
