import { <PERSON><PERSON>, Edit, Forward, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Star, Trash2 } from 'lucide-react'
import * as React from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { AnimatePresence, motion, PopButton, spring } from '@/lib/animations'
import { cn } from '@/lib/utils'
import type { Message } from './ChatMessages'

interface MessageActionsMenuProps {
  message: Message
  onEdit?: (messageId: string) => void
  onDelete?: (messageId: string) => void
  onCopy?: (content: string) => void
  onReply?: (messageId: string) => void
  onForward?: (messageId: string) => void
  onToggleStar?: (messageId: string) => void
  className?: string
}

export function MessageActionsMenu({
  message,
  onEdit,
  onDelete,
  onCopy,
  onReply,
  onForward,
  onToggleStar,
  className,
}: MessageActionsMenuProps) {
  const [isOpen, setIsOpen] = React.useState(false)
  const menuRef = React.useRef<HTMLDivElement>(null)

  // Close menu when clicking outside
  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [isOpen])

  const handleCopy = React.useCallback(async () => {
    try {
      await navigator.clipboard.writeText(message.content)
      onCopy?.(message.content)
      setIsOpen(false)
      // Could add a toast notification here
    } catch (err) {
      console.error('Failed to copy message:', err)
    }
  }, [message.content, onCopy])

  const handleEdit = React.useCallback(() => {
    onEdit?.(message.id)
    setIsOpen(false)
  }, [message.id, onEdit])

  const handleDelete = React.useCallback(() => {
    // Could add a confirmation dialog here
    if (window.confirm('Are you sure you want to delete this message?')) {
      onDelete?.(message.id)
      setIsOpen(false)
    }
  }, [message.id, onDelete])

  const handleReply = React.useCallback(() => {
    onReply?.(message.id)
    setIsOpen(false)
  }, [message.id, onReply])

  const handleForward = React.useCallback(() => {
    onForward?.(message.id)
    setIsOpen(false)
  }, [message.id, onForward])

  const handleToggleStar = React.useCallback(() => {
    onToggleStar?.(message.id)
    setIsOpen(false)
  }, [message.id, onToggleStar])

  const menuItems = [
    {
      icon: Copy,
      label: 'Copy',
      action: handleCopy,
      show: true,
    },
    {
      icon: Reply,
      label: 'Reply',
      action: handleReply,
      show: !!onReply,
    },
    {
      icon: Edit,
      label: 'Edit',
      action: handleEdit,
      show: message.role === 'user' && !!onEdit,
    },
    {
      icon: Forward,
      label: 'Forward',
      action: handleForward,
      show: !!onForward,
    },
    {
      icon: Star,
      label: 'Star',
      action: handleToggleStar,
      show: !!onToggleStar,
    },
    {
      icon: Trash2,
      label: 'Delete',
      action: handleDelete,
      show: message.role === 'user' && !!onDelete,
      className: 'text-destructive hover:bg-destructive/10',
    },
  ].filter((item) => item.show)

  return (
    <div ref={menuRef} className={cn('relative', className)}>
      <PopButton
        className="p-1 rounded-md hover:bg-accent opacity-0 group-hover:opacity-100 transition-opacity"
        onClick={() => setIsOpen(!isOpen)}
        aria-label="Message actions"
      >
        <MoreVertical className="h-4 w-4 text-muted-foreground" />
      </PopButton>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            className="absolute right-0 top-full mt-1 bg-popover border border-border rounded-lg shadow-lg overflow-hidden z-50 min-w-[160px]"
            initial={{ opacity: 0, scale: 0.95, y: -10 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: -10 }}
            transition={spring.snappy}
          >
            <motion.div className="py-1">
              {menuItems.map((item, index) => {
                const Icon = item.icon
                return (
                  <motion.button
                    key={item.label}
                    className={cn(
                      'w-full flex items-center gap-3 px-3 py-2 text-sm hover:bg-accent transition-colors',
                      item.className
                    )}
                    onClick={item.action}
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ ...spring.gentle, delay: index * 0.05 }}
                    whileHover={{ x: 2 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Icon className="h-4 w-4" />
                    <span>{item.label}</span>
                  </motion.button>
                )
              })}
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
