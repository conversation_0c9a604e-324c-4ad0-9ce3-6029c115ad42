import { ChevronDown, ChevronUp, MessageSquare, <PERSON>ly, User } from 'lucide-react'
import * as React from 'react'
import { Button } from '@/components/ui/button'
import {
  AnimatePresence,
  motion,
  PopButton,
  StaggerContainer,
  StaggerItem,
  spring,
} from '@/lib/animations'
import { cn } from '@/lib/utils'
import { ChatInput } from './ChatInput'
import type { Thread } from './ChatMessages'

interface MessageThreadProps {
  thread: Thread
  onToggleExpanded?: (threadId: string) => void
  onReplyToThread?: (threadId: string, message: string) => void
  className?: string
}

export function MessageThread({
  thread,
  onToggleExpanded,
  onReplyToThread,
  className,
}: MessageThreadProps) {
  const [replyValue, setReplyValue] = React.useState('')
  const [isReplying, setIsReplying] = React.useState(false)

  const isExpanded = thread.isExpanded ?? false
  const threadMessagesCount = thread.messages.length
  const hasMessages = threadMessagesCount > 0

  const handleToggleExpanded = React.useCallback(() => {
    onToggleExpanded?.(thread.id)
  }, [thread.id, onToggleExpanded])

  const handleStartReply = React.useCallback(() => {
    setIsReplying(true)
  }, [])

  const handleCancelReply = React.useCallback(() => {
    setIsReplying(false)
    setReplyValue('')
  }, [])

  const handleSendReply = React.useCallback(
    (message: string) => {
      if (message.trim()) {
        onReplyToThread?.(thread.id, message)
        setReplyValue('')
        setIsReplying(false)
      }
    },
    [thread.id, onReplyToThread]
  )

  const handleReplyInputChange = React.useCallback((value: string) => {
    setReplyValue(value)
  }, [])

  if (!hasMessages && !isExpanded) {
    return null
  }

  return (
    <motion.div
      className={cn('mt-2 ml-12 border-muted/50 border-l-2 pl-4', className)}
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={spring.gentle}
    >
      {/* Thread Header */}
      <motion.div
        className="flex items-center gap-2 mb-3"
        whileHover={{ x: 2 }}
        transition={spring.snappy}
      >
        <motion.div
          animate={{ rotate: [0, 5, 0] }}
          transition={{ duration: 2, repeat: Infinity, ease: 'easeInOut' }}
        >
          <Reply className="h-4 w-4 text-muted-foreground" />
        </motion.div>

        <PopButton
          className="flex items-center gap-2 text-sm text-muted-foreground hover:text-foreground transition-colors py-1 px-2 rounded-md hover:bg-accent"
          onClick={handleToggleExpanded}
        >
          <span className="font-medium">
            {threadMessagesCount} {threadMessagesCount === 1 ? 'reply' : 'replies'}
          </span>
          <motion.div animate={{ rotate: isExpanded ? 180 : 0 }} transition={spring.snappy}>
            <ChevronDown className="h-3 w-3" />
          </motion.div>
        </PopButton>

        {!isExpanded && !isReplying && (
          <PopButton
            className="text-xs text-primary hover:text-primary/80 transition-colors py-1 px-2 rounded-md hover:bg-primary/5"
            onClick={handleStartReply}
          >
            Reply
          </PopButton>
        )}
      </motion.div>

      {/* Thread Messages */}
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ opacity: 0, height: 0, y: -20 }}
            animate={{ opacity: 1, height: 'auto', y: 0 }}
            exit={{ opacity: 0, height: 0, y: -20 }}
            transition={spring.gentle}
            className="space-y-3 mb-4"
          >
            <StaggerContainer className="space-y-4" staggerDelay={0.1}>
              {thread.messages.map((message, index) => (
                <StaggerItem key={message.id}>
                  <motion.div
                    className="bg-muted/30 rounded-lg p-3 border border-border/50"
                    whileHover={{
                      borderColor: 'var(--color-primary)',
                      backgroundColor: 'rgba(var(--color-muted), 0.5)',
                    }}
                    transition={spring.gentle}
                  >
                    {/* Thread Message Header */}
                    <motion.div
                      className="flex items-center gap-2 mb-2"
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                    >
                      <motion.div
                        className="w-6 h-6 rounded-full bg-primary/10 flex items-center justify-center"
                        whileHover={{ scale: 1.1 }}
                      >
                        <User className="h-3 w-3 text-primary" />
                      </motion.div>
                      <span className="text-sm font-medium text-foreground">
                        {message.role === 'user' ? 'You' : 'Assistant'}
                      </span>
                      <span className="text-xs text-muted-foreground">
                        {message.timestamp.toLocaleTimeString([], {
                          hour: '2-digit',
                          minute: '2-digit',
                        })}
                      </span>
                    </motion.div>

                    {/* Thread Message Content */}
                    <motion.div
                      className="text-sm"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: index * 0.1 + 0.1 }}
                    >
                      {message.content}
                    </motion.div>
                  </motion.div>
                </StaggerItem>
              ))}
            </StaggerContainer>

            {/* Reply Button for Expanded Thread */}
            {!isReplying && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
              >
                <PopButton
                  className="flex items-center gap-2 text-sm text-primary hover:text-primary/80 transition-colors py-2 px-3 rounded-md hover:bg-primary/5 border border-dashed border-primary/30"
                  onClick={handleStartReply}
                >
                  <MessageSquare className="h-3 w-3" />
                  Reply to thread
                </PopButton>
              </motion.div>
            )}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Reply Input */}
      <AnimatePresence>
        {isReplying && (
          <motion.div
            initial={{ opacity: 0, height: 0, y: -10 }}
            animate={{ opacity: 1, height: 'auto', y: 0 }}
            exit={{ opacity: 0, height: 0, y: -10 }}
            transition={spring.gentle}
            className="mt-3"
          >
            <motion.div
              className="bg-background/50 border border-border rounded-lg p-3"
              whileHover={{ borderColor: 'var(--color-primary)' }}
            >
              <div className="flex items-center gap-2 mb-3">
                <motion.div
                  animate={{ rotate: [0, 10, 0] }}
                  transition={{ duration: 2, repeat: Infinity, ease: 'easeInOut' }}
                >
                  <MessageSquare className="h-4 w-4 text-primary" />
                </motion.div>
                <span className="text-sm font-medium text-foreground">Reply to thread</span>
              </div>

              <ChatInput
                value={replyValue}
                onChange={handleReplyInputChange}
                onSend={handleSendReply}
                placeholder="Reply to this thread..."
                size="sm"
              />

              <motion.div
                className="flex items-center gap-2 mt-3"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.2 }}
              >
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleCancelReply}
                  className="h-7 px-3 text-xs"
                >
                  Cancel
                </Button>
                <span className="text-xs text-muted-foreground">
                  Press Enter to send, Shift + Enter for new line
                </span>
              </motion.div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  )
}
