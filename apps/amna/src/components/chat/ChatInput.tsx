import { Loader2, Mi<PERSON>, Paperclip, Send, Smile, X } from 'lucide-react'
import * as React from 'react'
import { MotionButton } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import { EmojiPicker } from './EmojiPicker'
import { FileUpload, type UploadedFile } from './FileUpload'
import { VoiceRecorder } from './VoiceRecorder'

export type { UploadedFile }

import {
  AnimatePresence,
  motion,
  spring,
  useMotionValue,
  useTransform,
  variants,
} from '@/lib/animations'

interface ChatInputProps {
  value: string
  onChange: (value: string) => void
  onSend: (message: string, files?: UploadedFile[], voiceNote?: Blob) => void
  placeholder?: string
  disabled?: boolean
  loading?: boolean
  className?: string
  maxLength?: number
  showCharCount?: boolean
  error?: string
  size?: 'sm' | 'md' | 'lg'
  enableFileUpload?: boolean
  onFilesChange?: (files: UploadedFile[]) => void
  enableVoiceRecording?: boolean
}

export const ChatInput = React.memo(function ChatInput({
  value,
  onChange,
  onSend,
  placeholder = 'Ask me anything...',
  disabled = false,
  loading = false,
  className,
  maxLength = 2000,
  showCharCount = false,
  error,
  size = 'md',
  enableFileUpload = true,
  onFilesChange,
  enableVoiceRecording = true,
}: ChatInputProps) {
  const textareaRef = React.useRef<HTMLTextAreaElement>(null)
  const [isFocused, setIsFocused] = React.useState(false)
  const [height, setHeight] = React.useState(24)
  const [attachedFiles, setAttachedFiles] = React.useState<UploadedFile[]>([])
  const [showFileUpload, setShowFileUpload] = React.useState(false)
  const [showEmojiPicker, setShowEmojiPicker] = React.useState(false)
  const [showVoiceRecorder, setShowVoiceRecorder] = React.useState(false)

  // Motion values for enhanced animations
  const focusScale = useMotionValue(1)
  const focusGlow = useMotionValue(0)
  const errorShake = useMotionValue(0)

  // Transform values
  const glowOpacity = useTransform(focusGlow, [0, 1], [0, 0.6])
  const borderGlow = useTransform(focusGlow, [0, 1], ['rgba(0,0,0,0)', 'rgba(59, 130, 246, 0.5)'])

  const handleSubmit = React.useCallback(
    (e: React.FormEvent) => {
      e.preventDefault()
      if ((value.trim() || attachedFiles.length > 0) && !disabled && !loading) {
        onSend(value.trim(), attachedFiles.length > 0 ? attachedFiles : undefined)
        onChange('')
        setAttachedFiles([])
        setShowFileUpload(false)
      } else if (!value.trim() && attachedFiles.length === 0 && !disabled && !loading) {
        // Shake animation for empty submission
        errorShake.set(10)
        setTimeout(() => errorShake.set(-10), 100)
        setTimeout(() => errorShake.set(5), 200)
        setTimeout(() => errorShake.set(0), 300)
      }
    },
    [value, attachedFiles, disabled, loading, onSend, onChange, errorShake]
  )

  const handleKeyDown = React.useCallback(
    (e: React.KeyboardEvent) => {
      if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault()
        handleSubmit(e)
      }
    },
    [handleSubmit]
  )

  const adjustTextareaHeight = React.useCallback(() => {
    const textarea = textareaRef.current
    if (textarea) {
      textarea.style.height = 'auto'
      const newHeight = Math.min(textarea.scrollHeight, 200)
      setHeight(newHeight)
      textarea.style.height = newHeight + 'px'
    }
  }, [])

  const handleFocus = React.useCallback(() => {
    setIsFocused(true)
    focusScale.set(1.01)
    focusGlow.set(1)
  }, [focusScale, focusGlow])

  const handleBlur = React.useCallback(() => {
    setIsFocused(false)
    focusScale.set(1)
    focusGlow.set(0)
  }, [focusScale, focusGlow])

  const handleFilesSelected = React.useCallback(
    (files: UploadedFile[]) => {
      setAttachedFiles(files)
      onFilesChange?.(files)
    },
    [onFilesChange]
  )

  const handleRemoveFile = React.useCallback(
    (fileId: string) => {
      const updatedFiles = attachedFiles.filter((f) => f.id !== fileId)
      setAttachedFiles(updatedFiles)
      onFilesChange?.(updatedFiles)
    },
    [attachedFiles, onFilesChange]
  )

  const toggleFileUpload = React.useCallback(() => {
    setShowFileUpload((prev) => !prev)
    setShowEmojiPicker(false)
  }, [])

  const toggleEmojiPicker = React.useCallback(() => {
    setShowEmojiPicker((prev) => !prev)
    setShowFileUpload(false)
  }, [])

  const handleEmojiSelect = React.useCallback(
    (emoji: string) => {
      const textarea = textareaRef.current
      if (!textarea) return

      const start = textarea.selectionStart
      const end = textarea.selectionEnd
      const newValue = value.substring(0, start) + emoji + value.substring(end)

      onChange(newValue)

      // Set cursor position after emoji
      setTimeout(() => {
        textarea.selectionStart = textarea.selectionEnd = start + emoji.length
        textarea.focus()
      }, 0)
    },
    [value, onChange]
  )

  const handleVoiceRecording = React.useCallback(
    (audioBlob: Blob, duration: number) => {
      onSend('Voice message', undefined, audioBlob)
      setShowVoiceRecorder(false)
    },
    [onSend]
  )

  const toggleVoiceRecorder = React.useCallback(() => {
    setShowVoiceRecorder((prev) => !prev)
    setShowFileUpload(false)
    setShowEmojiPicker(false)
  }, [])

  React.useEffect(() => {
    adjustTextareaHeight()
  }, [value, adjustTextareaHeight])

  // Character count calculations
  const charCount = value.length
  const isNearLimit = charCount > maxLength * 0.8
  const isOverLimit = charCount > maxLength

  return (
    <motion.form
      onSubmit={handleSubmit}
      className={cn('relative group space-y-3', className)}
      style={{ x: errorShake }}
      animate={
        error
          ? {
              x: [0, -10, 10, -5, 5, 0],
              transition: { duration: 0.5 },
            }
          : {}
      }
    >
      {/* File Upload Area */}
      <AnimatePresence>
        {showFileUpload && enableFileUpload && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={spring.gentle}
          >
            <FileUpload
              onFilesSelected={handleFilesSelected}
              maxFiles={5}
              maxSize={10}
              disabled={disabled || loading}
            />
          </motion.div>
        )}
      </AnimatePresence>

      {/* Attached Files Preview */}
      <AnimatePresence>
        {attachedFiles.length > 0 && !showFileUpload && (
          <motion.div
            className="flex flex-wrap gap-2"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={spring.gentle}
          >
            {attachedFiles.map((file, index) => (
              <motion.div
                key={file.id}
                className="flex items-center gap-2 px-3 py-1.5 bg-muted rounded-full text-sm"
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                transition={{ ...spring.snappy, delay: index * 0.05 }}
              >
                <span className="truncate max-w-[150px]">{file.file.name}</span>
                <button
                  type="button"
                  onClick={() => handleRemoveFile(file.id)}
                  className="hover:text-destructive transition-colors"
                >
                  <X className="h-3 w-3" />
                </button>
              </motion.div>
            ))}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Voice Recorder */}
      <AnimatePresence>
        {showVoiceRecorder && enableVoiceRecording ? (
          <VoiceRecorder
            onSendRecording={handleVoiceRecording}
            onCancel={() => setShowVoiceRecorder(false)}
          />
        ) : (
          <motion.div
            className="relative flex items-end gap-3 p-4 border border-input rounded-xl bg-background/95 backdrop-blur-sm overflow-hidden"
            style={{
              scale: focusScale,
              boxShadow: useTransform(
                focusGlow,
                [0, 1],
                [
                  '0 1px 3px rgba(0, 0, 0, 0.1)',
                  '0 0 0 1px rgba(59, 130, 246, 0.5), 0 4px 20px rgba(59, 130, 246, 0.15)',
                ]
              ),
            }}
            animate={{
              borderColor: error
                ? 'rgb(239, 68, 68)'
                : isFocused
                  ? 'rgb(59, 130, 246)'
                  : 'rgb(228, 228, 231)',
            }}
            transition={spring.gentle}
            whileHover={{
              borderColor: error ? 'rgb(239, 68, 68)' : 'rgba(59, 130, 246, 0.7)',
              transition: spring.snappy,
            }}
          >
            {/* Animated Background Glow */}
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-primary/5 via-transparent to-primary/5 rounded-xl"
              style={{ opacity: glowOpacity }}
            />

            {/* Subtle Glow Effect on Focus */}
            <AnimatePresence>
              {isFocused && (
                <motion.div
                  className="absolute inset-0 bg-gradient-to-r from-transparent via-white/4 to-transparent"
                  initial={{ x: '-100%', opacity: 0.3 }}
                  animate={{ x: '100%', opacity: 0.6 }}
                  exit={{ opacity: 0 }}
                  transition={{
                    duration: 2.5,
                    ease: [0.25, 0.46, 0.45, 0.94], // butterfly easing
                    repeat: Infinity,
                  }}
                />
              )}
            </AnimatePresence>

            <motion.div className="flex-1 relative" animate={{ height }} transition={spring.gentle}>
              <textarea
                ref={textareaRef}
                value={value}
                onChange={(e) => onChange(e.target.value)}
                onKeyDown={handleKeyDown}
                onFocus={handleFocus}
                onBlur={handleBlur}
                placeholder={placeholder}
                disabled={disabled || loading}
                maxLength={maxLength}
                rows={1}
                className="w-full resize-none bg-transparent border-0 outline-none placeholder:text-muted-foreground text-sm leading-6 min-h-[24px] max-h-[200px] relative z-10"
                style={{ height: 'auto' }}
              />

              {/* Character Count */}
              <AnimatePresence>
                {showCharCount && isFocused && (
                  <motion.div
                    className="absolute bottom-1 right-1 text-xs text-muted-foreground"
                    initial={{ opacity: 0, scale: 0.8, y: 10 }}
                    animate={{ opacity: 1, scale: 1, y: 0 }}
                    exit={{ opacity: 0, scale: 0.8, y: 10 }}
                    transition={spring.snappy}
                  >
                    <motion.span
                      animate={{
                        color: isOverLimit
                          ? 'rgb(239, 68, 68)'
                          : isNearLimit
                            ? 'rgb(245, 158, 11)'
                            : 'rgb(156, 163, 175)',
                      }}
                      transition={spring.gentle}
                    >
                      {charCount}
                    </motion.span>
                    <span className="text-muted-foreground/60">/{maxLength}</span>
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>

            {/* File Attachment Button */}
            {enableFileUpload && (
              <motion.div
                animate={{
                  scale: isFocused ? 1.05 : 1,
                  y: isFocused ? -2 : 0,
                }}
                transition={spring.snappy}
              >
                <MotionButton
                  type="button"
                  size="sm"
                  variant="ghost"
                  className={cn(
                    'flex-shrink-0 h-10 w-10 p-0 rounded-xl relative overflow-hidden',
                    attachedFiles.length > 0 && 'text-primary'
                  )}
                  onClick={toggleFileUpload}
                  disabled={disabled || loading}
                  animated
                  whileHover={{ scale: 1.1, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <motion.div
                    animate={{
                      rotate: showFileUpload ? 45 : 0,
                    }}
                    transition={spring.snappy}
                  >
                    <Paperclip className="h-4 w-4" />
                  </motion.div>
                  {attachedFiles.length > 0 && (
                    <motion.div
                      className="absolute -top-1 -right-1 w-4 h-4 bg-primary text-primary-foreground rounded-full text-xs flex items-center justify-center"
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={spring.bouncy}
                    >
                      {attachedFiles.length}
                    </motion.div>
                  )}
                </MotionButton>
              </motion.div>
            )}

            {/* Emoji Picker Button */}
            <motion.div
              className="relative"
              animate={{
                scale: isFocused ? 1.05 : 1,
                y: isFocused ? -2 : 0,
              }}
              transition={spring.snappy}
            >
              <MotionButton
                type="button"
                size="sm"
                variant="ghost"
                className={cn(
                  'flex-shrink-0 h-10 w-10 p-0 rounded-xl relative overflow-hidden',
                  showEmojiPicker && 'text-primary bg-primary/10'
                )}
                onClick={toggleEmojiPicker}
                disabled={disabled || loading}
                animated
                whileHover={{ scale: 1.1, y: -2 }}
                whileTap={{ scale: 0.95 }}
              >
                <motion.div
                  animate={{
                    rotate: showEmojiPicker ? 20 : 0,
                  }}
                  transition={spring.snappy}
                >
                  <Smile className="h-4 w-4" />
                </motion.div>
              </MotionButton>

              {/* Emoji Picker */}
              <EmojiPicker
                isOpen={showEmojiPicker}
                onClose={() => setShowEmojiPicker(false)}
                onSelectEmoji={handleEmojiSelect}
              />
            </motion.div>

            {/* Voice Recording Button */}
            {enableVoiceRecording && (
              <motion.div
                animate={{
                  scale: isFocused ? 1.05 : 1,
                  y: isFocused ? -2 : 0,
                }}
                transition={spring.snappy}
              >
                <MotionButton
                  type="button"
                  size="sm"
                  variant="ghost"
                  className={cn(
                    'flex-shrink-0 h-10 w-10 p-0 rounded-xl relative overflow-hidden',
                    showVoiceRecorder && 'text-destructive bg-destructive/10'
                  )}
                  onClick={toggleVoiceRecorder}
                  disabled={disabled || loading}
                  animated
                  whileHover={{ scale: 1.1, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <motion.div
                    animate={{
                      scale: showVoiceRecorder ? [1, 1.2, 1] : 1,
                    }}
                    transition={{
                      duration: 1,
                      repeat: showVoiceRecorder ? Infinity : 0,
                    }}
                  >
                    <Mic className="h-4 w-4" />
                  </motion.div>
                </MotionButton>
              </motion.div>
            )}

            {/* Enhanced Submit Button */}
            <motion.div
              animate={{
                scale: isFocused ? 1.05 : 1,
                y: isFocused ? -2 : 0,
              }}
              transition={spring.snappy}
            >
              <MotionButton
                type="submit"
                size="sm"
                disabled={
                  (!value.trim() && attachedFiles.length === 0) ||
                  disabled ||
                  loading ||
                  isOverLimit
                }
                className={cn(
                  'flex-shrink-0 h-10 w-10 p-0 rounded-xl relative overflow-hidden group/btn',
                  (value.trim() || attachedFiles.length > 0) &&
                    !disabled &&
                    !loading &&
                    !isOverLimit
                    ? 'bg-primary text-primary-foreground shadow-lg'
                    : 'bg-muted text-muted-foreground'
                )}
                animated
                ripple
                whileHover={{ scale: 1.008, y: -1 }}
                whileTap={{ scale: 0.992 }}
              >
                {/* Button Subtle Glow Effect */}
                <motion.div
                  className="absolute inset-0 bg-white/8 rounded-xl"
                  animate={
                    value.trim() && !loading
                      ? {
                          scale: [1, 1.008, 1],
                          opacity: [0.1, 0.15, 0.1],
                        }
                      : {}
                  }
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    ease: [0.37, 0, 0.63, 1], // breathe easing
                  }}
                />

                <AnimatePresence mode="wait">
                  {loading ? (
                    <motion.div
                      key="loading"
                      initial={{ scale: 0, rotate: -180 }}
                      animate={{ scale: 1, rotate: 0 }}
                      exit={{ scale: 0, rotate: 180 }}
                      transition={spring.bouncy}
                    >
                      <Loader2 className="h-4 w-4 animate-spin" />
                    </motion.div>
                  ) : (
                    <motion.div
                      key="send"
                      initial={{ scale: 0, rotate: -180 }}
                      animate={{ scale: 1, rotate: 0 }}
                      exit={{ scale: 0, rotate: 180 }}
                      transition={spring.bouncy}
                      whileHover={{
                        x: 2,
                        transition: spring.snappy,
                      }}
                    >
                      <Send className="h-4 w-4 relative z-10" />
                    </motion.div>
                  )}
                </AnimatePresence>
              </MotionButton>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Error Message */}
      <AnimatePresence>
        {error && (
          <motion.div
            className="mt-2 text-sm text-red-600 dark:text-red-400 flex items-center gap-2"
            initial={{ opacity: 0, y: -10, height: 0 }}
            animate={{ opacity: 1, y: 0, height: 'auto' }}
            exit={{ opacity: 0, y: -10, height: 0 }}
            transition={spring.gentle}
          >
            <motion.div animate={{ rotate: [0, 10, -10, 0] }} transition={{ duration: 0.5 }}>
              !
            </motion.div>
            {error}
          </motion.div>
        )}
      </AnimatePresence>
    </motion.form>
  )
})
