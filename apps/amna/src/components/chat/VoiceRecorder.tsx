import { <PERSON><PERSON>, <PERSON>c<PERSON><PERSON>, Pause, Play, Send, X } from 'lucide-react'
import * as React from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { AnimatePresence, motion, spring, useMotionValue, useTransform } from '@/lib/animations'
import { cn } from '@/lib/utils'

interface VoiceRecorderProps {
  onSendRecording: (audioBlob: Blob, duration: number) => void
  onCancel: () => void
  className?: string
}

export const VoiceRecorder = React.memo(function VoiceRecorder({
  onSendRecording,
  onCancel,
  className,
}: VoiceRecorderProps) {
  const [isRecording, setIsRecording] = React.useState(false)
  const [isPaused, setIsPaused] = React.useState(false)
  const [duration, setDuration] = React.useState(0)
  const [audioBlob, setAudioBlob] = React.useState<Blob | null>(null)
  const [audioUrl, setAudioUrl] = React.useState<string | null>(null)
  const [isPlaying, setIsPlaying] = React.useState(false)
  const [waveformData, setWaveformData] = React.useState<number[]>([])

  const mediaRecorderRef = React.useRef<MediaRecorder | null>(null)
  const audioChunksRef = React.useRef<Blob[]>([])
  const audioRef = React.useRef<HTMLAudioElement | null>(null)
  const animationRef = React.useRef<number | null>(null)
  const startTimeRef = React.useRef<number>(0)
  const analyserRef = React.useRef<AnalyserNode | null>(null)
  const audioContextRef = React.useRef<AudioContext | null>(null)

  // Motion values for animations
  const recordingScale = useMotionValue(1)
  const recordingGlow = useMotionValue(0)

  // Start recording
  const startRecording = React.useCallback(async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true })

      // Create MediaRecorder
      const mediaRecorder = new MediaRecorder(stream)
      mediaRecorderRef.current = mediaRecorder
      audioChunksRef.current = []

      // Setup audio analysis for waveform
      audioContextRef.current = new (window.AudioContext || (window as any).webkitAudioContext)()
      const source = audioContextRef.current.createMediaStreamSource(stream)
      analyserRef.current = audioContextRef.current.createAnalyser()
      analyserRef.current.fftSize = 256
      source.connect(analyserRef.current)

      // Handle data available
      mediaRecorder.ondataavailable = (event) => {
        audioChunksRef.current.push(event.data)
      }

      // Handle recording stop
      mediaRecorder.onstop = () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/webm' })
        setAudioBlob(audioBlob)
        const url = URL.createObjectURL(audioBlob)
        setAudioUrl(url)

        // Stop all tracks
        stream.getTracks().forEach((track) => track.stop())
      }

      // Start recording
      mediaRecorder.start()
      setIsRecording(true)
      startTimeRef.current = Date.now()

      // Start duration timer
      const updateDuration = () => {
        if (mediaRecorderRef.current?.state === 'recording') {
          setDuration(Math.floor((Date.now() - startTimeRef.current) / 1000))
          animationRef.current = requestAnimationFrame(updateDuration)
        }
      }
      updateDuration()

      // Start waveform animation
      updateWaveform()

      // Animate recording state
      recordingScale.set(1.1)
      recordingGlow.set(1)
    } catch (err) {
      console.error('Error accessing microphone:', err)
      // Handle error - show permission denied message
    }
  }, [recordingScale, recordingGlow])

  // Update waveform visualization
  const updateWaveform = React.useCallback(() => {
    if (!analyserRef.current || !isRecording) return

    const dataArray = new Uint8Array(analyserRef.current.frequencyBinCount)
    analyserRef.current.getByteFrequencyData(dataArray)

    // Sample the frequency data to create waveform
    const samples = 32
    const blockSize = Math.floor(dataArray.length / samples)
    const sampledData: number[] = []

    for (let i = 0; i < samples; i++) {
      let sum = 0
      for (let j = 0; j < blockSize; j++) {
        sum += dataArray[i * blockSize + j]
      }
      sampledData.push(sum / blockSize / 255) // Normalize to 0-1
    }

    setWaveformData(sampledData)

    if (isRecording && !isPaused) {
      requestAnimationFrame(updateWaveform)
    }
  }, [isRecording, isPaused])

  // Stop recording
  const stopRecording = React.useCallback(() => {
    if (mediaRecorderRef.current && mediaRecorderRef.current.state !== 'inactive') {
      mediaRecorderRef.current.stop()
      setIsRecording(false)
      setIsPaused(false)

      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }

      // Reset animations
      recordingScale.set(1)
      recordingGlow.set(0)

      // Cleanup audio context
      if (audioContextRef.current) {
        audioContextRef.current.close()
      }
    }
  }, [recordingScale, recordingGlow])

  // Toggle pause/resume
  const togglePause = React.useCallback(() => {
    if (!mediaRecorderRef.current) return

    if (isPaused) {
      mediaRecorderRef.current.resume()
      setIsPaused(false)
      updateWaveform()
    } else {
      mediaRecorderRef.current.pause()
      setIsPaused(true)
    }
  }, [isPaused, updateWaveform])

  // Play/pause recorded audio
  const togglePlayback = React.useCallback(() => {
    if (!audioUrl || !audioRef.current) return

    if (isPlaying) {
      audioRef.current.pause()
      setIsPlaying(false)
    } else {
      audioRef.current.play()
      setIsPlaying(true)
    }
  }, [audioUrl, isPlaying])

  // Send recording
  const handleSend = React.useCallback(() => {
    if (audioBlob) {
      onSendRecording(audioBlob, duration)
    }
  }, [audioBlob, duration, onSendRecording])

  // Cancel recording
  const handleCancel = React.useCallback(() => {
    stopRecording()
    setAudioBlob(null)
    setAudioUrl(null)
    setDuration(0)
    setWaveformData([])
    onCancel()
  }, [stopRecording, onCancel])

  // Cleanup on unmount
  React.useEffect(() => {
    return () => {
      if (mediaRecorderRef.current && mediaRecorderRef.current.state !== 'inactive') {
        mediaRecorderRef.current.stop()
      }
      if (audioUrl) {
        URL.revokeObjectURL(audioUrl)
      }
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }
      if (audioContextRef.current) {
        audioContextRef.current.close()
      }
    }
  }, [audioUrl])

  // Format duration
  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  return (
    <motion.div
      className={cn('flex items-center gap-3', className)}
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
      transition={spring.gentle}
    >
      {/* Cancel button */}
      <Button
        size="sm"
        variant="ghost"
        onClick={handleCancel}
        className="h-10 w-10 p-0 rounded-full"
      >
        <X className="h-4 w-4" />
      </Button>

      {/* Recording interface */}
      {!audioBlob ? (
        <>
          {/* Record button */}
          {!isRecording ? (
            <motion.button
              onClick={startRecording}
              className="relative h-16 w-16 rounded-full bg-destructive text-white flex items-center justify-center"
              whileHover={{ scale: 1.008 }}
              whileTap={{ scale: 0.992 }}
            >
              <Mic className="h-6 w-6" />
            </motion.button>
          ) : (
            <motion.div className="flex items-center gap-3 flex-1">
              {/* Pause/Resume button */}
              <motion.button
                onClick={togglePause}
                className="h-10 w-10 rounded-full bg-muted flex items-center justify-center"
                whileHover={{ scale: 1.008 }}
                whileTap={{ scale: 0.992 }}
              >
                {isPaused ? <Play className="h-4 w-4" /> : <Pause className="h-4 w-4" />}
              </motion.button>

              {/* Waveform visualization */}
              <div className="flex-1 flex items-center gap-1 h-12">
                {waveformData.map((value, index) => (
                  <motion.div
                    key={index}
                    className="flex-1 bg-destructive rounded-full"
                    animate={{
                      height: `${Math.max(4, value * 48)}px`,
                      opacity: isPaused ? 0.5 : 1,
                    }}
                    transition={{
                      duration: 0.1,
                      ease: 'linear',
                    }}
                  />
                ))}
                {/* Fill remaining space with placeholder bars */}
                {Array.from({ length: Math.max(0, 32 - waveformData.length) }).map((_, i) => (
                  <div key={`placeholder-${i}`} className="flex-1 h-1 bg-muted rounded-full" />
                ))}
              </div>

              {/* Duration */}
              <motion.div
                className="text-sm font-medium tabular-nums"
                animate={{ opacity: isPaused ? 0.5 : 1 }}
              >
                {formatDuration(duration)}
              </motion.div>

              {/* Stop button */}
              <motion.button
                onClick={stopRecording}
                className="h-12 w-12 rounded-full bg-destructive text-white flex items-center justify-center"
                style={{
                  scale: recordingScale,
                  boxShadow: useTransform(
                    recordingGlow,
                    [0, 1],
                    ['0 0 0 0 rgba(239, 68, 68, 0)', '0 0 0 8px rgba(239, 68, 68, 0.3)']
                  ),
                }}
                animate={{
                  scale: isPaused ? 1 : [1, 1.008, 1],
                }}
                transition={{
                  scale: {
                    repeat: isPaused ? 0 : Infinity,
                    duration: 2,
                  },
                }}
                whileTap={{ scale: 0.95 }}
              >
                <MicOff className="h-5 w-5" />
              </motion.button>
            </motion.div>
          )}
        </>
      ) : (
        /* Playback interface */
        <motion.div
          className="flex items-center gap-3 flex-1"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.1 }}
        >
          {/* Play/Pause button */}
          <motion.button
            onClick={togglePlayback}
            className="h-10 w-10 rounded-full bg-muted flex items-center justify-center"
            whileHover={{ scale: 1.008 }}
            whileTap={{ scale: 0.992 }}
          >
            {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
          </motion.button>

          {/* Audio element */}
          <audio ref={audioRef} src={audioUrl || undefined} onEnded={() => setIsPlaying(false)} />

          {/* Static waveform */}
          <div className="flex-1 flex items-center gap-1 h-8">
            {waveformData.map((value, index) => (
              <motion.div
                key={index}
                className="flex-1 bg-primary/30 rounded-full"
                style={{
                  height: `${Math.max(4, value * 32)}px`,
                }}
                initial={{ opacity: 0, scaleY: 0 }}
                animate={{ opacity: 1, scaleY: 1 }}
                transition={{ delay: index * 0.02 }}
              />
            ))}
          </div>

          {/* Duration */}
          <div className="text-sm font-medium tabular-nums">{formatDuration(duration)}</div>

          {/* Send button */}
          <motion.button
            onClick={handleSend}
            className="h-12 w-12 rounded-full bg-primary text-primary-foreground flex items-center justify-center"
            whileHover={{ scale: 1.008 }}
            whileTap={{ scale: 0.992 }}
          >
            <Send className="h-5 w-5" />
          </motion.button>
        </motion.div>
      )}
    </motion.div>
  )
})
