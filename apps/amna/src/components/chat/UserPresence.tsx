import { Circle } from 'lucide-react'
import * as React from 'react'
import { AnimatePresence, motion, spring } from '@/lib/animations'
import { cn } from '@/lib/utils'

export type PresenceStatus = 'online' | 'away' | 'busy' | 'offline'

interface UserPresenceProps {
  status: PresenceStatus
  showLabel?: boolean
  size?: 'sm' | 'md' | 'lg'
  animate?: boolean
  className?: string
}

const statusConfig = {
  online: {
    color: 'bg-green-500',
    label: 'Online',
    pulse: true,
  },
  away: {
    color: 'bg-yellow-500',
    label: 'Away',
    pulse: false,
  },
  busy: {
    color: 'bg-red-500',
    label: 'Busy',
    pulse: false,
  },
  offline: {
    color: 'bg-gray-400',
    label: 'Offline',
    pulse: false,
  },
}

const sizeConfig = {
  sm: {
    indicator: 'h-2 w-2',
    text: 'text-xs',
  },
  md: {
    indicator: 'h-3 w-3',
    text: 'text-sm',
  },
  lg: {
    indicator: 'h-4 w-4',
    text: 'text-base',
  },
}

export const UserPresence = React.memo(function UserPresence({
  status,
  showLabel = false,
  size = 'md',
  animate: enableAnimation = true,
  className,
}: UserPresenceProps) {
  const config = statusConfig[status]
  const sizeClass = sizeConfig[size]

  return (
    <motion.div
      className={cn('flex items-center gap-2', className)}
      initial={enableAnimation ? { opacity: 0, scale: 0.8 } : false}
      animate={enableAnimation ? { opacity: 1, scale: 1 } : false}
      transition={spring.snappy}
    >
      <div className="relative">
        <motion.div
          className={cn('rounded-full', sizeClass.indicator, config.color)}
          animate={
            config.pulse && enableAnimation
              ? {
                  scale: [1, 1.2, 1],
                }
              : {}
          }
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: 'easeInOut',
          }}
        />

        {/* Pulse ring for online status */}
        {config.pulse && enableAnimation && (
          <motion.div
            className={cn('absolute inset-0 rounded-full', config.color)}
            initial={{ scale: 1, opacity: 0.8 }}
            animate={{
              scale: [1, 1.5, 2],
              opacity: [0.8, 0.4, 0],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: 'easeOut',
            }}
          />
        )}
      </div>

      {showLabel && (
        <motion.span
          className={cn('text-muted-foreground', sizeClass.text)}
          initial={enableAnimation ? { opacity: 0, x: -5 } : false}
          animate={enableAnimation ? { opacity: 1, x: 0 } : false}
          transition={{ ...spring.gentle, delay: 0.1 }}
        >
          {config.label}
        </motion.span>
      )}
    </motion.div>
  )
})

// Component to show typing indicator
interface TypingIndicatorProps {
  users: string[]
  className?: string
}

export const TypingIndicator = React.memo(function TypingIndicator({
  users,
  className,
}: TypingIndicatorProps) {
  if (users.length === 0) return null

  const message = React.useMemo(() => {
    if (users.length === 1) {
      return `${users[0]} is typing`
    } else if (users.length === 2) {
      return `${users[0]} and ${users[1]} are typing`
    } else {
      return `${users[0]} and ${users.length - 1} others are typing`
    }
  }, [users])

  return (
    <AnimatePresence>
      <motion.div
        className={cn('flex items-center gap-2 text-sm text-muted-foreground', className)}
        initial={{ opacity: 0, y: 5 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -5 }}
        transition={spring.gentle}
      >
        <div className="flex gap-1">
          {[0, 1, 2].map((i) => (
            <motion.div
              key={i}
              className="h-2 w-2 bg-muted-foreground rounded-full"
              animate={{
                y: [0, -4, 0],
              }}
              transition={{
                duration: 0.6,
                repeat: Infinity,
                delay: i * 0.2,
                ease: 'easeInOut',
              }}
            />
          ))}
        </div>
        <span>{message}</span>
      </motion.div>
    </AnimatePresence>
  )
})

// Component to show user presence in a list
interface UserPresenceListProps {
  users: Array<{
    id: string
    name: string
    avatar?: string
    status: PresenceStatus
  }>
  maxVisible?: number
  className?: string
}

export const UserPresenceList = React.memo(function UserPresenceList({
  users,
  maxVisible = 5,
  className,
}: UserPresenceListProps) {
  const visibleUsers = users.slice(0, maxVisible)
  const remainingCount = Math.max(0, users.length - maxVisible)

  return (
    <div className={cn('flex items-center gap-2', className)}>
      <div className="flex -space-x-2">
        {visibleUsers.map((user, index) => (
          <motion.div
            key={user.id}
            className="relative"
            initial={{ opacity: 0, scale: 0.8, x: -10 }}
            animate={{ opacity: 1, scale: 1, x: 0 }}
            transition={{ ...spring.snappy, delay: index * 0.05 }}
            whileHover={{ scale: 1.1, zIndex: 10 }}
          >
            <div className="relative">
              {user.avatar ? (
                <img
                  src={user.avatar}
                  alt={user.name}
                  className="h-8 w-8 rounded-full border-2 border-background"
                />
              ) : (
                <div className="h-8 w-8 rounded-full border-2 border-background bg-muted flex items-center justify-center text-xs font-medium">
                  {user.name.charAt(0).toUpperCase()}
                </div>
              )}

              {/* Presence indicator */}
              <div className="absolute -bottom-0.5 -right-0.5">
                <UserPresence status={user.status} size="sm" animate={false} />
              </div>
            </div>
          </motion.div>
        ))}

        {remainingCount > 0 && (
          <motion.div
            className="h-8 w-8 rounded-full border-2 border-background bg-muted flex items-center justify-center text-xs font-medium"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ ...spring.snappy, delay: visibleUsers.length * 0.05 }}
          >
            +{remainingCount}
          </motion.div>
        )}
      </div>
    </div>
  )
})

// Hook to manage user presence
export function useUserPresence(userId: string) {
  const [presence, setPresence] = React.useState<PresenceStatus>('online')
  const [typingUsers, setTypingUsers] = React.useState<string[]>([])
  const lastActivityRef = React.useRef<number>(Date.now())
  const typingTimeoutRef = React.useRef<NodeJS.Timeout>()

  // Update last activity
  const updateActivity = React.useCallback(() => {
    lastActivityRef.current = Date.now()
    if (presence === 'away' || presence === 'offline') {
      setPresence('online')
    }
  }, [presence])

  // Set typing status
  const setTyping = React.useCallback((isTyping: boolean) => {
    // In a real app, this would send to a server
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current)
    }

    if (isTyping) {
      // Clear typing after 5 seconds
      typingTimeoutRef.current = setTimeout(() => {
        // Remove from typing users
      }, 5000)
    }
  }, [])

  // Auto-away detection
  React.useEffect(() => {
    const checkActivity = () => {
      const now = Date.now()
      const timeSinceLastActivity = now - lastActivityRef.current

      if (timeSinceLastActivity > 5 * 60 * 1000 && presence === 'online') {
        // Away after 5 minutes
        setPresence('away')
      } else if (timeSinceLastActivity > 15 * 60 * 1000 && presence === 'away') {
        // Offline after 15 minutes
        setPresence('offline')
      }
    }

    const interval = setInterval(checkActivity, 30000) // Check every 30 seconds

    return () => clearInterval(interval)
  }, [presence])

  // Listen for user activity
  React.useEffect(() => {
    const events = ['mousedown', 'keydown', 'touchstart', 'scroll']

    events.forEach((event) => {
      window.addEventListener(event, updateActivity)
    })

    return () => {
      events.forEach((event) => {
        window.removeEventListener(event, updateActivity)
      })
    }
  }, [updateActivity])

  return {
    presence,
    setPresence,
    typingUsers,
    setTyping,
    updateActivity,
  }
}
