import { <PERSON>Down, <PERSON>Up, Calendar, Filter, Search, User, X } from 'lucide-react'
import * as React from 'react'
import { But<PERSON> } from '@/components/ui/button'
import {
  AnimatePresence,
  motion,
  PopButton,
  StaggerContainer,
  StaggerItem,
  spring,
} from '@/lib/animations'
import { cn } from '@/lib/utils'
import type { Message } from './ChatMessages'

interface SearchResult {
  message: Message
  matchType: 'content' | 'metadata' | 'personality'
  snippet: string
  highlightIndices: [number, number][]
}

interface SearchFilters {
  role?: 'user' | 'assistant' | 'all'
  dateRange?: {
    start: Date
    end: Date
  }
  messageType?: 'text' | 'code' | 'file' | 'image' | 'table' | 'list' | 'structured' | 'all'
  personality?: string
  hasReactions?: boolean
  hasThreads?: boolean
}

interface MessageSearchProps {
  messages: Message[]
  isOpen: boolean
  onClose: () => void
  onMessageSelect: (messageId: string) => void
  className?: string
  renderMode?: 'modal' | 'inline'
}

export function MessageSearch({
  messages,
  isOpen,
  onClose,
  onMessageSelect,
  className,
  renderMode = 'modal',
}: MessageSearchProps) {
  const [query, setQuery] = React.useState('')
  const [filters, setFilters] = React.useState<SearchFilters>({ role: 'all', messageType: 'all' })
  const [results, setResults] = React.useState<SearchResult[]>([])
  const [selectedIndex, setSelectedIndex] = React.useState(0)
  const [isLoading, setIsLoading] = React.useState(false)
  const [showFilters, setShowFilters] = React.useState(false)

  const inputRef = React.useRef<HTMLInputElement>(null)
  const resultsRef = React.useRef<HTMLDivElement>(null)

  // Focus input when opened
  React.useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus()
    }
  }, [isOpen])

  // Search function with fuzzy matching and filters
  const performSearch = React.useCallback(
    async (searchQuery: string, searchFilters: SearchFilters) => {
      if (!searchQuery.trim()) {
        setResults([])
        return
      }

      setIsLoading(true)

      // Simulate search delay for better UX
      await new Promise((resolve) => setTimeout(resolve, 150))

      const searchTerms = searchQuery
        .toLowerCase()
        .split(' ')
        .filter((term) => term.length > 0)
      const searchResults: SearchResult[] = []

      messages.forEach((message) => {
        // Apply filters
        if (
          searchFilters.role &&
          searchFilters.role !== 'all' &&
          message.role !== searchFilters.role
        ) {
          return
        }

        if (
          searchFilters.messageType &&
          searchFilters.messageType !== 'all' &&
          message.messageType !== searchFilters.messageType
        ) {
          return
        }

        if (
          searchFilters.personality &&
          message.metadata?.personality?.name !== searchFilters.personality
        ) {
          return
        }

        if (searchFilters.hasReactions && (!message.reactions || message.reactions.length === 0)) {
          return
        }

        if (searchFilters.hasThreads && !message.threadId) {
          return
        }

        if (searchFilters.dateRange) {
          const messageDate = message.timestamp
          if (
            messageDate < searchFilters.dateRange.start ||
            messageDate > searchFilters.dateRange.end
          ) {
            return
          }
        }

        // Search in content
        const content = message.content.toLowerCase()
        const contentMatches: [number, number][] = []
        let contentScore = 0

        searchTerms.forEach((term) => {
          const index = content.indexOf(term)
          if (index !== -1) {
            contentMatches.push([index, index + term.length])
            contentScore += term.length / content.length
          }
        })

        // Search in metadata
        let metadataScore = 0
        const metadataText = [
          message.metadata?.personality?.name,
          message.metadata?.personality?.role,
          message.metadata?.fileName,
          message.messageType,
        ]
          .filter(Boolean)
          .join(' ')
          .toLowerCase()

        searchTerms.forEach((term) => {
          if (metadataText.includes(term)) {
            metadataScore += 0.5
          }
        })

        const totalScore = contentScore + metadataScore

        if (totalScore > 0) {
          // Create snippet with highlights
          const snippet = createSnippet(message.content, searchTerms, 100)

          searchResults.push({
            message,
            matchType: contentScore > metadataScore ? 'content' : 'metadata',
            snippet,
            highlightIndices: contentMatches,
          })
        }
      })

      // Sort by relevance (score) and recency
      searchResults.sort((a, b) => {
        const scoreA = calculateRelevanceScore(a, searchTerms)
        const scoreB = calculateRelevanceScore(b, searchTerms)

        if (scoreA !== scoreB) {
          return scoreB - scoreA
        }

        return b.message.timestamp.getTime() - a.message.timestamp.getTime()
      })

      setResults(searchResults.slice(0, 50)) // Limit to 50 results
      setSelectedIndex(0)
      setIsLoading(false)
    },
    [messages]
  )

  // Create snippet with context around matches
  const createSnippet = React.useCallback((content: string, terms: string[], maxLength: number) => {
    const lowerContent = content.toLowerCase()
    let bestStart = 0
    let bestScore = 0

    // Find the best position to start the snippet
    terms.forEach((term) => {
      const index = lowerContent.indexOf(term)
      if (index !== -1) {
        const contextStart = Math.max(0, index - 30)
        const contextEnd = Math.min(content.length, index + term.length + 30)
        const score = terms.reduce((acc, t) => {
          const termIndex = lowerContent.substring(contextStart, contextEnd).indexOf(t)
          return acc + (termIndex !== -1 ? 1 : 0)
        }, 0)

        if (score > bestScore) {
          bestScore = score
          bestStart = contextStart
        }
      }
    })

    const snippet = content.substring(bestStart, bestStart + maxLength)
    return bestStart > 0 ? '...' + snippet : snippet
  }, [])

  // Calculate relevance score
  const calculateRelevanceScore = React.useCallback((result: SearchResult, terms: string[]) => {
    const content = result.message.content.toLowerCase()
    let score = 0

    terms.forEach((term) => {
      const count = (content.match(new RegExp(term, 'g')) || []).length
      score += count * term.length
    })

    // Boost score for exact matches
    if (content.includes(terms.join(' '))) {
      score *= 1.5
    }

    // Boost score for recent messages
    const daysSinceMessage =
      (Date.now() - result.message.timestamp.getTime()) / (1000 * 60 * 60 * 24)
    score *= Math.max(0.1, 1 - daysSinceMessage / 30) // Reduce score for messages older than 30 days

    return score
  }, [])

  // Debounced search
  React.useEffect(() => {
    const timeoutId = setTimeout(() => {
      performSearch(query, filters)
    }, 300)

    return () => clearTimeout(timeoutId)
  }, [query, filters, performSearch])

  // Keyboard navigation
  const handleKeyDown = React.useCallback(
    (e: React.KeyboardEvent) => {
      if (!isOpen) {
        return
      }

      switch (e.key) {
        case 'Escape':
          onClose()
          break
        case 'ArrowDown':
          e.preventDefault()
          setSelectedIndex((prev) => Math.min(prev + 1, results.length - 1))
          break
        case 'ArrowUp':
          e.preventDefault()
          setSelectedIndex((prev) => Math.max(prev - 1, 0))
          break
        case 'Enter':
          e.preventDefault()
          if (results[selectedIndex]) {
            onMessageSelect(results[selectedIndex].message.id)
            onClose()
          }
          break
      }
    },
    [isOpen, onClose, results, selectedIndex, onMessageSelect]
  )

  // Highlight text function
  const highlightText = React.useCallback((text: string, terms: string[]) => {
    if (!terms.length) {
      return text
    }

    const regex = new RegExp(`(${terms.join('|')})`, 'gi')
    const parts = text.split(regex)

    return parts.map((part, index) => {
      const isHighlight = terms.some((term) => part.toLowerCase() === term.toLowerCase())

      return isHighlight ? (
        <mark key={index} className="bg-yellow-200 dark:bg-yellow-800 px-1 rounded">
          {part}
        </mark>
      ) : (
        part
      )
    })
  }, [])

  if (!isOpen && renderMode === 'modal') {
    return null
  }

  // Shared content component
  const SearchContent = (
    <motion.div
      className={cn(
        'bg-background border border-border rounded-lg shadow-xl flex flex-col overflow-hidden',
        renderMode === 'modal' ? 'w-full max-w-2xl mx-4 max-h-[70vh]' : 'h-full',
        className
      )}
      initial={renderMode === 'modal' ? { scale: 0.9, y: -20, opacity: 0 } : { opacity: 0 }}
      animate={renderMode === 'modal' ? { scale: 1, y: 0, opacity: 1 } : { opacity: 1 }}
      exit={renderMode === 'modal' ? { scale: 0.9, y: -20, opacity: 0 } : { opacity: 0 }}
      transition={spring.bouncy}
    >
      {/* Search Header */}
      <div className="p-4 border-b border-border">
        <div className="flex items-center gap-3 mb-3">
          <motion.div
            animate={{ rotate: [0, 360] }}
            transition={{ duration: 2, repeat: Infinity, ease: 'linear' }}
          >
            <Search className="h-5 w-5 text-primary" />
          </motion.div>

          <div className="flex-1 relative">
            <input
              ref={inputRef}
              type="text"
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              placeholder="Search messages..."
              className="w-full px-3 py-2 bg-muted/30 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary"
            />
            {query && (
              <motion.button
                className="absolute right-2 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground"
                onClick={() => setQuery('')}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <X className="h-4 w-4" />
              </motion.button>
            )}
          </div>

          <PopButton
            className="p-2 hover:bg-accent rounded-md"
            onClick={() => setShowFilters(!showFilters)}
            whileHover={{ scale: 1.05 }}
          >
            <Filter className="h-4 w-4" />
          </PopButton>

          {renderMode === 'modal' && (
            <PopButton
              className="p-2 hover:bg-accent rounded-md"
              onClick={onClose}
              whileHover={{ scale: 1.05 }}
            >
              <X className="h-4 w-4" />
            </PopButton>
          )}
        </div>

        {/* Filters */}
        <AnimatePresence>
          {showFilters && (
            <motion.div
              className="space-y-3"
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={spring.gentle}
            >
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <label className="text-xs font-medium text-muted-foreground mb-1 block">
                    Role
                  </label>
                  <select
                    value={filters.role}
                    onChange={(e) =>
                      setFilters((prev) => ({ ...prev, role: e.target.value as any }))
                    }
                    className="w-full px-2 py-1 text-sm bg-muted/30 border border-border rounded"
                  >
                    <option value="all">All</option>
                    <option value="user">User</option>
                    <option value="assistant">Assistant</option>
                  </select>
                </div>

                <div>
                  <label className="text-xs font-medium text-muted-foreground mb-1 block">
                    Type
                  </label>
                  <select
                    value={filters.messageType}
                    onChange={(e) =>
                      setFilters((prev) => ({ ...prev, messageType: e.target.value as any }))
                    }
                    className="w-full px-2 py-1 text-sm bg-muted/30 border border-border rounded"
                  >
                    <option value="all">All</option>
                    <option value="text">Text</option>
                    <option value="code">Code</option>
                    <option value="file">File</option>
                    <option value="image">Image</option>
                  </select>
                </div>
              </div>

              <div className="flex items-center gap-4 text-sm">
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={filters.hasReactions}
                    onChange={(e) =>
                      setFilters((prev) => ({ ...prev, hasReactions: e.target.checked }))
                    }
                    className="rounded"
                  />
                  Has reactions
                </label>
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={filters.hasThreads}
                    onChange={(e) =>
                      setFilters((prev) => ({ ...prev, hasThreads: e.target.checked }))
                    }
                    className="rounded"
                  />
                  Has threads
                </label>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Results Count */}
        {query && (
          <motion.div
            className="text-sm text-muted-foreground mt-3"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
          >
            {isLoading
              ? 'Searching...'
              : `${results.length} result${results.length !== 1 ? 's' : ''}`}
          </motion.div>
        )}
      </div>

      {/* Search Results */}
      <div ref={resultsRef} className="flex-1 overflow-y-auto">
        <AnimatePresence>
          {isLoading && (
            <motion.div
              className="flex items-center justify-center py-8"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
            >
              <div className="flex items-center gap-2 text-muted-foreground">
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
                >
                  <Search className="h-4 w-4" />
                </motion.div>
                Searching...
              </div>
            </motion.div>
          )}

          {!isLoading && query && results.length === 0 && (
            <motion.div
              className="flex flex-col items-center justify-center py-8 text-muted-foreground"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
            >
              <Search className="h-8 w-8 mb-2" />
              <p>No messages found</p>
              <p className="text-sm">Try different search terms or adjust filters</p>
            </motion.div>
          )}

          {!isLoading && results.length > 0 && (
            <StaggerContainer className="p-2" staggerDelay={0.05}>
              {results.map((result, index) => (
                <StaggerItem key={result.message.id}>
                  <motion.div
                    className={cn(
                      'p-3 rounded-lg cursor-pointer transition-colors border border-transparent',
                      index === selectedIndex
                        ? 'bg-primary/10 border-primary/30'
                        : 'hover:bg-muted/50'
                    )}
                    onClick={() => {
                      onMessageSelect(result.message.id)
                      onClose()
                    }}
                    whileHover={{ scale: 1.01 }}
                    whileTap={{ scale: 0.99 }}
                  >
                    {/* Message Header */}
                    <div className="flex items-center gap-2 mb-2">
                      <motion.div
                        className={cn(
                          'w-6 h-6 rounded-full flex items-center justify-center text-xs',
                          result.message.role === 'user'
                            ? 'bg-primary text-primary-foreground'
                            : 'bg-secondary text-secondary-foreground'
                        )}
                        whileHover={{ scale: 1.1 }}
                      >
                        <User className="h-3 w-3" />
                      </motion.div>

                      <span className="text-sm font-medium">
                        {result.message.role === 'user' ? 'You' : 'Assistant'}
                      </span>

                      {result.message.metadata?.personality && (
                        <span className="text-xs text-primary bg-primary/10 px-2 py-0.5 rounded-full">
                          {result.message.metadata.personality.name}
                        </span>
                      )}

                      <span className="text-xs text-muted-foreground ml-auto">
                        {result.message.timestamp.toLocaleDateString()}
                      </span>
                    </div>

                    {/* Message Snippet */}
                    <div className="text-sm leading-relaxed">
                      {highlightText(result.snippet, query.toLowerCase().split(' '))}
                    </div>

                    {/* Message Type Badge */}
                    {result.message.messageType && result.message.messageType !== 'text' && (
                      <div className="mt-2">
                        <span className="text-xs bg-muted text-muted-foreground px-2 py-1 rounded">
                          {result.message.messageType}
                        </span>
                      </div>
                    )}
                  </motion.div>
                </StaggerItem>
              ))}
            </StaggerContainer>
          )}
        </AnimatePresence>
      </div>

      {/* Keyboard Shortcuts */}
      {results.length > 0 && (
        <motion.div
          className="p-3 border-t border-border bg-muted/30"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.3 }}
        >
          <div className="flex items-center justify-between text-xs text-muted-foreground">
            <div className="flex items-center gap-4">
              <span className="flex items-center gap-1">
                <ArrowUp className="h-3 w-3" />
                <ArrowDown className="h-3 w-3" />
                Navigate
              </span>
              <span>Enter to select</span>
            </div>
            {renderMode === 'modal' && <span>Esc to close</span>}
          </div>
        </motion.div>
      )}
    </motion.div>
  )

  // Return appropriate wrapper based on render mode
  if (renderMode === 'inline') {
    return (
      <div className="h-full flex flex-col" onKeyDown={handleKeyDown}>
        {SearchContent}
      </div>
    )
  }

  // Modal mode
  return (
    <motion.div
      className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-start justify-center pt-20"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={spring.gentle}
      onClick={(e) => e.target === e.currentTarget && onClose()}
      onKeyDown={handleKeyDown}
    >
      {SearchContent}
    </motion.div>
  )
}
