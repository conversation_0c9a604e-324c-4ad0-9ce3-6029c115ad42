import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'lucide-react'
import * as React from 'react'
import {
  FloatingElement,
  motion,
  StaggerContainer,
  StaggerItem,
  spring,
  variants,
} from '@/lib/animations'
import { cn } from '@/lib/utils'

interface TypingIndicatorProps {
  className?: string
}

export const TypingIndicator = React.memo(function TypingIndicator({
  className,
}: TypingIndicatorProps) {
  const [dots, setDots] = React.useState('')

  // Animated dots for thinking text
  React.useEffect(() => {
    const interval = setInterval(() => {
      setDots((prev) => {
        if (prev.length >= 3) {
          return ''
        }
        return prev + '.'
      })
    }, 500)

    return () => clearInterval(interval)
  }, [])

  const thinkingMessages = [
    'AI is thinking',
    'Processing your request',
    'Analyzing information',
    'Generating response',
  ]

  const [currentMessage, setCurrentMessage] = React.useState(0)

  React.useEffect(() => {
    const interval = setInterval(() => {
      setCurrentMessage((prev) => (prev + 1) % thinkingMessages.length)
    }, 2000)

    return () => clearInterval(interval)
  }, [])

  return (
    <motion.div
      className={cn('flex gap-4 p-4', className)}
      initial={{ opacity: 0, y: 20, scale: 0.9 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      exit={{ opacity: 0, y: -10, scale: 0.9 }}
      transition={spring.gentle}
    >
      {/* Animated Avatar */}
      <motion.div
        className="flex-shrink-0"
        initial={{ scale: 0, rotate: -180 }}
        animate={{ scale: 1, rotate: 0 }}
        transition={spring.bouncy}
      >
        <motion.div
          className="w-8 h-8 rounded-full bg-secondary text-secondary-foreground flex items-center justify-center relative overflow-hidden"
          animate={{
            boxShadow: [
              '0 0 0 0 rgba(var(--color-primary), 0.3)',
              '0 0 0 8px rgba(var(--color-primary), 0)',
              '0 0 0 0 rgba(var(--color-primary), 0)',
            ],
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: 'easeOut',
          }}
        >
          <motion.div
            animate={{
              rotate: [0, 3, -3, 0],
              scale: [1, 1.015, 1],
            }}
            transition={{
              rotate: {
                duration: 4,
                repeat: Infinity,
                ease: [0.645, 0.045, 0.355, 1], // float easing
              },
              scale: {
                duration: 2.5,
                repeat: Infinity,
                ease: [0.37, 0, 0.63, 1], // breathe easing
              },
            }}
          >
            <Bot className="h-4 w-4" />
          </motion.div>

          {/* Sparkle Effect */}
          <motion.div
            className="absolute top-0 right-0"
            animate={{
              scale: [0, 1, 0],
              rotate: [0, 180, 360],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: 'easeInOut',
            }}
          >
            <Sparkles className="h-2 w-2 text-primary" />
          </motion.div>
        </motion.div>
      </motion.div>

      {/* Enhanced Typing Animation */}
      <motion.div
        className="flex-1 space-y-4"
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ ...spring.standard, delay: 0.2 }}
      >
        <motion.div
          className="max-w-[80%] rounded-lg px-4 py-3 bg-muted relative overflow-hidden"
          animate={{
            scale: [1, 1.003, 1],
          }}
          transition={{
            duration: 3,
            repeat: Infinity,
            ease: [0.37, 0, 0.63, 1], // breathe easing
          }}
        >
          {/* Subtle Glow Effect */}
          <motion.div
            className="absolute inset-0 bg-gradient-to-r from-transparent via-white/4 to-transparent"
            animate={{
              x: ['-100%', '100%'],
              opacity: [0.3, 0.6, 0.3],
            }}
            transition={{
              duration: 2.5,
              repeat: Infinity,
              ease: [0.25, 0.46, 0.45, 0.94], // butterfly easing
            }}
          />

          <div className="flex items-center gap-2 relative z-10">
            <motion.span
              className="text-sm text-muted-foreground font-medium"
              key={currentMessage}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.3 }}
            >
              {thinkingMessages[currentMessage]}
              {dots}
            </motion.span>

            {/* Enhanced Typing Dots */}
            <StaggerContainer className="flex gap-1" staggerDelay={0.15}>
              {[0, 1, 2].map((index) => (
                <StaggerItem key={index}>
                  <motion.div
                    className="w-1.5 h-1.5 bg-primary rounded-full"
                    animate={{
                      y: [-2, 2, -2],
                      opacity: [0.3, 1, 0.3],
                      scale: [0.8, 1.2, 0.8],
                    }}
                    transition={{
                      duration: 1.4,
                      repeat: Infinity,
                      ease: 'easeInOut',
                      delay: index * 0.2,
                    }}
                  />
                </StaggerItem>
              ))}
            </StaggerContainer>
          </div>
        </motion.div>
      </motion.div>
    </motion.div>
  )
})
