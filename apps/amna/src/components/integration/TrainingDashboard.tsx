import { useEffect, useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { useApi, usePaginatedApi } from '@/hooks/useApi'
import {
  SkillGap,
  TrainingEnrollment,
  TrainingProgram,
  TrainingService,
} from '@/services/api/training.service'

export function TrainingDashboard() {
  const [selectedCategory, setSelectedCategory] = useState<string>('')

  // Fetch training programs with pagination
  const {
    items: programs,
    isLoading: programsLoading,
    error: programsError,
    hasMore,
    loadMore,
    refresh: refreshPrograms,
  } = usePaginatedApi(
    (page, pageSize) =>
      TrainingService.listPrograms({
        category: selectedCategory || undefined,
        limit: pageSize,
        offset: (page - 1) * pageSize,
        status: 'active',
      }).then((res) => ({ items: res.programs, total: res.total })),
    { pageSize: 10 }
  )

  // Fetch user enrollments
  const {
    data: enrollmentsData,
    isLoading: enrollmentsLoading,
    execute: fetchEnrollments,
  } = useApi(() => TrainingService.getMyEnrollments({ status: 'in_progress' }))

  // Fetch skill gaps
  const {
    data: skillGaps,
    isLoading: skillGapsLoading,
    execute: analyzeSkillGaps,
  } = useApi(TrainingService.analyzeSkillGaps)

  // Fetch training analytics
  const {
    data: analytics,
    isLoading: analyticsLoading,
    execute: fetchAnalytics,
  } = useApi(TrainingService.getTrainingAnalytics)

  // Load initial data
  useEffect(() => {
    fetchEnrollments()
    analyzeSkillGaps()
    fetchAnalytics()
  }, [])

  // Handle program enrollment
  const handleEnroll = async (programId: string) => {
    try {
      await TrainingService.enrollInProgram(programId)
      await fetchEnrollments() // Refresh enrollments
      alert('Successfully enrolled in the program!')
    } catch (error) {
      console.error('Enrollment failed:', error)
      alert('Failed to enroll in the program')
    }
  }

  if (programsLoading && programs.length === 0) {
    return <div>Loading training programs...</div>
  }

  if (programsError) {
    return (
      <div className="text-red-500">
        Error loading programs: {programsError.message}
        <Button onClick={refreshPrograms} className="ml-4">
          Retry
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-6 p-6">
      <h1 className="text-2xl font-bold">Training Dashboard</h1>

      {/* Analytics Summary */}
      {analytics && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card className="p-4">
            <h3 className="text-sm font-medium text-gray-500">Active Enrollments</h3>
            <p className="text-2xl font-bold">{analytics.activeEnrollments}</p>
          </Card>
          <Card className="p-4">
            <h3 className="text-sm font-medium text-gray-500">Completed Programs</h3>
            <p className="text-2xl font-bold">{analytics.completedPrograms}</p>
          </Card>
          <Card className="p-4">
            <h3 className="text-sm font-medium text-gray-500">Average Progress</h3>
            <p className="text-2xl font-bold">{analytics.averageProgress}%</p>
          </Card>
          <Card className="p-4">
            <h3 className="text-sm font-medium text-gray-500">Time Spent</h3>
            <p className="text-2xl font-bold">{Math.round(analytics.timeSpent / 60)}h</p>
          </Card>
        </div>
      )}

      {/* Skill Gaps */}
      {skillGaps && skillGaps.length > 0 && (
        <Card className="p-6">
          <h2 className="text-xl font-semibold mb-4">Identified Skill Gaps</h2>
          <div className="space-y-2">
            {skillGaps.slice(0, 3).map((gap) => (
              <div key={gap.id} className="flex justify-between items-center">
                <div>
                  <span className="font-medium">{gap.skill}</span>
                  <span className="ml-2 text-sm text-gray-500">
                    Current: {gap.currentLevel}/5, Required: {gap.requiredLevel}/5
                  </span>
                </div>
                <span
                  className={`px-2 py-1 text-xs rounded ${
                    gap.priority === 'high'
                      ? 'bg-red-100 text-red-800'
                      : gap.priority === 'medium'
                        ? 'bg-yellow-100 text-yellow-800'
                        : 'bg-green-100 text-green-800'
                  }`}
                >
                  {gap.priority}
                </span>
              </div>
            ))}
          </div>
          {skillGaps.length > 3 && (
            <p className="text-sm text-gray-500 mt-2">
              +{skillGaps.length - 3} more skill gaps identified
            </p>
          )}
        </Card>
      )}

      {/* Current Enrollments */}
      {enrollmentsData && enrollmentsData.enrollments.length > 0 && (
        <Card className="p-6">
          <h2 className="text-xl font-semibold mb-4">My Current Programs</h2>
          <div className="space-y-3">
            {enrollmentsData.enrollments.map((enrollment) => (
              <div key={enrollment.id} className="border rounded p-4">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="font-medium">Program: {enrollment.programId}</h3>
                    <p className="text-sm text-gray-500">
                      Started: {new Date(enrollment.startDate).toLocaleDateString()}
                    </p>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium">{enrollment.progress}% Complete</div>
                    <div className="w-32 bg-gray-200 rounded-full h-2 mt-1">
                      <div
                        className="bg-blue-600 h-2 rounded-full"
                        style={{ width: `${enrollment.progress}%` }}
                      />
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}

      {/* Available Programs */}
      <Card className="p-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">Available Training Programs</h2>
          <select
            className="border rounded px-3 py-1"
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
          >
            <option value="">All Categories</option>
            <option value="technical">Technical</option>
            <option value="leadership">Leadership</option>
            <option value="communication">Communication</option>
            <option value="compliance">Compliance</option>
          </select>
        </div>

        <div className="space-y-4">
          {programs.map((program) => (
            <div key={program.id} className="border rounded p-4">
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <h3 className="font-medium text-lg">{program.title}</h3>
                  <p className="text-gray-600 text-sm mt-1">{program.description}</p>
                  <div className="flex gap-4 mt-2 text-sm text-gray-500">
                    <span>Duration: {program.duration}h</span>
                    <span>Level: {program.level}</span>
                    <span>Format: {program.format}</span>
                    <span>Price: ${program.price}</span>
                  </div>
                  <div className="flex flex-wrap gap-1 mt-2">
                    {program.skills.slice(0, 3).map((skill, index) => (
                      <span key={index} className="px-2 py-1 bg-gray-100 text-xs rounded">
                        {skill}
                      </span>
                    ))}
                    {program.skills.length > 3 && (
                      <span className="text-xs text-gray-500">
                        +{program.skills.length - 3} more
                      </span>
                    )}
                  </div>
                </div>
                <Button
                  onClick={() => handleEnroll(program.id)}
                  className="ml-4"
                  disabled={enrollmentsData?.enrollments.some((e) => e.programId === program.id)}
                >
                  {enrollmentsData?.enrollments.some((e) => e.programId === program.id)
                    ? 'Enrolled'
                    : 'Enroll'}
                </Button>
              </div>
            </div>
          ))}
        </div>

        {hasMore && (
          <div className="mt-4 text-center">
            <Button onClick={loadMore} variant="outline">
              Load More Programs
            </Button>
          </div>
        )}
      </Card>

      {/* Upcoming Deadlines */}
      {analytics?.upcomingDeadlines && analytics.upcomingDeadlines.length > 0 && (
        <Card className="p-6">
          <h2 className="text-xl font-semibold mb-4">Upcoming Deadlines</h2>
          <div className="space-y-2">
            {analytics.upcomingDeadlines.map((deadline) => (
              <div key={deadline.enrollmentId} className="flex justify-between">
                <span>{deadline.programTitle}</span>
                <span className="text-sm text-gray-500">
                  Due: {new Date(deadline.deadline).toLocaleDateString()}
                </span>
              </div>
            ))}
          </div>
        </Card>
      )}
    </div>
  )
}
