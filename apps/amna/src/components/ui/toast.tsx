import { AlertCircle, AlertTriangle, CheckCircle, Info, X } from 'lucide-react'
import * as React from 'react'
import {
  AnimatePresence,
  motion,
  StaggerContainer,
  StaggerItem,
  spring,
  useDragControls,
  useMotionValue,
  useTransform,
} from '@/lib/animations'
import { cn } from '@/lib/utils'
import { MotionButton } from './button'

type ToastType = 'success' | 'error' | 'warning' | 'info'

interface Toast {
  id: string
  type: ToastType
  title: string
  description?: string
  duration?: number
}

interface ToastContextType {
  toasts: Toast[]
  addToast: (toast: Omit<Toast, 'id'>) => void
  removeToast: (id: string) => void
}

const ToastContext = React.createContext<ToastContextType | undefined>(undefined)

export function useToast() {
  const context = React.useContext(ToastContext)
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider')
  }
  return context
}

export function ToastProvider({ children }: { children: React.ReactNode }) {
  const [toasts, setToasts] = React.useState<Toast[]>([])

  const removeToast = React.useCallback((id: string) => {
    setToasts((prev) => prev.filter((toast) => toast.id !== id))
  }, [])

  const addToast = React.useCallback(
    (toast: Omit<Toast, 'id'>) => {
      const id = Math.random().toString(36).substr(2, 9)
      const newToast = { ...toast, id }
      setToasts((prev) => [...prev, newToast])

      // Auto remove after duration
      if (toast.duration !== 0) {
        setTimeout(() => {
          removeToast(id)
        }, toast.duration || 5000)
      }
    },
    [removeToast]
  )

  return (
    <ToastContext.Provider value={{ toasts, addToast, removeToast }}>
      {children}
      <ToastContainer />
    </ToastContext.Provider>
  )
}

function ToastContainer() {
  const { toasts } = useToast()

  return (
    <motion.div
      className="fixed top-4 right-4 z-50 space-y-2 max-w-sm"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
    >
      <StaggerContainer staggerDelay={0.1}>
        <AnimatePresence mode="popLayout">
          {toasts.map((toast, index) => (
            <StaggerItem key={toast.id}>
              <ToastItem toast={toast} index={index} />
            </StaggerItem>
          ))}
        </AnimatePresence>
      </StaggerContainer>
    </motion.div>
  )
}

function ToastItem({ toast, index }: { toast: Toast; index: number }) {
  const { removeToast } = useToast()
  const [isHovered, setIsHovered] = React.useState(false)
  const dragControls = useDragControls()

  // Drag and swipe to dismiss
  const x = useMotionValue(0)
  const opacity = useTransform(x, [-200, 0, 200], [0, 1, 0])
  const scale = useTransform(x, [-200, 0, 200], [0.8, 1, 0.8])

  const handleDragEnd = (event: any, info: any) => {
    const threshold = 100
    if (Math.abs(info.offset.x) > threshold || Math.abs(info.velocity.x) > 500) {
      removeToast(toast.id)
    } else {
      x.set(0)
    }
  }

  const handleClose = () => {
    removeToast(toast.id)
  }

  const icons = {
    success: CheckCircle,
    error: AlertCircle,
    warning: AlertTriangle,
    info: Info,
  }

  const colors = {
    success:
      'border-green-200 bg-green-50 text-green-800 dark:border-green-800 dark:bg-green-950 dark:text-green-200',
    error:
      'border-red-200 bg-red-50 text-red-800 dark:border-red-800 dark:bg-red-950 dark:text-red-200',
    warning:
      'border-yellow-200 bg-yellow-50 text-yellow-800 dark:border-yellow-800 dark:bg-yellow-950 dark:text-yellow-200',
    info: 'border-blue-200 bg-blue-50 text-blue-800 dark:border-blue-800 dark:bg-blue-950 dark:text-blue-200',
  }

  const accentColors = {
    success: 'text-green-600 dark:text-green-400',
    error: 'text-red-600 dark:text-red-400',
    warning: 'text-yellow-600 dark:text-yellow-400',
    info: 'text-blue-600 dark:text-blue-400',
  }

  const Icon = icons[toast.type]

  return (
    <motion.div
      layout
      drag="x"
      dragControls={dragControls}
      dragConstraints={{ left: 0, right: 0 }}
      dragElastic={0.2}
      onDragEnd={handleDragEnd}
      style={{ x, opacity, scale }}
      initial={{
        opacity: 0,
        y: -50,
        scale: 0.9,
        rotateX: -90,
      }}
      animate={{
        opacity: 1,
        y: 0,
        scale: 1,
        rotateX: 0,
      }}
      exit={{
        opacity: 0,
        x: 400,
        scale: 0.8,
        rotateY: 90,
        transition: { duration: 0.3 },
      }}
      transition={{
        ...spring.gentle,
        delay: index * 0.1,
      }}
      whileHover={{
        scale: 1.02,
        boxShadow: '0 8px 30px rgba(0, 0, 0, 0.15)',
        transition: spring.snappy,
      }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
      className={cn(
        'flex items-start gap-3 p-4 rounded-lg border shadow-lg backdrop-blur-sm cursor-grab active:cursor-grabbing min-w-[320px] max-w-[420px] relative overflow-hidden',
        colors[toast.type]
      )}
    >
      {/* Progress bar for auto-dismiss */}
      {toast.duration && toast.duration > 0 && (
        <motion.div
          className="absolute bottom-0 left-0 h-1 bg-current opacity-30"
          initial={{ width: '100%' }}
          animate={{ width: '0%' }}
          transition={{
            duration: toast.duration / 1000,
            ease: 'linear',
          }}
        />
      )}

      {/* Animated Icon */}
      <motion.div
        initial={{ scale: 0, rotate: -180 }}
        animate={{ scale: 1, rotate: 0 }}
        transition={{
          ...spring.bouncy,
          delay: index * 0.1 + 0.2,
        }}
        className={cn('flex-shrink-0 mt-0.5', accentColors[toast.type])}
      >
        <motion.div
          animate={{
            rotate: toast.type === 'success' ? [0, 360] : 0,
            scale: isHovered ? 1.1 : 1,
          }}
          transition={{
            rotate: {
              duration: 2,
              repeat: toast.type === 'success' ? Infinity : 0,
              ease: 'linear',
            },
            scale: spring.snappy,
          }}
        >
          <Icon className="h-5 w-5" />
        </motion.div>
      </motion.div>

      {/* Content */}
      <motion.div
        className="flex-1 min-w-0"
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{
          delay: index * 0.1 + 0.3,
          duration: 0.4,
        }}
      >
        <motion.h4
          className="font-medium text-sm"
          animate={{
            opacity: isHovered ? 1 : 0.9,
          }}
          transition={spring.gentle}
        >
          {toast.title}
        </motion.h4>
        {toast.description && (
          <motion.p
            className="text-sm opacity-90 mt-1"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 0.9, y: 0 }}
            transition={{
              delay: index * 0.1 + 0.4,
              duration: 0.3,
            }}
          >
            {toast.description}
          </motion.p>
        )}
      </motion.div>

      {/* Enhanced Close Button */}
      <motion.div
        initial={{ opacity: 0, scale: 0 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{
          delay: index * 0.1 + 0.5,
          ...spring.bouncy,
        }}
      >
        <MotionButton
          variant="ghost"
          size="sm"
          className="h-6 w-6 p-0 hover:bg-black/10 dark:hover:bg-white/10"
          onClick={handleClose}
          animated
          ripple={false}
        >
          <motion.div whileHover={{ rotate: 90 }} transition={spring.snappy}>
            <X className="h-4 w-4" />
          </motion.div>
        </MotionButton>
      </motion.div>

      {/* Swipe indicator */}
      <motion.div
        className="absolute left-2 top-1/2 -translate-y-1/2 opacity-30"
        initial={{ x: -20, opacity: 0 }}
        animate={{
          x: isHovered ? 0 : -20,
          opacity: isHovered ? 0.5 : 0,
        }}
        transition={spring.gentle}
      >
        <div className="text-xs">←</div>
      </motion.div>
    </motion.div>
  )
}
