import type * as React from 'react'
import { cn } from '@/lib/utils'

export function Skeleton({ className, ...props }: React.HTMLAttributes<HTMLDivElement>) {
  return <div className={cn('animate-pulse rounded-md bg-muted', className)} {...props} />
}

export function MessageSkeleton({ className }: { className?: string }) {
  return (
    <div className={cn('flex gap-4 p-4', className)}>
      {/* Avatar skeleton */}
      <Skeleton className="w-8 h-8 rounded-full flex-shrink-0" />

      {/* Content skeleton */}
      <div className="flex-1 space-y-3">
        <div className="space-y-2">
          <Skeleton className="h-4 w-3/4" />
          <Skeleton className="h-4 w-1/2" />
          <Skeleton className="h-4 w-5/6" />
        </div>

        {/* Sources skeleton */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mt-4">
          <Skeleton className="h-20 w-full rounded-lg" />
          <Skeleton className="h-20 w-full rounded-lg" />
        </div>

        {/* Follow-up questions skeleton */}
        <div className="space-y-2 mt-4">
          <Skeleton className="h-8 w-2/3 rounded-full" />
          <Skeleton className="h-8 w-1/2 rounded-full" />
        </div>
      </div>
    </div>
  )
}

export function ChatEmptyStateSkeleton({ className }: { className?: string }) {
  return (
    <div
      className={cn('flex flex-col items-center justify-center min-h-[60vh] space-y-8', className)}
    >
      {/* Title skeleton */}
      <div className="text-center space-y-4">
        <Skeleton className="h-8 w-64 mx-auto" />
        <Skeleton className="h-4 w-96 mx-auto" />
      </div>

      {/* Suggested prompts skeleton */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 w-full max-w-2xl">
        {Array.from({ length: 4 }).map((_, i) => (
          <Skeleton key={i} className="h-24 w-full rounded-lg" />
        ))}
      </div>
    </div>
  )
}
