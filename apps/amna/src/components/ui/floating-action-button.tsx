import { Home, MessageSquare, Plus, Sparkles } from 'lucide-react'
import * as React from 'react'
import { useNavigate } from 'react-router-dom'
import {
  AnimatePresence,
  FloatingElement,
  motion,
  StaggerContainer,
  StaggerItem,
  spring,
  useDragControls,
  useMotionValue,
  useTransform,
} from '@/lib/animations'
import { cn } from '@/lib/utils'
import { MotionButton } from './button'

interface FloatingActionButtonProps {
  className?: string
}

export function FloatingActionButton({ className }: FloatingActionButtonProps) {
  const navigate = useNavigate()
  const [isExpanded, setIsExpanded] = React.useState(false)
  const [isDragging, setIsDragging] = React.useState(false)
  const dragControls = useDragControls()

  // Drag position tracking
  const x = useMotionValue(0)
  const y = useMotionValue(0)
  const rotateX = useTransform(y, [-100, 100], [30, -30])
  const rotateY = useTransform(x, [-100, 100], [-30, 30])

  const actionItems = [
    {
      icon: MessageSquare,
      label: 'New Chat',
      action: () => {
        navigate('/chat')
        setIsExpanded(false)
      },
      color: 'bg-blue-500 hover:bg-blue-600',
    },
    {
      icon: Home,
      label: 'Home',
      action: () => {
        navigate('/')
        setIsExpanded(false)
      },
      color: 'bg-green-500 hover:bg-green-600',
    },
    {
      icon: Sparkles,
      label: 'Explore',
      action: () => {
        // Add explore functionality
        setIsExpanded(false)
      },
      color: 'bg-purple-500 hover:bg-purple-600',
    },
  ]

  const handleDragStart = () => {
    setIsDragging(true)
  }

  const handleDragEnd = () => {
    setIsDragging(false)
    // Reset position
    x.set(0)
    y.set(0)
  }

  return (
    <motion.div
      className={cn('fixed bottom-6 right-6 z-50 flex flex-col-reverse gap-3', className)}
      initial={{ scale: 0, opacity: 0 }}
      animate={{ scale: 1, opacity: 1 }}
      transition={{ ...spring.bouncy, delay: 0.5 }}
    >
      {/* Enhanced Action Buttons */}
      <AnimatePresence>
        {isExpanded && (
          <StaggerContainer className="flex flex-col-reverse gap-3" staggerDelay={0.1}>
            {actionItems.map((item, index) => {
              const Icon = item.icon
              return (
                <StaggerItem key={index}>
                  <motion.div
                    initial={{
                      opacity: 0,
                      scale: 0,
                      x: 20,
                      rotate: -45,
                    }}
                    animate={{
                      opacity: 1,
                      scale: 1,
                      x: 0,
                      rotate: 0,
                    }}
                    exit={{
                      opacity: 0,
                      scale: 0,
                      x: 20,
                      rotate: 45,
                    }}
                    transition={spring.snappy}
                    whileHover={{ scale: 1.05, x: -5 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <MotionButton
                      size="sm"
                      variant="secondary"
                      className={cn(
                        'rounded-full shadow-lg hover:shadow-xl text-white border-0 min-w-[120px] justify-start',
                        item.color
                      )}
                      onClick={item.action}
                      animated
                      ripple
                    >
                      <motion.div
                        animate={{
                          rotate: [0, 360],
                          transition: {
                            duration: 2,
                            repeat: Infinity,
                            ease: 'linear',
                          },
                        }}
                      >
                        <Icon className="h-4 w-4 mr-2" />
                      </motion.div>
                      {item.label}
                    </MotionButton>
                  </motion.div>
                </StaggerItem>
              )
            })}
          </StaggerContainer>
        )}
      </AnimatePresence>

      {/* Enhanced Main FAB with Drag Support */}
      <FloatingElement>
        <motion.div
          drag
          dragControls={dragControls}
          dragConstraints={{ left: -100, right: 100, top: -100, bottom: 100 }}
          dragElastic={0.2}
          onDragStart={handleDragStart}
          onDragEnd={handleDragEnd}
          style={{ x, y, rotateX, rotateY }}
          whileHover={{
            scale: 1.1,
            boxShadow: '0 10px 30px rgba(0, 0, 0, 0.3)',
          }}
          whileTap={{ scale: 0.9 }}
          whileDrag={{
            scale: 1.2,
            boxShadow: '0 15px 40px rgba(0, 0, 0, 0.4)',
            transition: { duration: 0.2 },
          }}
          className="cursor-grab active:cursor-grabbing"
        >
          <MotionButton
            size="lg"
            className={cn(
              'rounded-full w-16 h-16 shadow-xl bg-gradient-to-r from-primary to-primary/80 border-0 text-primary-foreground relative overflow-hidden',
              isDragging && 'cursor-grabbing'
            )}
            onClick={() => setIsExpanded(!isExpanded)}
            animated
            ripple
          >
            {/* Background Pulse Effect */}
            <motion.div
              className="absolute inset-0 bg-white/20 rounded-full"
              animate={{
                scale: [1, 1.5, 1],
                opacity: [0.3, 0, 0.3],
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: 'easeInOut',
              }}
            />

            {/* Rotating Plus Icon */}
            <motion.div
              animate={{
                rotate: isExpanded ? 45 : 0,
                scale: isExpanded ? 1.1 : 1,
              }}
              transition={spring.snappy}
            >
              <Plus className="h-7 w-7 relative z-10" />
            </motion.div>

            {/* Sparkle Effects */}
            <motion.div
              className="absolute top-1 right-1"
              animate={{
                scale: [0, 1, 0],
                rotate: [0, 180, 360],
                opacity: [0, 1, 0],
              }}
              transition={{
                duration: 3,
                repeat: Infinity,
                ease: 'easeInOut',
                delay: 1,
              }}
            >
              <Sparkles className="h-3 w-3 text-white" />
            </motion.div>
          </MotionButton>
        </motion.div>
      </FloatingElement>
    </motion.div>
  )
}
