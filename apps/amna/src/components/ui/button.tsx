import { Slot } from '@radix-ui/react-slot'
import { cva, type VariantProps } from 'class-variance-authority'
import { type HTMLMotionProps, motion } from 'framer-motion'
import * as React from 'react'
import { prefersReducedMotion, spring } from '@/lib/animations'
import { cn } from '@/lib/utils'

const buttonVariants = cva(
  'inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 relative overflow-hidden',
  {
    variants: {
      variant: {
        default: 'bg-primary text-primary-foreground hover:bg-primary/90',
        destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90',
        outline: 'border border-input bg-background hover:bg-accent hover:text-accent-foreground',
        secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
        ghost: 'hover:bg-accent hover:text-accent-foreground',
        link: 'text-primary underline-offset-4 hover:underline',
      },
      size: {
        default: 'h-10 px-4 py-2',
        sm: 'h-9 rounded-md px-3',
        lg: 'h-11 rounded-md px-8',
        icon: 'h-10 w-10',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
  animated?: boolean
  ripple?: boolean
}

export interface MotionButtonProps
  extends Omit<HTMLMotionProps<'button'>, 'size'>,
    VariantProps<typeof buttonVariants> {
  animated?: boolean
  ripple?: boolean
}

// Enhanced animated button with motion
const MotionButton = React.forwardRef<HTMLButtonElement, MotionButtonProps>(
  ({ className, variant, size, animated = true, ripple = true, children, ...props }, ref) => {
    const [isPressed, setIsPressed] = React.useState(false)
    const [ripples, setRipples] = React.useState<Array<{ id: number; x: number; y: number }>>([])
    const buttonRef = React.useRef<HTMLButtonElement>(null)

    // Merge refs
    React.useImperativeHandle(ref, () => buttonRef.current!)

    const shouldAnimate = animated && !prefersReducedMotion()

    const handlePointerDown = (event: React.PointerEvent<HTMLButtonElement>) => {
      if (ripple && shouldAnimate) {
        const rect = buttonRef.current?.getBoundingClientRect()
        if (rect) {
          const x = event.clientX - rect.left
          const y = event.clientY - rect.top
          const newRipple = {
            id: Date.now(),
            x,
            y,
          }
          setRipples((prev) => [...prev, newRipple])

          // Remove ripple after animation
          setTimeout(() => {
            setRipples((prev) => prev.filter((r) => r.id !== newRipple.id))
          }, 600)
        }
      }
      setIsPressed(true)
      props.onPointerDown?.(event)
    }

    const handlePointerUp = (event: React.PointerEvent<HTMLButtonElement>) => {
      setIsPressed(false)
      props.onPointerUp?.(event)
    }

    const animationProps = shouldAnimate
      ? {
          whileHover: { scale: 1.02 },
          whileTap: { scale: 0.98 },
          transition: spring.button,
          animate: {
            scale: isPressed ? 0.95 : 1,
          },
        }
      : {}

    return (
      <motion.button
        ref={buttonRef}
        className={cn(buttonVariants({ variant, size, className }))}
        onPointerDown={handlePointerDown}
        onPointerUp={handlePointerUp}
        {...animationProps}
        {...props}
      >
        {children}

        {/* Ripple effects */}
        {ripple && shouldAnimate && (
          <div className="absolute inset-0 overflow-hidden rounded-[inherit]">
            {ripples.map((ripple) => (
              <motion.span
                key={ripple.id}
                className="absolute bg-white/30 rounded-full"
                style={{
                  left: ripple.x - 10,
                  top: ripple.y - 10,
                  width: 20,
                  height: 20,
                }}
                initial={{ scale: 0, opacity: 0.8 }}
                animate={{ scale: 4, opacity: 0 }}
                transition={{ duration: 0.6, ease: 'easeOut' }}
              />
            ))}
          </div>
        )}
      </motion.button>
    )
  }
)
MotionButton.displayName = 'MotionButton'

// Standard button for backward compatibility
const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  (
    { className, variant, size, asChild = false, animated = false, ripple = false, ...props },
    ref
  ) => {
    if (asChild) {
      return (
        <Slot className={cn(buttonVariants({ variant, size, className }))} ref={ref} {...props} />
      )
    }

    if (animated || ripple) {
      return (
        <MotionButton
          className={className}
          variant={variant}
          size={size}
          animated={animated}
          ripple={ripple}
          ref={ref}
          {...props}
        />
      )
    }

    return (
      <button className={cn(buttonVariants({ variant, size, className }))} ref={ref} {...props} />
    )
  }
)
Button.displayName = 'Button'

export { Button, MotionButton }
