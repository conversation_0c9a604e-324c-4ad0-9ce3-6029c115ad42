import { apiClient } from '@/api/client'

export interface TrainingProgram {
  id: string
  title: string
  description: string
  category: string
  duration: number
  price: number
  vendorId: string
  vendorName?: string
  skills: string[]
  level: 'beginner' | 'intermediate' | 'advanced'
  format: 'online' | 'classroom' | 'hybrid'
  status: 'active' | 'inactive' | 'draft'
  createdAt: string
  updatedAt: string
}

export interface TrainingEnrollment {
  id: string
  userId: string
  programId: string
  status: 'enrolled' | 'in_progress' | 'completed' | 'cancelled'
  progress: number
  startDate: string
  completionDate?: string
  certificateUrl?: string
  feedback?: {
    rating: number
    comment: string
  }
}

export interface SkillGap {
  id: string
  userId: string
  skill: string
  currentLevel: number
  requiredLevel: number
  gap: number
  priority: 'low' | 'medium' | 'high'
  recommendedPrograms: string[]
  createdAt: string
  updatedAt: string
}

export interface TrainingRecommendation {
  programId: string
  program: TrainingProgram
  matchScore: number
  reasons: string[]
  estimatedTimeToComplete: number
  estimatedImpact: 'low' | 'medium' | 'high'
}

export class TrainingService {
  private static readonly BASE_PATH = '/training'

  // Training Programs
  static async listPrograms(filters?: {
    category?: string
    vendorId?: string
    skills?: string[]
    level?: string
    format?: string
    status?: string
    search?: string
    limit?: number
    offset?: number
  }): Promise<{ programs: TrainingProgram[]; total: number }> {
    return apiClient.get(`${TrainingService.BASE_PATH}/programs`, { params: filters })
  }

  static async getProgram(id: string): Promise<TrainingProgram> {
    return apiClient.get<TrainingProgram>(`${TrainingService.BASE_PATH}/programs/${id}`)
  }

  static async createProgram(
    data: Omit<TrainingProgram, 'id' | 'createdAt' | 'updatedAt'>
  ): Promise<TrainingProgram> {
    return apiClient.post<TrainingProgram>(`${TrainingService.BASE_PATH}/programs`, data)
  }

  static async updateProgram(id: string, data: Partial<TrainingProgram>): Promise<TrainingProgram> {
    return apiClient.patch<TrainingProgram>(`${TrainingService.BASE_PATH}/programs/${id}`, data)
  }

  static async deleteProgram(id: string): Promise<void> {
    return apiClient.delete(`${TrainingService.BASE_PATH}/programs/${id}`)
  }

  // Enrollments
  static async enrollInProgram(programId: string): Promise<TrainingEnrollment> {
    return apiClient.post<TrainingEnrollment>(
      `${TrainingService.BASE_PATH}/programs/${programId}/enroll`
    )
  }

  static async getMyEnrollments(filters?: {
    status?: string
    limit?: number
    offset?: number
  }): Promise<{ enrollments: TrainingEnrollment[]; total: number }> {
    return apiClient.get(`${TrainingService.BASE_PATH}/enrollments/my`, { params: filters })
  }

  static async getEnrollment(id: string): Promise<TrainingEnrollment> {
    return apiClient.get<TrainingEnrollment>(`${TrainingService.BASE_PATH}/enrollments/${id}`)
  }

  static async updateEnrollmentProgress(id: string, progress: number): Promise<TrainingEnrollment> {
    return apiClient.patch<TrainingEnrollment>(
      `${TrainingService.BASE_PATH}/enrollments/${id}/progress`,
      { progress }
    )
  }

  static async completeEnrollment(
    id: string,
    feedback?: { rating: number; comment: string }
  ): Promise<TrainingEnrollment> {
    return apiClient.post<TrainingEnrollment>(
      `${TrainingService.BASE_PATH}/enrollments/${id}/complete`,
      { feedback }
    )
  }

  static async cancelEnrollment(id: string): Promise<void> {
    return apiClient.post(`${TrainingService.BASE_PATH}/enrollments/${id}/cancel`)
  }

  // Skill Gap Analysis
  static async analyzeSkillGaps(): Promise<SkillGap[]> {
    return apiClient.post<SkillGap[]>(`${TrainingService.BASE_PATH}/skill-gaps/analyze`)
  }

  static async getMySkillGaps(): Promise<SkillGap[]> {
    return apiClient.get<SkillGap[]>(`${TrainingService.BASE_PATH}/skill-gaps/my`)
  }

  static async updateSkillGap(id: string, data: Partial<SkillGap>): Promise<SkillGap> {
    return apiClient.patch<SkillGap>(`${TrainingService.BASE_PATH}/skill-gaps/${id}`, data)
  }

  // Recommendations
  static async getRecommendations(filters?: {
    skills?: string[]
    maxResults?: number
    includeCompleted?: boolean
  }): Promise<TrainingRecommendation[]> {
    return apiClient.get<TrainingRecommendation[]>(`${TrainingService.BASE_PATH}/recommendations`, {
      params: filters,
    })
  }

  static async dismissRecommendation(programId: string): Promise<void> {
    return apiClient.post(`${TrainingService.BASE_PATH}/recommendations/${programId}/dismiss`)
  }

  // Certificates
  static async getCertificates(): Promise<
    Array<{
      enrollmentId: string
      program: TrainingProgram
      completionDate: string
      certificateUrl: string
    }>
  > {
    return apiClient.get(`${TrainingService.BASE_PATH}/certificates`)
  }

  static async generateCertificate(enrollmentId: string): Promise<{ url: string }> {
    return apiClient.post<{ url: string }>(
      `${TrainingService.BASE_PATH}/certificates/${enrollmentId}/generate`
    )
  }

  // Analytics
  static async getTrainingAnalytics(): Promise<{
    totalPrograms: number
    activeEnrollments: number
    completedPrograms: number
    averageProgress: number
    skillsAcquired: string[]
    timeSpent: number
    upcomingDeadlines: Array<{
      enrollmentId: string
      programTitle: string
      deadline: string
    }>
  }> {
    return apiClient.get(`${TrainingService.BASE_PATH}/analytics/my`)
  }

  static async getDepartmentAnalytics(): Promise<{
    totalEnrollments: number
    activeUsers: number
    completionRate: number
    popularPrograms: Array<{ program: TrainingProgram; enrollmentCount: number }>
    skillDistribution: Record<string, number>
    vendorPerformance: Array<{
      vendorId: string
      vendorName: string
      programCount: number
      averageRating: number
      completionRate: number
    }>
  }> {
    return apiClient.get(`${TrainingService.BASE_PATH}/analytics/department`)
  }
}
