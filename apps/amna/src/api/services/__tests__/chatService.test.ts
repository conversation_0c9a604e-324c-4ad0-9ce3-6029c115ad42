import { beforeEach, describe, expect, it, vi } from 'vitest'
import type { ChatConversation, SendChatRequest } from '@/api/types'
import { API_ERROR_CODES, ENDPOINTS } from '@/config/api.config'
import { mockApiEndpoint, mockApiError, mockNetworkError } from '@/test/api-test-utils'
import { chatService } from '../chatService'

describe('ChatService', () => {
  beforeEach(() => {
    // Reset any mocks
    vi.clearAllMocks()
  })

  describe('sendMessage', () => {
    it('should send a message successfully', async () => {
      const request: SendChatRequest = {
        message: 'Hello, world!',
        conversationId: 'conv_123',
      }

      const result = await chatService.sendMessage(request)

      expect(result).toMatchObject({
        message: {
          role: 'assistant',
          conversationId: 'conv_123',
        },
        conversationId: 'conv_123',
      })
    })

    it('should handle server errors', async () => {
      mockApiError(
        'post',
        ENDPOINTS.chat.send,
        API_ERROR_CODES.SERVER_ERROR,
        'Internal server error',
        500
      )

      const request: SendChatRequest = {
        message: 'Hello, world!',
      }

      await expect(chatService.sendMessage(request)).rejects.toThrow()
    })

    it('should handle network errors', async () => {
      mockNetworkError('post', ENDPOINTS.chat.send)

      const request: SendChatRequest = {
        message: 'Hello, world!',
      }

      await expect(chatService.sendMessage(request)).rejects.toThrow()
    })
  })

  describe('getChatHistory', () => {
    it('should fetch chat history with pagination', async () => {
      const result = await chatService.getChatHistory({
        page: 1,
        limit: 10,
      })

      expect(result).toHaveProperty('data')
      expect(result).toHaveProperty('pagination')
      expect(result.pagination).toMatchObject({
        page: 1,
        limit: 10,
      })
    })

    it('should filter by archived status', async () => {
      const result = await chatService.getChatHistory({
        archived: true,
      })

      expect(result).toHaveProperty('data')
      expect(Array.isArray(result.data)).toBe(true)
    })
  })

  describe('getConversation', () => {
    it('should fetch a specific conversation', async () => {
      const mockConversation: ChatConversation = {
        id: 'conv_123',
        userId: 'user_123',
        title: 'Test Conversation',
        messages: [],
        metadata: {
          archived: false,
          starred: false,
          lastMessageAt: new Date().toISOString(),
          messageCount: 0,
        },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      }

      mockApiEndpoint('get', `${ENDPOINTS.chat.history}/conv_123`, {
        data: mockConversation,
        status: 'success',
        timestamp: new Date().toISOString(),
      })

      const result = await chatService.getConversation('conv_123')

      expect(result).toMatchObject({
        id: 'conv_123',
        title: 'Test Conversation',
      })
    })

    it('should handle 404 errors', async () => {
      mockApiError(
        'get',
        `${ENDPOINTS.chat.history}/conv_999`,
        API_ERROR_CODES.NOT_FOUND,
        'Conversation not found',
        404
      )

      await expect(chatService.getConversation('conv_999')).rejects.toThrow()
    })
  })

  describe('deleteConversation', () => {
    it('should delete a conversation', async () => {
      mockApiEndpoint('delete', ENDPOINTS.chat.delete('conv_123'), null, 204)

      await expect(chatService.deleteConversation('conv_123')).resolves.not.toThrow()
    })
  })

  describe('submitFeedback', () => {
    it('should submit feedback for a message', async () => {
      mockApiEndpoint('post', ENDPOINTS.chat.feedback('msg_123'), null, 204)

      await expect(
        chatService.submitFeedback({
          messageId: 'msg_123',
          rating: 'positive',
          feedback: 'Great response!',
        })
      ).resolves.not.toThrow()
    })
  })

  describe('toggleStar', () => {
    it('should star a conversation', async () => {
      mockApiEndpoint('patch', `${ENDPOINTS.chat.history}/conv_123`, null, 204)

      await expect(chatService.toggleStar('conv_123', true)).resolves.not.toThrow()
    })

    it('should unstar a conversation', async () => {
      mockApiEndpoint('patch', `${ENDPOINTS.chat.history}/conv_123`, null, 204)

      await expect(chatService.toggleStar('conv_123', false)).resolves.not.toThrow()
    })
  })

  describe('exportConversation', () => {
    it('should export conversation as JSON', async () => {
      const mockBlob = new Blob(['{"test": "data"}'], {
        type: 'application/json',
      })
      mockApiEndpoint('get', `${ENDPOINTS.chat.history}/conv_123/export?format=json`, mockBlob)

      const result = await chatService.exportConversation('conv_123', 'json')
      expect(result).toBeInstanceOf(Blob)
    })
  })
})
