import { apiClient, setAuthToken, getAuthToken } from '@/api/client'
import { ENDPOINTS } from '@/config/api.config'

export interface LoginCredentials {
  email: string
  password: string
}

export interface LoginResponse {
  accessToken: string
  refreshToken: string
  user: {
    id: string
    email: string
    firstName: string
    lastName: string
    roles: string[]
    permissions: string[]
  }
}

export interface RegisterData {
  email: string
  password: string
  firstName: string
  lastName: string
  organizationId?: string
}

export interface RefreshTokenResponse {
  accessToken: string
  refreshToken: string
}

export class AuthService {
  static async login(credentials: LoginCredentials): Promise<LoginResponse> {
    const response = await apiClient.post<LoginResponse>(
      ENDPOINTS.auth.login,
      credentials,
      { headers: { skipAuth: true } }
    )

    // Store tokens
    setAuthToken(response.data.accessToken)
    localStorage.setItem('refresh_token', response.data.refreshToken)

    return response.data
  }

  static async register(data: RegisterData): Promise<LoginResponse> {
    const response = await apiClient.post<LoginResponse>(
      ENDPOINTS.auth.register,
      data,
      { headers: { skipAuth: true } }
    )

    // Store tokens
    setAuthToken(response.data.accessToken)
    localStorage.setItem('refresh_token', response.data.refreshToken)

    return response.data
  }

  static async logout(): Promise<void> {
    try {
      await apiClient.post(ENDPOINTS.auth.logout)
    } finally {
      // Clear tokens regardless of API response
      setAuthToken(null)
      localStorage.removeItem('refresh_token')
    }
  }

  static async refreshToken(): Promise<RefreshTokenResponse> {
    const refreshToken = localStorage.getItem('refresh_token')
    if (!refreshToken) {
      throw new Error('No refresh token available')
    }

    const response = await apiClient.post<RefreshTokenResponse>(
      ENDPOINTS.auth.refresh,
      { refreshToken },
      { headers: { skipAuth: true } }
    )

    // Update tokens
    setAuthToken(response.data.accessToken)
    localStorage.setItem('refresh_token', response.data.refreshToken)

    return response.data
  }

  static async getCurrentUser() {
    try {
      const response = await apiClient.get<LoginResponse['user']>(ENDPOINTS.auth.profile)
      return response.data
    } catch (error) {
      // Return null if auth fails, let the app handle it gracefully
      console.warn('Failed to get current user:', error)
      return null
    }
  }

  static async updateProfile(data: Partial<RegisterData>) {
    // TODO: Implement profile update when Command Center supports it
    // The Command Center currently doesn't have a PATCH endpoint for profile updates
    throw new Error('Profile update not yet implemented')
  }

  static isAuthenticated(): boolean {
    return !!getAuthToken()
  }
}
