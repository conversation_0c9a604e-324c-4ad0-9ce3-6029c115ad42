import { ENDPOINTS } from '@/config/api.config'
import { apiClient } from '../client'
import type { ApiResponse, WebSearchRequest, WebSearchResult } from '../types'

export class SearchService {
  /**
   * Perform web search
   */
  async searchWeb(request: WebSearchRequest): Promise<WebSearchResult> {
    const response = await apiClient.post<ApiResponse<WebSearchResult>>(
      ENDPOINTS.search.web,
      request
    )
    return response.data
  }

  /**
   * Search documents
   */
  async searchDocuments(
    query: string,
    options?: {
      fileTypes?: string[]
      folders?: string[]
      dateRange?: {
        from: string
        to: string
      }
      limit?: number
    }
  ): Promise<{
    results: Array<{
      id: string
      title: string
      path: string
      snippet: string
      fileType: string
      size: number
      modifiedAt: string
      relevanceScore: number
    }>
    totalResults: number
    searchTime: number
  }> {
    const response = await apiClient.post<
      ApiResponse<{
        results: Array<{
          id: string
          title: string
          path: string
          snippet: string
          fileType: string
          size: number
          modifiedAt: string
          relevanceScore: number
        }>
        totalResults: number
        searchTime: number
      }>
    >(ENDPOINTS.search.documents, {
      query,
      ...options,
    })
    return response.data
  }

  /**
   * Get search suggestions
   */
  async getSuggestions(query: string, type: 'web' | 'documents' = 'web'): Promise<string[]> {
    const response = await apiClient.get<ApiResponse<string[]>>(
      `${ENDPOINTS.search[type]}/suggestions`,
      {
        params: { query },
      }
    )
    return response.data
  }

  /**
   * Get trending searches
   */
  async getTrending(limit: number = 10): Promise<
    Array<{
      query: string
      count: number
      trend: 'up' | 'down' | 'stable'
    }>
  > {
    const response = await apiClient.get<
      ApiResponse<
        Array<{
          query: string
          count: number
          trend: 'up' | 'down' | 'stable'
        }>
      >
    >(`${ENDPOINTS.search.web}/trending`, {
      params: { limit },
    })
    return response.data
  }

  /**
   * Save search for later
   */
  async saveSearch(query: string, results: WebSearchResult, name?: string): Promise<string> {
    const response = await apiClient.post<ApiResponse<{ id: string }>>(
      `${ENDPOINTS.search.web}/save`,
      {
        query,
        results,
        name: name || query,
      }
    )
    return response.data.id
  }

  /**
   * Get saved searches
   */
  async getSavedSearches(): Promise<
    Array<{
      id: string
      name: string
      query: string
      savedAt: string
      resultCount: number
    }>
  > {
    const response = await apiClient.get<
      ApiResponse<
        Array<{
          id: string
          name: string
          query: string
          savedAt: string
          resultCount: number
        }>
      >
    >(`${ENDPOINTS.search.web}/saved`)
    return response.data
  }

  /**
   * Delete saved search
   */
  async deleteSavedSearch(id: string): Promise<void> {
    await apiClient.delete(`${ENDPOINTS.search.web}/saved/${id}`)
  }

  /**
   * Get search history
   */
  async getSearchHistory(limit: number = 50): Promise<
    Array<{
      query: string
      timestamp: string
      resultCount: number
      clickedResults: number
    }>
  > {
    const response = await apiClient.get<
      ApiResponse<
        Array<{
          query: string
          timestamp: string
          resultCount: number
          clickedResults: number
        }>
      >
    >(`${ENDPOINTS.search.web}/history`, {
      params: { limit },
    })
    return response.data
  }

  /**
   * Clear search history
   */
  async clearSearchHistory(): Promise<void> {
    await apiClient.delete(`${ENDPOINTS.search.web}/history`)
  }
}

// Export singleton instance
export const searchService = new SearchService()
