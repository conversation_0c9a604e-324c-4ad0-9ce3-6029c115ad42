import { apiClient } from '../client'

const apiRequest = apiClient.request.bind(apiClient)

export interface AgentConfig {
  id: string
  name: string
  description: string
  capabilities: string[]
  tools: string[]
  model: string
  temperature?: number
  maxTokens?: number
}

export interface TaskRequest {
  agentId: string
  description: string
  context?: Record<string, any>
  priority?: 'low' | 'medium' | 'high'
}

export interface TaskResponse {
  id: string
  status: 'pending' | 'running' | 'completed' | 'failed'
  result?: any
  error?: string
  progress?: number
  startedAt?: string
  completedAt?: string
}

export interface WorkflowRequest {
  name: string
  description: string
  steps: WorkflowStep[]
  context?: Record<string, any>
}

export interface WorkflowStep {
  name: string
  agentId: string
  task: string
  dependsOn?: string[]
  condition?: string
}

export interface WorkflowResponse {
  id: string
  status: 'pending' | 'running' | 'completed' | 'failed'
  currentStep?: string
  steps: Record<string, TaskResponse>
  result?: any
  error?: string
}

export interface ChatMessage {
  role: 'user' | 'assistant' | 'system'
  content: string
  timestamp?: string
  metadata?: Record<string, any>
}

export interface ChatRequest {
  messages: ChatMessage[]
  agentId?: string
  stream?: boolean
  context?: Record<string, any>
}

export interface ChatResponse {
  id: string
  response: string
  usage?: {
    promptTokens: number
    completionTokens: number
    totalTokens: number
  }
  metadata?: Record<string, any>
}

export class AmnaService {
  private static readonly BASE_PATH = '/api/amna'

  // Agent Management
  static async listAgents(): Promise<AgentConfig[]> {
    return apiRequest<AgentConfig[]>('GET', `${AmnaService.BASE_PATH}/agents`)
  }

  static async getAgent(id: string): Promise<AgentConfig> {
    return apiRequest<AgentConfig>('GET', `${AmnaService.BASE_PATH}/agents/${id}`)
  }

  static async createAgent(config: Omit<AgentConfig, 'id'>): Promise<AgentConfig> {
    return apiRequest<AgentConfig>('POST', `${AmnaService.BASE_PATH}/agents`, config)
  }

  static async updateAgent(id: string, config: Partial<AgentConfig>): Promise<AgentConfig> {
    return apiRequest<AgentConfig>('PATCH', `${AmnaService.BASE_PATH}/agents/${id}`, config)
  }

  static async deleteAgent(id: string): Promise<void> {
    return apiRequest<void>('DELETE', `${AmnaService.BASE_PATH}/agents/${id}`)
  }

  // Task Management
  static async createTask(request: TaskRequest): Promise<TaskResponse> {
    return apiRequest<TaskResponse>('POST', `${AmnaService.BASE_PATH}/tasks`, request)
  }

  static async getTask(id: string): Promise<TaskResponse> {
    return apiRequest<TaskResponse>('GET', `${AmnaService.BASE_PATH}/tasks/${id}`)
  }

  static async listTasks(filters?: {
    status?: string
    agentId?: string
    limit?: number
    offset?: number
  }): Promise<{ tasks: TaskResponse[]; total: number }> {
    return apiRequest('GET', `${AmnaService.BASE_PATH}/tasks`, undefined, { params: filters })
  }

  static async cancelTask(id: string): Promise<void> {
    return apiRequest<void>('POST', `${AmnaService.BASE_PATH}/tasks/${id}/cancel`)
  }

  // Workflow Management
  static async createWorkflow(request: WorkflowRequest): Promise<WorkflowResponse> {
    return apiRequest<WorkflowResponse>('POST', `${AmnaService.BASE_PATH}/workflows`, request)
  }

  static async getWorkflow(id: string): Promise<WorkflowResponse> {
    return apiRequest<WorkflowResponse>('GET', `${AmnaService.BASE_PATH}/workflows/${id}`)
  }

  static async listWorkflows(filters?: {
    status?: string
    limit?: number
    offset?: number
  }): Promise<{ workflows: WorkflowResponse[]; total: number }> {
    return apiRequest('GET', `${AmnaService.BASE_PATH}/workflows`, undefined, { params: filters })
  }

  static async cancelWorkflow(id: string): Promise<void> {
    return apiRequest<void>('POST', `${AmnaService.BASE_PATH}/workflows/${id}/cancel`)
  }

  // Chat Interface
  static async chat(request: ChatRequest): Promise<ChatResponse> {
    return apiRequest<ChatResponse>('POST', `${AmnaService.BASE_PATH}/llm/chat`, request)
  }

  static async getChatHistory(conversationId?: string): Promise<ChatMessage[]> {
    const url = conversationId
      ? `${AmnaService.BASE_PATH}/history/${conversationId}`
      : `${AmnaService.BASE_PATH}/history`
    return apiRequest<ChatMessage[]>('GET', url)
  }

  static async deleteChatHistory(conversationId: string): Promise<void> {
    return apiRequest<void>('DELETE', `${AmnaService.BASE_PATH}/history/${conversationId}`)
  }

  // Tool Registry
  static async listTools(): Promise<
    Array<{
      name: string
      description: string
      category: string
      parameters: Record<string, any>
    }>
  > {
    return apiRequest('GET', `${AmnaService.BASE_PATH}/tools`)
  }

  static async executeTool(name: string, parameters: Record<string, any>): Promise<any> {
    return apiRequest('POST', `${AmnaService.BASE_PATH}/tools/${name}/execute`, parameters)
  }

  // Memory Management
  static async getMemory(key: string): Promise<any> {
    return apiRequest('GET', `${AmnaService.BASE_PATH}/memory/${key}`)
  }

  static async setMemory(key: string, value: any, ttl?: number): Promise<void> {
    return apiRequest<void>('POST', `${AmnaService.BASE_PATH}/memory/${key}`, { value, ttl })
  }

  static async deleteMemory(key: string): Promise<void> {
    return apiRequest<void>('DELETE', `${AmnaService.BASE_PATH}/memory/${key}`)
  }

  static async searchMemory(query: string): Promise<Array<{ key: string; value: any }>> {
    return apiRequest('GET', `${AmnaService.BASE_PATH}/memory/search`, undefined, {
      params: { query },
    })
  }

  // Health Check
  static async health(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy'
    services: Record<string, boolean>
    timestamp: string
  }> {
    return apiRequest('GET', `${AmnaService.BASE_PATH}/health`)
  }

  // AMNA Execute - Main execution endpoint
  static async execute(execution: {
    type: 'agent' | 'task' | 'workflow'
    name: string
    input?: string
    config?: Record<string, any>
    automation?: {
      mode?: 'OFF' | 'SEMI' | 'AUTO'
    }
  }): Promise<any> {
    return apiRequest('POST', `${AmnaService.BASE_PATH}/execute`, execution)
  }

  // Configuration
  static async getConfig(): Promise<any> {
    return apiRequest('GET', `${AmnaService.BASE_PATH}/config`)
  }

  static async setAutomationMode(mode: 'OFF' | 'SEMI' | 'AUTO'): Promise<void> {
    return apiRequest<void>('PUT', `${AmnaService.BASE_PATH}/config/automation-mode`, { mode })
  }

  // Statistics
  static async getStats(): Promise<{
    counts: {
      agents: number
      tasks: number
      workflows: number
      tools: number
      executions: number
    }
    recentExecutions: any[]
    automation: any
  }> {
    return apiRequest('GET', `${AmnaService.BASE_PATH}/stats`)
  }
}
