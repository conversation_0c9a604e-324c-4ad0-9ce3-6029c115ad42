import { ENDPOINTS, FEATURES } from '@/config/api.config'
import { mockChatAPI } from '@/utils/mockChatAPI'
import { apiClient } from '../client'
import type {
  ApiResponse,
  ChatConversation,
  ChatFeedback,
  ChatHistoryRequest,
  ChatMessage,
  PaginatedResponse,
  SendChatRequest,
  SendChatResponse,
} from '../types'

export class ChatService {
  /**
   * Send a chat message
   */
  async sendMessage(request: SendChatRequest): Promise<SendChatResponse> {
    // Use mock API if feature flag is enabled
    if (FEATURES.useMockAPI) {
      const mockResponse = await mockChatAPI.sendMessage(request.message)

      // Transform mock response to match API contract
      const chatMessage: ChatMessage = {
        id: `msg_${Date.now()}`,
        conversationId: request.conversationId || `conv_${Date.now()}`,
        role: 'assistant',
        content: mockResponse.content,
        metadata: {
          sources: mockResponse.sources,
          followUpQuestions: mockResponse.followUpQuestions,
          responseType: mockResponse.type,
          processingTime: mockResponse.processingTime,
        },
        createdAt: new Date().toISOString(),
      }

      return {
        message: chatMessage,
        conversationId: chatMessage.conversationId,
        usage: {
          promptTokens: 0,
          completionTokens: 0,
          totalTokens: 0,
        },
      }
    }

    // Real API call
    const response = await apiClient.post<ApiResponse<SendChatResponse>>(
      ENDPOINTS.chat.send,
      request
    )

    return response.data
  }

  /**
   * Get chat history with pagination
   */
  async getChatHistory(
    params: ChatHistoryRequest = {}
  ): Promise<PaginatedResponse<ChatConversation>> {
    const queryParams = new URLSearchParams()

    // Build query parameters
    if (params.page) {
      queryParams.append('page', params.page.toString())
    }
    if (params.limit) {
      queryParams.append('limit', params.limit.toString())
    }
    if (params.archived !== undefined) {
      queryParams.append('archived', params.archived.toString())
    }
    if (params.starred !== undefined) {
      queryParams.append('starred', params.starred.toString())
    }
    if (params.search) {
      queryParams.append('search', params.search)
    }
    if (params.sortBy) {
      queryParams.append('sortBy', params.sortBy)
    }
    if (params.sortOrder) {
      queryParams.append('sortOrder', params.sortOrder)
    }

    const response = await apiClient.get<PaginatedResponse<ChatConversation>>(
      `${ENDPOINTS.chat.history}?${queryParams.toString()}`
    )

    return response
  }

  /**
   * Get a specific conversation
   */
  async getConversation(conversationId: string): Promise<ChatConversation> {
    const response = await apiClient.get<ApiResponse<ChatConversation>>(
      `${ENDPOINTS.chat.history}/${conversationId}`
    )

    return response.data
  }

  /**
   * Delete a conversation
   */
  async deleteConversation(conversationId: string): Promise<void> {
    await apiClient.delete(ENDPOINTS.chat.delete(conversationId))
  }

  /**
   * Delete a specific message
   */
  async deleteMessage(messageId: string): Promise<void> {
    await apiClient.delete(`${ENDPOINTS.chat.send}/${messageId}`)
  }

  /**
   * Submit feedback for a message
   */
  async submitFeedback(feedback: ChatFeedback): Promise<void> {
    await apiClient.post(ENDPOINTS.chat.feedback(feedback.messageId), feedback)
  }

  /**
   * Star/unstar a conversation
   */
  async toggleStar(conversationId: string, starred: boolean): Promise<void> {
    await apiClient.patch(`${ENDPOINTS.chat.history}/${conversationId}`, {
      starred,
    })
  }

  /**
   * Archive/unarchive a conversation
   */
  async toggleArchive(conversationId: string, archived: boolean): Promise<void> {
    await apiClient.patch(`${ENDPOINTS.chat.history}/${conversationId}`, {
      archived,
    })
  }

  /**
   * Update conversation title
   */
  async updateTitle(conversationId: string, title: string): Promise<void> {
    await apiClient.patch(`${ENDPOINTS.chat.history}/${conversationId}`, {
      title,
    })
  }

  /**
   * Export conversation
   */
  async exportConversation(
    conversationId: string,
    format: 'json' | 'markdown' | 'pdf' = 'json'
  ): Promise<Blob> {
    const response = await apiClient.get(`${ENDPOINTS.chat.history}/${conversationId}/export`, {
      params: { format },
      responseType: 'blob',
    })

    return response as unknown as Blob
  }

  /**
   * Clear all chat history
   */
  async clearHistory(): Promise<void> {
    await apiClient.delete(ENDPOINTS.chat.history)
  }

  /**
   * Get chat statistics
   */
  async getStatistics(): Promise<{
    totalConversations: number
    totalMessages: number
    averageMessagesPerConversation: number
    mostActiveDay: string
    topTopics: string[]
  }> {
    const response = await apiClient.get<
      ApiResponse<{
        totalConversations: number
        totalMessages: number
        averageMessagesPerConversation: number
        mostActiveDay: string
        topTopics: string[]
      }>
    >(`${ENDPOINTS.chat.history}/stats`)

    return response.data
  }
}

// Export singleton instance
export const chatService = new ChatService()
