import { apiClient } from '@/api/client'

export interface Vendor {
  id: string
  name: string
  description: string
  contactEmail: string
  contactPhone: string
  website?: string
  address?: string
  categories: string[]
  specializations: string[]
  status: 'active' | 'inactive' | 'pending' | 'suspended'
  rating: number
  totalPrograms: number
  certifications: string[]
  establishedYear?: number
  employeeCount?: string
  logo?: string
  documents: VendorDocument[]
  createdAt: string
  updatedAt: string
}

export interface VendorDocument {
  id: string
  type: 'license' | 'certification' | 'insurance' | 'agreement' | 'other'
  name: string
  url: string
  expiryDate?: string
  status: 'valid' | 'expired' | 'pending'
}

export interface VendorPerformance {
  vendorId: string
  period: string
  metrics: {
    totalPrograms: number
    activePrograms: number
    totalEnrollments: number
    completionRate: number
    averageRating: number
    revenueGenerated: number
    studentSatisfaction: number
  }
  trends: {
    enrollmentGrowth: number
    ratingTrend: number
    completionTrend: number
  }
}

export interface VendorContract {
  id: string
  vendorId: string
  contractNumber: string
  startDate: string
  endDate: string
  value: number
  status: 'draft' | 'active' | 'expired' | 'terminated'
  terms: string
  documents: string[]
  renewalDate?: string
  createdAt: string
  updatedAt: string
}

export interface VendorEvaluation {
  id: string
  vendorId: string
  evaluatorId: string
  evaluatorName: string
  period: string
  scores: {
    quality: number
    delivery: number
    support: number
    value: number
    innovation: number
  }
  overallScore: number
  strengths: string[]
  improvements: string[]
  recommendation: 'continue' | 'review' | 'terminate'
  comments: string
  createdAt: string
}

export class VendorService {
  private static readonly BASE_PATH = '/vendors'

  // Vendor Management
  static async listVendors(filters?: {
    status?: string
    category?: string
    search?: string
    rating?: number
    limit?: number
    offset?: number
  }): Promise<{ vendors: Vendor[]; total: number }> {
    return apiClient.get(`${VendorService.BASE_PATH}`, { params: filters })
  }

  static async getVendor(id: string): Promise<Vendor> {
    return apiClient.get<Vendor>(`${VendorService.BASE_PATH}/${id}`)
  }

  static async createVendor(
    data: Omit<Vendor, 'id' | 'createdAt' | 'updatedAt' | 'rating' | 'totalPrograms'>
  ): Promise<Vendor> {
    return apiClient.post<Vendor>(`${VendorService.BASE_PATH}`, data)
  }

  static async updateVendor(id: string, data: Partial<Vendor>): Promise<Vendor> {
    return apiClient.patch<Vendor>(`${VendorService.BASE_PATH}/${id}`, data)
  }

  static async deleteVendor(id: string): Promise<void> {
    return apiClient.delete(`${VendorService.BASE_PATH}/${id}`)
  }

  // Vendor Status Management
  static async activateVendor(id: string): Promise<Vendor> {
    return apiClient.post<Vendor>(`${VendorService.BASE_PATH}/${id}/activate`)
  }

  static async suspendVendor(id: string, reason: string): Promise<Vendor> {
    return apiClient.post<Vendor>(`${VendorService.BASE_PATH}/${id}/suspend`, { reason })
  }

  // Documents
  static async uploadDocument(
    vendorId: string,
    file: File,
    documentType: string
  ): Promise<VendorDocument> {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('type', documentType)

    return apiClient.post<VendorDocument>(
      `${VendorService.BASE_PATH}/${vendorId}/documents`,
      formData,
      {
        headers: { 'Content-Type': 'multipart/form-data' },
      }
    )
  }

  static async deleteDocument(vendorId: string, documentId: string): Promise<void> {
    return apiClient.delete(`${VendorService.BASE_PATH}/${vendorId}/documents/${documentId}`)
  }

  // Performance
  static async getVendorPerformance(vendorId: string, period?: string): Promise<VendorPerformance> {
    return apiClient.get<VendorPerformance>(`${VendorService.BASE_PATH}/${vendorId}/performance`, {
      params: { period },
    })
  }

  static async getPerformanceComparison(
    vendorIds: string[],
    period?: string
  ): Promise<VendorPerformance[]> {
    return apiClient.post<VendorPerformance[]>(`${VendorService.BASE_PATH}/performance/compare`, {
      vendorIds,
      period,
    })
  }

  // Contracts
  static async listContracts(
    vendorId?: string,
    filters?: {
      status?: string
      limit?: number
      offset?: number
    }
  ): Promise<{ contracts: VendorContract[]; total: number }> {
    const params = vendorId ? { vendorId, ...filters } : filters
    return apiClient.get(`${VendorService.BASE_PATH}/contracts`, { params })
  }

  static async getContract(contractId: string): Promise<VendorContract> {
    return apiClient.get<VendorContract>(`${VendorService.BASE_PATH}/contracts/${contractId}`)
  }

  static async createContract(
    data: Omit<VendorContract, 'id' | 'createdAt' | 'updatedAt'>
  ): Promise<VendorContract> {
    return apiClient.post<VendorContract>(`${VendorService.BASE_PATH}/contracts`, data)
  }

  static async updateContract(
    contractId: string,
    data: Partial<VendorContract>
  ): Promise<VendorContract> {
    return apiClient.patch<VendorContract>(
      `${VendorService.BASE_PATH}/contracts/${contractId}`,
      data
    )
  }

  static async renewContract(contractId: string, newEndDate: string): Promise<VendorContract> {
    return apiClient.post<VendorContract>(
      `${VendorService.BASE_PATH}/contracts/${contractId}/renew`,
      { newEndDate }
    )
  }

  // Evaluations
  static async listEvaluations(vendorId: string): Promise<VendorEvaluation[]> {
    return apiClient.get<VendorEvaluation[]>(`${VendorService.BASE_PATH}/${vendorId}/evaluations`)
  }

  static async createEvaluation(
    data: Omit<VendorEvaluation, 'id' | 'createdAt' | 'evaluatorName'>
  ): Promise<VendorEvaluation> {
    return apiClient.post<VendorEvaluation>(
      `${VendorService.BASE_PATH}/${data.vendorId}/evaluations`,
      data
    )
  }

  static async getEvaluation(evaluationId: string): Promise<VendorEvaluation> {
    return apiClient.get<VendorEvaluation>(`${VendorService.BASE_PATH}/evaluations/${evaluationId}`)
  }

  // Analytics
  static async getVendorAnalytics(): Promise<{
    totalVendors: number
    activeVendors: number
    vendorsByCategory: Record<string, number>
    averageRating: number
    topPerformers: Array<{ vendor: Vendor; score: number }>
    recentlyAdded: Vendor[]
    upcomingRenewals: Array<{
      vendor: Vendor
      contract: VendorContract
      daysUntilRenewal: number
    }>
  }> {
    return apiClient.get(`${VendorService.BASE_PATH}/analytics`)
  }

  // Search and Recommendations
  static async searchVendors(
    query: string,
    filters?: {
      categories?: string[]
      minRating?: number
      specializations?: string[]
    }
  ): Promise<Vendor[]> {
    return apiClient.post<Vendor[]>(`${VendorService.BASE_PATH}/search`, {
      query,
      ...filters,
    })
  }

  static async getRecommendedVendors(requirements: {
    categories: string[]
    skills: string[]
    budget?: number
  }): Promise<Array<{ vendor: Vendor; matchScore: number; reasons: string[] }>> {
    return apiClient.post(`${VendorService.BASE_PATH}/recommendations`, requirements)
  }
}
