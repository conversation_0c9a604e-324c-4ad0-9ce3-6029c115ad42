import { apiClient } from '@/api/client'

export interface WinsSubmission {
  id: string
  userId: string
  userName?: string
  userDepartment?: string
  weekStartDate: string
  achievements: Achievement[]
  challenges: Challenge[]
  learnings: string[]
  nextWeekGoals: string[]
  recognitions?: Recognition[]
  status: 'draft' | 'submitted' | 'reviewed' | 'approved' | 'rejected'
  reviewerComments?: string
  reviewedBy?: string
  reviewedAt?: string
  score?: number
  badges?: Badge[]
  createdAt: string
  updatedAt: string
}

export interface Achievement {
  id: string
  title: string
  description: string
  impact: 'low' | 'medium' | 'high'
  category: string
  metrics?: Record<string, any>
  collaborators?: string[]
}

export interface Challenge {
  id: string
  description: string
  status: 'ongoing' | 'resolved' | 'escalated'
  resolution?: string
  support_needed?: string
}

export interface Recognition {
  id: string
  employeeName: string
  reason: string
  type: 'peer' | 'team' | 'cross-team'
}

export interface Badge {
  id: string
  name: string
  description: string
  icon: string
  criteria: string
  awardedAt: string
}

export interface WinsReport {
  period: string
  totalSubmissions: number
  participationRate: number
  departmentBreakdown: Record<string, number>
  topAchievements: Achievement[]
  commonChallenges: Array<{
    category: string
    count: number
    examples: string[]
  }>
  recognitionStats: {
    totalRecognitions: number
    byType: Record<string, number>
    topRecognizers: Array<{ userId: string; name: string; count: number }>
    topRecognized: Array<{ employeeName: string; count: number }>
  }
  learningTrends: Array<{ topic: string; mentions: number }>
  badgesAwarded: Array<{ badge: Badge; count: number }>
}

export interface WinsLeaderboard {
  period: string
  entries: Array<{
    userId: string
    userName: string
    department: string
    score: number
    consecutiveWeeks: number
    totalAchievements: number
    badges: Badge[]
    rank: number
    trend: 'up' | 'down' | 'stable'
  }>
}

export interface WinsTemplate {
  id: string
  name: string
  description: string
  structure: {
    achievementPrompts: string[]
    challengePrompts: string[]
    learningPrompts: string[]
    goalPrompts: string[]
  }
  isDefault: boolean
  createdAt: string
}

export class WinsService {
  private static readonly BASE_PATH = '/wins'

  // Submissions
  static async createSubmission(
    data: Omit<WinsSubmission, 'id' | 'createdAt' | 'updatedAt' | 'status'>
  ): Promise<WinsSubmission> {
    return apiClient.post<WinsSubmission>(`${WinsService.BASE_PATH}/submissions`, data)
  }

  static async getMySubmissions(filters?: {
    status?: string
    weekStartDate?: string
    limit?: number
    offset?: number
  }): Promise<{ submissions: WinsSubmission[]; total: number }> {
    return apiClient.get(`${WinsService.BASE_PATH}/submissions/my`, { params: filters })
  }

  static async getSubmission(id: string): Promise<WinsSubmission> {
    return apiClient.get<WinsSubmission>(`${WinsService.BASE_PATH}/submissions/${id}`)
  }

  static async updateSubmission(
    id: string,
    data: Partial<WinsSubmission>
  ): Promise<WinsSubmission> {
    return apiClient.patch<WinsSubmission>(`${WinsService.BASE_PATH}/submissions/${id}`, data)
  }

  static async submitForReview(id: string): Promise<WinsSubmission> {
    return apiClient.post<WinsSubmission>(`${WinsService.BASE_PATH}/submissions/${id}/submit`)
  }

  static async deleteSubmission(id: string): Promise<void> {
    return apiClient.delete(`${WinsService.BASE_PATH}/submissions/${id}`)
  }

  // Review Process (Manager)
  static async getSubmissionsForReview(filters?: {
    status?: string
    department?: string
    weekStartDate?: string
    limit?: number
    offset?: number
  }): Promise<{ submissions: WinsSubmission[]; total: number }> {
    return apiClient.get(`${WinsService.BASE_PATH}/submissions/review`, { params: filters })
  }

  static async reviewSubmission(
    id: string,
    review: {
      status: 'approved' | 'rejected'
      comments?: string
      score?: number
    }
  ): Promise<WinsSubmission> {
    return apiClient.post<WinsSubmission>(
      `${WinsService.BASE_PATH}/submissions/${id}/review`,
      review
    )
  }

  static async bulkApprove(
    submissionIds: string[]
  ): Promise<{ success: string[]; failed: string[] }> {
    return apiClient.post(`${WinsService.BASE_PATH}/submissions/bulk-approve`, { submissionIds })
  }

  // Reports and Analytics
  static async getWeeklyReport(weekStartDate?: string): Promise<WinsReport> {
    return apiClient.get<WinsReport>(`${WinsService.BASE_PATH}/reports/weekly`, {
      params: { weekStartDate },
    })
  }

  static async getMonthlyReport(month: string, year: number): Promise<WinsReport> {
    return apiClient.get<WinsReport>(`${WinsService.BASE_PATH}/reports/monthly`, {
      params: { month, year },
    })
  }

  static async getLeaderboard(
    period: 'week' | 'month' | 'quarter' | 'year'
  ): Promise<WinsLeaderboard> {
    return apiClient.get<WinsLeaderboard>(`${WinsService.BASE_PATH}/leaderboard`, {
      params: { period },
    })
  }

  static async getMyAnalytics(): Promise<{
    totalSubmissions: number
    currentStreak: number
    longestStreak: number
    totalAchievements: number
    totalRecognitionsGiven: number
    totalRecognitionsReceived: number
    badges: Badge[]
    recentActivity: Array<{
      type: 'submission' | 'recognition' | 'badge'
      date: string
      details: any
    }>
    performanceTrend: Array<{
      week: string
      score: number
    }>
  }> {
    return apiClient.get(`${WinsService.BASE_PATH}/analytics/my`)
  }

  // Templates
  static async getTemplates(): Promise<WinsTemplate[]> {
    return apiClient.get<WinsTemplate[]>(`${WinsService.BASE_PATH}/templates`)
  }

  static async getTemplate(id: string): Promise<WinsTemplate> {
    return apiClient.get<WinsTemplate>(`${WinsService.BASE_PATH}/templates/${id}`)
  }

  static async createTemplate(data: Omit<WinsTemplate, 'id' | 'createdAt'>): Promise<WinsTemplate> {
    return apiClient.post<WinsTemplate>(`${WinsService.BASE_PATH}/templates`, data)
  }

  // Badges
  static async getBadges(): Promise<Badge[]> {
    return apiClient.get<Badge[]>(`${WinsService.BASE_PATH}/badges`)
  }

  static async getMyBadges(): Promise<Badge[]> {
    return apiClient.get<Badge[]>(`${WinsService.BASE_PATH}/badges/my`)
  }

  // Recognition
  static async recognizeColleague(data: {
    employeeName: string
    reason: string
    type: 'peer' | 'team' | 'cross-team'
    submissionId?: string
  }): Promise<Recognition> {
    return apiClient.post<Recognition>(`${WinsService.BASE_PATH}/recognitions`, data)
  }

  static async getRecognitions(filters?: {
    given?: boolean
    received?: boolean
    limit?: number
    offset?: number
  }): Promise<{ recognitions: Recognition[]; total: number }> {
    return apiClient.get(`${WinsService.BASE_PATH}/recognitions`, { params: filters })
  }

  // Export
  static async exportSubmissions(filters: {
    startDate: string
    endDate: string
    format: 'pdf' | 'excel' | 'csv'
    department?: string
  }): Promise<{ url: string }> {
    return apiClient.post<{ url: string }>(`${WinsService.BASE_PATH}/export`, filters)
  }

  // Notifications
  static async getReminders(): Promise<
    Array<{
      type: 'submission_due' | 'review_pending' | 'recognition_received'
      message: string
      dueDate?: string
      actionUrl?: string
    }>
  > {
    return apiClient.get(`${WinsService.BASE_PATH}/reminders`)
  }

  static async updateNotificationPreferences(preferences: {
    submissionReminders: boolean
    reviewReminders: boolean
    recognitionNotifications: boolean
    weeklyDigest: boolean
  }): Promise<void> {
    return apiClient.put(`${WinsService.BASE_PATH}/notifications/preferences`, preferences)
  }
}
