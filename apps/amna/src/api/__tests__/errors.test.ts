import { describe, expect, it } from 'vitest'
import { API_ERROR_CODES } from '@/config/api.config'
import { ApiError, handleApiError, isApiError } from '../errors'

describe('ApiError', () => {
  describe('constructor', () => {
    it('should create an ApiError instance', () => {
      const error = new ApiError(API_ERROR_CODES.SERVER_ERROR, 'Test error', 500, {
        detail: 'test',
      })

      expect(error).toBeInstanceOf(ApiError)
      expect(error.code).toBe(API_ERROR_CODES.SERVER_ERROR)
      expect(error.message).toBe('Test error')
      expect(error.statusCode).toBe(500)
      expect(error.details).toEqual({ detail: 'test' })
    })
  })

  describe('fromResponse', () => {
    it('should create ApiError from 400 response', () => {
      const response = new Response(null, { status: 400 })
      const error = ApiError.fromResponse(response)

      expect(error.code).toBe(API_ERROR_CODES.VALIDATION_ERROR)
      expect(error.statusCode).toBe(400)
    })

    it('should create ApiError from 401 response', () => {
      const response = new Response(null, { status: 401 })
      const error = ApiError.fromResponse(response)

      expect(error.code).toBe(API_ERROR_CODES.UNAUTHORIZED)
      expect(error.statusCode).toBe(401)
    })

    it('should create ApiError from 403 response', () => {
      const response = new Response(null, { status: 403 })
      const error = ApiError.fromResponse(response)

      expect(error.code).toBe(API_ERROR_CODES.FORBIDDEN)
      expect(error.statusCode).toBe(403)
    })

    it('should create ApiError from 404 response', () => {
      const response = new Response(null, { status: 404 })
      const error = ApiError.fromResponse(response)

      expect(error.code).toBe(API_ERROR_CODES.NOT_FOUND)
      expect(error.statusCode).toBe(404)
    })

    it('should create ApiError from 429 response', () => {
      const response = new Response(null, { status: 429 })
      const error = ApiError.fromResponse(response)

      expect(error.code).toBe(API_ERROR_CODES.RATE_LIMITED)
      expect(error.statusCode).toBe(429)
    })

    it('should create ApiError from 500 response', () => {
      const response = new Response(null, { status: 500 })
      const error = ApiError.fromResponse(response)

      expect(error.code).toBe(API_ERROR_CODES.SERVER_ERROR)
      expect(error.statusCode).toBe(500)
    })

    it('should use server-provided error message', () => {
      const response = new Response(null, { status: 400 })
      const data = { message: 'Custom error message' }
      const error = ApiError.fromResponse(response, data)

      expect(error.message).toBe('Custom error message')
    })
  })

  describe('networkError', () => {
    it('should create network error', () => {
      const originalError = new Error('Network failed')
      const error = ApiError.networkError(originalError)

      expect(error.code).toBe(API_ERROR_CODES.NETWORK_ERROR)
      expect(error.message).toBe('Network connection failed')
      expect(error.details).toBe(originalError)
    })
  })

  describe('timeout', () => {
    it('should create timeout error', () => {
      const error = ApiError.timeout()

      expect(error.code).toBe(API_ERROR_CODES.TIMEOUT)
      expect(error.message).toBe('Request timed out')
    })
  })

  describe('isRetryable', () => {
    it('should identify retryable errors', () => {
      const networkError = ApiError.networkError(new Error())
      const timeoutError = ApiError.timeout()
      const serverError = new ApiError(API_ERROR_CODES.SERVER_ERROR, 'Server error')

      expect(networkError.isRetryable()).toBe(true)
      expect(timeoutError.isRetryable()).toBe(true)
      expect(serverError.isRetryable()).toBe(true)
    })

    it('should identify non-retryable errors', () => {
      const validationError = new ApiError(API_ERROR_CODES.VALIDATION_ERROR, 'Invalid')
      const unauthorizedError = new ApiError(API_ERROR_CODES.UNAUTHORIZED, 'Unauthorized')

      expect(validationError.isRetryable()).toBe(false)
      expect(unauthorizedError.isRetryable()).toBe(false)
    })
  })

  describe('toJSON', () => {
    it('should serialize to JSON', () => {
      const error = new ApiError(API_ERROR_CODES.SERVER_ERROR, 'Test error', 500, {
        detail: 'test',
      })

      const json = error.toJSON()

      expect(json).toEqual({
        code: API_ERROR_CODES.SERVER_ERROR,
        message: 'Test error',
        statusCode: 500,
        details: { detail: 'test' },
      })
    })
  })
})

describe('isApiError', () => {
  it('should identify ApiError instances', () => {
    const apiError = new ApiError(API_ERROR_CODES.SERVER_ERROR, 'Error')
    const regularError = new Error('Regular error')

    expect(isApiError(apiError)).toBe(true)
    expect(isApiError(regularError)).toBe(false)
    expect(isApiError(null)).toBe(false)
    expect(isApiError(undefined)).toBe(false)
    expect(isApiError({})).toBe(false)
  })
})

describe('handleApiError', () => {
  it('should return ApiError if already an ApiError', () => {
    const apiError = new ApiError(API_ERROR_CODES.SERVER_ERROR, 'Error')
    const result = handleApiError(apiError)

    expect(result).toBe(apiError)
  })

  it('should convert Error to ApiError', () => {
    const error = new Error('Test error')
    const result = handleApiError(error)

    expect(result).toBeInstanceOf(ApiError)
    expect(result.code).toBe(API_ERROR_CODES.NETWORK_ERROR)
    expect(result.details).toBe(error)
  })

  it('should handle unknown errors', () => {
    const result = handleApiError('Unknown error')

    expect(result).toBeInstanceOf(ApiError)
    expect(result.code).toBe(API_ERROR_CODES.SERVER_ERROR)
    expect(result.message).toBe('An unexpected error occurred')
    expect(result.details).toBe('Unknown error')
  })
})
