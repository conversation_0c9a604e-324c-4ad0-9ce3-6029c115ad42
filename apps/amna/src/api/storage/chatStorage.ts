import { FEATURES } from '@/config/api.config'
import { chatService } from '../services/chatService'
import type { ChatConversation, ChatMessage, SendChatRequest } from '../types'
import { db } from './database'

export class ChatStorage {
  private syncInProgress = false
  private syncInterval: number | null = null

  /**
   * Initialize the storage and start sync if online
   */
  async initialize(): Promise<void> {
    await db.initialize()

    // Start periodic sync if offline mode is enabled
    if (FEATURES.enableOfflineMode) {
      this.startPeriodicSync()
    }

    // Listen for online/offline events
    window.addEventListener('online', () => this.handleOnline())
    window.addEventListener('offline', () => this.handleOffline())
  }

  /**
   * Save a conversation to local storage
   */
  async saveConversation(conversation: ChatConversation): Promise<void> {
    await db.saveConversation(conversation)

    // If online, sync immediately
    if (navigator.onLine && !FEATURES.useMockAPI) {
      await this.syncConversation(conversation.id)
    }
  }

  /**
   * Get a conversation from local storage
   */
  async getConversation(id: string): Promise<ChatConversation | undefined> {
    // Try local first
    let conversation = await db.getConversation(id)

    // If not found locally and online, fetch from server
    if (!conversation && navigator.onLine && !FEATURES.useMockAPI) {
      try {
        conversation = await chatService.getConversation(id)
        if (conversation) {
          await db.saveConversation(conversation)
        }
      } catch (error) {
        console.error('Failed to fetch conversation from server:', error)
      }
    }

    return conversation
  }

  /**
   * Get all conversations
   */
  getAllConversations(options?: {
    archived?: boolean
    starred?: boolean
    limit?: number
  }): Promise<ChatConversation[]> {
    return db.getAllConversations(options)
  }

  /**
   * Save a message locally and queue for sync
   */
  async saveMessage(message: ChatMessage): Promise<void> {
    await db.saveMessage(message)

    // Add to pending sync if offline
    if (!navigator.onLine || FEATURES.useMockAPI) {
      await db.addPendingSync({
        type: 'message',
        action: 'create',
        data: message,
      })
    }
  }

  /**
   * Send a message (offline-capable)
   */
  async sendMessage(request: SendChatRequest): Promise<ChatMessage> {
    // Create user message
    const userMessage: ChatMessage = {
      id: `msg_${Date.now()}_user`,
      conversationId: request.conversationId || `conv_${Date.now()}`,
      role: 'user',
      content: request.message,
      createdAt: new Date().toISOString(),
    }

    // Save user message locally
    await this.saveMessage(userMessage)

    // Update conversation
    let conversation = await db.getConversation(userMessage.conversationId)
    if (!conversation) {
      conversation = {
        id: userMessage.conversationId,
        userId: 'current_user', // This should come from auth context
        title: request.message.slice(0, 50) + '...',
        messages: [userMessage],
        metadata: {
          archived: false,
          starred: false,
          lastMessageAt: userMessage.createdAt,
          messageCount: 1,
        },
        createdAt: userMessage.createdAt,
        updatedAt: userMessage.createdAt,
      }
    } else {
      conversation.messages.push(userMessage)
      conversation.metadata.messageCount++
      conversation.metadata.lastMessageAt = userMessage.createdAt
      conversation.updatedAt = userMessage.createdAt
    }
    await db.saveConversation(conversation)

    // If online, send to server
    if (navigator.onLine || FEATURES.useMockAPI) {
      try {
        const response = await chatService.sendMessage(request)

        // Save assistant message
        await this.saveMessage(response.message)

        // Update conversation with assistant message
        conversation.messages.push(response.message)
        conversation.metadata.messageCount++
        conversation.metadata.lastMessageAt = response.message.createdAt
        conversation.updatedAt = response.message.createdAt
        await db.saveConversation(conversation)

        return response.message
      } catch (error) {
        console.error('Failed to send message:', error)

        // Create a placeholder response for offline mode
        const placeholderResponse: ChatMessage = {
          id: `msg_${Date.now()}_offline`,
          conversationId: userMessage.conversationId,
          role: 'assistant',
          content:
            "I'm currently offline. Your message has been saved and will be processed when the connection is restored.",
          metadata: {
            responseType: 'general',
          },
          createdAt: new Date().toISOString(),
        }

        await this.saveMessage(placeholderResponse)
        return placeholderResponse
      }
    } else {
      // Offline mode - return placeholder
      const placeholderResponse: ChatMessage = {
        id: `msg_${Date.now()}_offline`,
        conversationId: userMessage.conversationId,
        role: 'assistant',
        content:
          "You're currently offline. Your message has been saved and will be sent when you're back online.",
        metadata: {
          responseType: 'general',
        },
        createdAt: new Date().toISOString(),
      }

      await this.saveMessage(placeholderResponse)
      return placeholderResponse
    }
  }

  /**
   * Delete a conversation locally and queue for sync
   */
  async deleteConversation(id: string): Promise<void> {
    await db.deleteConversation(id)

    if (!navigator.onLine || FEATURES.useMockAPI) {
      await db.addPendingSync({
        type: 'conversation',
        action: 'delete',
        data: { id },
      })
    } else {
      try {
        await chatService.deleteConversation(id)
      } catch (error) {
        console.error('Failed to delete conversation on server:', error)
        await db.addPendingSync({
          type: 'conversation',
          action: 'delete',
          data: { id },
        })
      }
    }
  }

  /**
   * Sync a specific conversation with the server
   */
  private async syncConversation(id: string): Promise<void> {
    if (FEATURES.useMockAPI) {
      return
    }

    try {
      const localConversation = await db.getConversation(id)
      if (!localConversation) {
        return
      }

      // For now, we'll just ensure the server has the latest version
      // In a real implementation, this would handle conflict resolution
      // Sync logging removed for production
    } catch (error) {
      console.error('Failed to sync conversation:', error)
    }
  }

  /**
   * Sync all pending operations with the server
   */
  async syncPendingOperations(): Promise<void> {
    if (this.syncInProgress || !navigator.onLine || FEATURES.useMockAPI) {
      return
    }

    this.syncInProgress = true

    try {
      const pendingOps = await db.getPendingSync()

      for (const op of pendingOps) {
        try {
          switch (op.type) {
            case 'message':
              if (op.action === 'create') {
                // Sync message creation
                // Sync logging removed for production
                // await chatService.sendMessage(op.data);
              }
              break

            case 'conversation':
              if (op.action === 'delete') {
                await chatService.deleteConversation((op.data as { id: string }).id)
              }
              break

            case 'feedback':
              // Sync feedback
              // Sync logging removed for production
              break
          }

          // Remove from pending sync on success
          await db.removePendingSync(op.id)
        } catch (error) {
          console.error('Failed to sync operation:', op, error)

          // Increment retry count
          await db.updatePendingSyncRetries(op.id, op.retries + 1)

          // Remove if too many retries
          if (op.retries >= 3) {
            await db.removePendingSync(op.id)
          }
        }
      }
    } finally {
      this.syncInProgress = false
    }
  }

  /**
   * Start periodic sync
   */
  private startPeriodicSync(): void {
    if (this.syncInterval) {
      return
    }

    // Sync every 30 seconds when online
    this.syncInterval = window.setInterval(() => {
      if (navigator.onLine) {
        this.syncPendingOperations()
      }
    }, 30000)
  }

  /**
   * Stop periodic sync
   */
  private stopPeriodicSync(): void {
    if (this.syncInterval) {
      window.clearInterval(this.syncInterval)
      this.syncInterval = null
    }
  }

  /**
   * Handle coming back online
   */
  private async handleOnline(): Promise<void> {
    // Back online - starting sync
    await this.syncPendingOperations()
    this.startPeriodicSync()
  }

  /**
   * Handle going offline
   */
  private handleOffline(): void {
    // Gone offline - stopping sync
    this.stopPeriodicSync()
  }

  /**
   * Get storage statistics
   */
  getStorageInfo() {
    return db.getStorageInfo()
  }

  /**
   * Clear all local data
   */
  async clearAll(): Promise<void> {
    await db.clearAll()
  }

  /**
   * Export conversations for backup
   */
  async exportConversations(): Promise<string> {
    const conversations = await db.getAllConversations()
    return JSON.stringify(conversations, null, 2)
  }

  /**
   * Import conversations from backup
   */
  async importConversations(jsonData: string): Promise<void> {
    try {
      const conversations = JSON.parse(jsonData) as ChatConversation[]

      for (const conversation of conversations) {
        await db.saveConversation(conversation)

        // Save all messages
        for (const message of conversation.messages) {
          await db.saveMessage(message)
        }
      }
    } catch {
      throw new Error('Invalid backup data')
    }
  }
}

// Export singleton instance
export const chatStorage = new ChatStorage()
