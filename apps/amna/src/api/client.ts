import axios, { type AxiosInstance, type AxiosRequestConfig } from 'axios'
import { API_CONFIG, HEADERS } from '@/config/api.config'
import { ApiError } from './errors'

// Create axios instance for Command Center API
export const apiClient: AxiosInstance = axios.create({
  baseURL: API_CONFIG.baseURL,
  timeout: API_CONFIG.timeout,
  headers: HEADERS,
})

// Request retry configuration
const MAX_RETRIES = API_CONFIG.retries
const RETRY_DELAY = API_CONFIG.retryDelay

// Retry interceptor
apiClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    const config = error.config

    // Handle connection refused errors
    if (error.code === 'ERR_NETWORK' || error.code === 'ECONNREFUSED') {
      console.warn('API connection refused. Make sure the Command Center is running on port 3000.')
      // Don't retry connection refused errors
      return Promise.reject(new ApiError('Unable to connect to the API server. Please ensure the Command Center is running.', 'CONNECTION_REFUSED'))
    }

    // Retry logic for network errors
    if (!config || config.__retryCount >= MAX_RETRIES) {
      return Promise.reject(error)
    }

    config.__retryCount = config.__retryCount || 0
    config.__retryCount += 1

    // Only retry on network errors or 5xx errors
    if (error.code === 'NETWORK_ERROR' || error.response?.status >= 500) {
      await new Promise((resolve) => setTimeout(resolve, RETRY_DELAY * config.__retryCount))
      return apiClient(config)
    }

    return Promise.reject(error)
  }
)

// Add auth token interceptor
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token')
    if (token && !config.headers.skipAuth) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => Promise.reject(error)
)

// Add response error interceptor for auth handling
apiClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    // Handle auth errors
    if (error.response?.status === 401) {
      // Clear auth token and redirect to login
      localStorage.removeItem('auth_token')
      window.location.href = '/login'
    }

    // Convert to our ApiError format if needed
    if (!(error instanceof ApiError)) {
      return Promise.reject(ApiError.fromError(error))
    }

    return Promise.reject(error)
  }
)

// Helper functions for auth token management
export function setAuthToken(token: string | null) {
  if (token) {
    localStorage.setItem('auth_token', token)
  } else {
    localStorage.removeItem('auth_token')
  }
}

export function getAuthToken(): string | null {
  return localStorage.getItem('auth_token')
}

// Re-export types and the client for backward compatibility
export type ApiClient = AxiosInstance
export default apiClient

// Helper function to create API requests with proper error handling
export async function apiRequest<T = any>(
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH',
  url: string,
  data?: any,
  config?: AxiosRequestConfig
): Promise<T> {
  try {
    const response = await apiClient.request<T>({
      method,
      url,
      data,
      ...config,
    })
    return response.data
  } catch (error) {
    throw ApiError.fromError(error)
  }
}
