import { ENDPOINTS, FEATURES } from '@/config/api.config'
import { apiClient } from '../client'
import type { WsChatMessage } from '../types'

export interface ChatSocketOptions {
  onMessage?: (message: WsChatMessage) => void
  onConnect?: () => void
  onDisconnect?: () => void
  onError?: (error: Error) => void
  reconnectAttempts?: number
  reconnectDelay?: number
}

export class ChatSocket {
  private ws: WebSocket | null = null
  private options: Required<ChatSocketOptions>
  private reconnectCount = 0
  private reconnectTimeout: number | null = null
  private heartbeatInterval: number | null = null
  private messageQueue: WsChatMessage[] = []
  private isConnecting = false
  private shouldReconnect = true

  constructor(options: ChatSocketOptions = {}) {
    this.options = {
      onMessage: options.onMessage || (() => {}),
      onConnect: options.onConnect || (() => {}),
      onDisconnect: options.onDisconnect || (() => {}),
      onError: options.onError || (() => {}),
      reconnectAttempts: options.reconnectAttempts || 5,
      reconnectDelay: options.reconnectDelay || 3000,
    }
  }

  /**
   * Connect to the WebSocket server
   */
  connect(): void {
    if (!FEATURES.enableWebSocket) {
      console.warn('WebSocket is disabled in configuration')
      return
    }

    if (this.ws?.readyState === WebSocket.OPEN || this.isConnecting) {
      return
    }

    this.isConnecting = true
    this.shouldReconnect = true

    try {
      // Get auth token
      const token = apiClient.getAuthToken()
      const wsUrl = new URL(ENDPOINTS.ws.chat)

      if (token) {
        wsUrl.searchParams.set('token', token)
      }

      this.ws = new WebSocket(wsUrl.toString())

      this.ws.onopen = () => {
        // WebSocket connected
        this.isConnecting = false
        this.reconnectCount = 0
        this.options.onConnect()
        this.startHeartbeat()
        this.flushMessageQueue()
      }

      this.ws.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data) as WsChatMessage
          this.handleMessage(message)
        } catch (error) {
          console.error('Failed to parse WebSocket message:', error)
        }
      }

      this.ws.onerror = (event) => {
        console.error('WebSocket error:', event)
        this.options.onError(new Error('WebSocket connection error'))
      }

      this.ws.onclose = () => {
        // WebSocket disconnected
        this.isConnecting = false
        this.stopHeartbeat()
        this.options.onDisconnect()

        if (this.shouldReconnect) {
          this.attemptReconnect()
        }
      }
    } catch (error) {
      this.isConnecting = false
      console.error('Failed to connect to WebSocket:', error)
      this.options.onError(error as Error)

      if (this.shouldReconnect) {
        this.attemptReconnect()
      }
    }
  }

  /**
   * Disconnect from the WebSocket server
   */
  disconnect(): void {
    this.shouldReconnect = false

    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout)
      this.reconnectTimeout = null
    }

    this.stopHeartbeat()

    if (this.ws) {
      this.ws.close()
      this.ws = null
    }
  }

  /**
   * Send a message through the WebSocket
   */
  send(message: WsChatMessage): void {
    if (this.ws?.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message))
    } else {
      // Queue message if not connected
      this.messageQueue.push(message)

      // Attempt to connect if not already trying
      if (!this.isConnecting) {
        this.connect()
      }
    }
  }

  /**
   * Send a chat message
   */
  sendChatMessage(conversationId: string, content: string): void {
    this.send({
      type: 'message',
      payload: {
        conversationId,
        content,
      },
    })
  }

  /**
   * Send typing indicator
   */
  sendTyping(conversationId: string): void {
    this.send({
      type: 'typing',
      payload: {
        conversationId,
      },
    })
  }

  /**
   * Send stop signal
   */
  sendStop(conversationId: string): void {
    this.send({
      type: 'stop',
      payload: {
        conversationId,
      },
    })
  }

  /**
   * Get connection state
   */
  getState(): 'connecting' | 'connected' | 'disconnected' {
    if (this.isConnecting) {
      return 'connecting'
    }
    if (this.ws?.readyState === WebSocket.OPEN) {
      return 'connected'
    }
    return 'disconnected'
  }

  /**
   * Handle incoming messages
   */
  private handleMessage(message: WsChatMessage): void {
    switch (message.type) {
      case 'message':
      case 'typing':
      case 'stop':
      case 'error':
        this.options.onMessage(message)
        break

      default:
        console.warn('Unknown WebSocket message type:', message)
    }
  }

  /**
   * Attempt to reconnect
   */
  private attemptReconnect(): void {
    if (this.reconnectCount >= this.options.reconnectAttempts) {
      console.error('Max reconnection attempts reached')
      return
    }

    this.reconnectCount++
    const delay = this.options.reconnectDelay * 2 ** (this.reconnectCount - 1)

    // Attempting to reconnect with exponential backoff

    this.reconnectTimeout = window.setTimeout(() => {
      this.connect()
    }, delay)
  }

  /**
   * Start heartbeat to keep connection alive
   */
  private startHeartbeat(): void {
    this.stopHeartbeat()

    // Send ping every 30 seconds
    this.heartbeatInterval = window.setInterval(() => {
      if (this.ws?.readyState === WebSocket.OPEN) {
        this.ws.send(JSON.stringify({ type: 'ping' }))
      }
    }, 30000)
  }

  /**
   * Stop heartbeat
   */
  private stopHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
      this.heartbeatInterval = null
    }
  }

  /**
   * Flush queued messages
   */
  private flushMessageQueue(): void {
    while (this.messageQueue.length > 0 && this.ws?.readyState === WebSocket.OPEN) {
      const message = this.messageQueue.shift()
      if (message) {
        this.ws.send(JSON.stringify(message))
      }
    }
  }
}

// Export singleton instance with default options
export const chatSocket = new ChatSocket()
