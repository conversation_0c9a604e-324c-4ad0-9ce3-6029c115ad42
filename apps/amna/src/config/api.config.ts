import { env } from './env'

export const API_CONFIG = {
  baseURL: env.apiBaseUrl,
  timeout: 30000,
  retries: 3,
  retryDelay: 1000,
} as const

export const ENDPOINTS = {
  // AMNA endpoints (Command-Center)
  amna: {
    execute: '/api/amna/execute',
    config: '/api/amna/config',
    status: '/api/amna/status',
    health: '/api/amna/health',
    stats: '/api/amna/stats',
    agents: '/api/amna/agents',
    tasks: '/api/amna/tasks',
    workflows: '/api/amna/workflows',
    memory: '/api/amna/memory',
    tools: '/api/amna/tools',
    history: '/api/amna/history',
  },
  // AI/Chat endpoints (Command-Center)
  ai: {
    chat: '/api/ai/conversations',
    models: '/api/amna/llm/models',
    llmChat: '/api/amna/llm/chat',
    conversations: '/api/ai/conversations',
    messages: (id: string) => `/api/ai/conversations/${id}/messages`,
    createConversation: '/api/ai/conversations',
  },
  // Auth endpoints
  auth: {
    login: '/api/auth/login',
    register: '/api/auth/register',
    refresh: '/api/auth/refresh',
    logout: '/api/auth/logout',
    profile: '/api/auth/me',
  },
  // WebSocket endpoint
  ws: {
    amna: '/amna', // WebSocket namespace for AMNA
  },
} as const

export const HEADERS = {
  'Content-Type': 'application/json',
  'X-Client-Version': env.appVersion,
} as const

// Feature flags
export const FEATURES = {
  enableWebSocket: env.enableWebSocket,
  enableOfflineMode: env.enableOffline,
} as const

// API Error codes
export const API_ERROR_CODES = {
  NETWORK_ERROR: 'NETWORK_ERROR',
  TIMEOUT: 'TIMEOUT',
  UNAUTHORIZED: 'UNAUTHORIZED',
  FORBIDDEN: 'FORBIDDEN',
  NOT_FOUND: 'NOT_FOUND',
  RATE_LIMITED: 'RATE_LIMITED',
  SERVER_ERROR: 'SERVER_ERROR',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
} as const
