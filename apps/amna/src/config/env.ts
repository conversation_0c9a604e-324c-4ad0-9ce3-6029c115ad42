// Environment variable utilities with validation

export const env = {
  // API Configuration
  apiBaseUrl: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000',
  wsUrl: import.meta.env.VITE_WS_URL || 'ws://localhost:3000',

  // Feature Flags
  enableWebSocket: import.meta.env.VITE_ENABLE_WEBSOCKET !== 'false',
  enableOfflineMode: import.meta.env.VITE_ENABLE_OFFLINE === 'true',

  // Environment
  environment: import.meta.env.VITE_ENV || 'development',
  isDevelopment: import.meta.env.DEV,
  isProduction: import.meta.env.PROD,

  // External Services
  sentryDsn: import.meta.env.VITE_SENTRY_DSN || '',
  analyticsId: import.meta.env.VITE_ANALYTICS_ID || '',
  supportEmail: import.meta.env.VITE_SUPPORT_EMAIL || '<EMAIL>',

  // Build info
  appVersion: (globalThis as unknown as { __APP_VERSION__?: string }).__APP_VERSION__ || 'dev',
  buildTime:
    (globalThis as unknown as { __BUILD_TIME__?: string }).__BUILD_TIME__ ||
    new Date().toISOString(),
} as const

// Validate required environment variables
export function validateEnv(): void {
  const required: Array<keyof typeof env> = []

  // Add more required fields as needed based on environment
  if (env.isProduction) {
    // In production, we might require certain fields
    // required.push('sentryDsn');
  }

  const missing = required.filter((key) => !env[key])

  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`)
  }
}

// Log environment info (only in development)
export function logEnvInfo(): void {
  if (env.isDevelopment) {
    // Environment configuration logging disabled for production
    // To view configuration, use browser DevTools
  }
}

// Export type for environment
export type Environment = typeof env
