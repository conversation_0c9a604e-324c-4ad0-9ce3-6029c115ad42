// TODO: Replace with local authentication implementation
/*
import {
  AuthProvider,
  CrossAppNavigation,
  ProtectedRoute,
  UnifiedLoginPage,
} from '@luminar/shared-auth'
*/
import { LUMINAR_APPS } from '@luminar/shared-config'
import { Navigate, Route, BrowserRouter as Router, Routes } from 'react-router-dom'
// Import existing components
import ChatPage from './App' // Your existing chat functionality
import { AmnaChatInterface } from './components/amna/AmnaChatInterface'
import { TrainingDashboard } from './components/integration/TrainingDashboard'
import { authConfig } from './config/auth.config'

// Placeholder components for demonstration
const Dashboard = () => (
  <div className="p-8">
    <h1 className="text-2xl">AMNA Dashboard</h1>
  </div>
)
const AgentManagement = () => (
  <div className="p-8">
    <h1 className="text-2xl">Agent Management</h1>
  </div>
)
const WorkflowBuilder = () => (
  <div className="p-8">
    <h1 className="text-2xl">Workflow Builder</h1>
  </div>
)
const TaskMonitor = () => (
  <div className="p-8">
    <h1 className="text-2xl">Task Monitor</h1>
  </div>
)
const Settings = () => (
  <div className="p-8">
    <h1 className="text-2xl">Settings</h1>
  </div>
)
const UnauthorizedPage = () => (
  <div className="p-8 text-center">
    <h1 className="text-2xl text-red-600">Unauthorized Access</h1>
    <p className="mt-4">You don't have permission to access this page.</p>
  </div>
)

function App() {
  return (
    <Router>
      <AuthProvider config={authConfig}>
        <Routes>
          {/* Public routes */}
          <Route
            path="/login"
            element={
              <UnifiedLoginPage
                appName="AMNA"
                appDescription="AI-powered Assistant for Modern Needs & Applications"
                appLogo="/assets/amna-logo.svg"
                customStyles={{
                  primaryColor: '#3b82f6',
                }}
              />
            }
          />

          <Route path="/unauthorized" element={<UnauthorizedPage />} />

          {/* Protected routes */}
          <Route
            path="/*"
            element={
              <ProtectedRoute>
                <AppLayout />
              </ProtectedRoute>
            }
          />
        </Routes>
      </AuthProvider>
    </Router>
  )
}

function AppLayout() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Cross-app navigation */}
      <CrossAppNavigation currentApp="amna" apps={LUMINAR_APPS} position="top" theme="light" />

      {/* Main app content */}
      <div className="pt-12">
        <Routes>
          <Route path="/" element={<Navigate to="/dashboard" replace />} />
          <Route path="/dashboard" element={<Dashboard />} />

          {/* Original chat interface */}
          <Route path="/chat/*" element={<ChatPage />} />

          {/* New integrated chat interface */}
          <Route path="/amna-chat" element={<AmnaChatInterface />} />

          {/* Training integration */}
          <Route path="/training" element={<TrainingDashboard />} />

          {/* Admin routes with permission checks */}
          <Route
            path="/agents"
            element={
              <ProtectedRoute permissions={['agents:view']}>
                <AgentManagement />
              </ProtectedRoute>
            }
          />
          <Route
            path="/workflows"
            element={
              <ProtectedRoute permissions={['workflows:view']}>
                <WorkflowBuilder />
              </ProtectedRoute>
            }
          />
          <Route
            path="/tasks"
            element={
              <ProtectedRoute permissions={['tasks:view']}>
                <TaskMonitor />
              </ProtectedRoute>
            }
          />

          {/* Settings */}
          <Route path="/settings" element={<Settings />} />
        </Routes>
      </div>
    </div>
  )
}

export default App
