/// <reference types="vite/client" />

interface ImportMetaEnv {
  // API Configuration
  readonly VITE_API_BASE_URL: string
  readonly VITE_WS_URL: string

  // Feature Flags
  readonly VITE_ENABLE_WEBSOCKET: string
  readonly VITE_ENABLE_OFFLINE: string

  // Environment
  readonly VITE_ENV: 'development' | 'staging' | 'production'

  // Optional: External Services
  readonly VITE_SENTRY_DSN?: string
  readonly VITE_ANALYTICS_ID?: string
  readonly VITE_SUPPORT_EMAIL?: string
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}

// Global constants defined in vite.config.ts
declare const __APP_VERSION__: string
declare const __BUILD_TIME__: string
