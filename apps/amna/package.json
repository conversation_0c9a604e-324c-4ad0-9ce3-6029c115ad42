{"name": "@apps/amna", "private": true, "sideEffects": false, "type": "module", "exports": {".": {"import": "./dist/index.js", "types": "./dist/index.d.ts"}, "./package.json": "./package.json"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "scripts": {"dev": "vite dev", "build": "vite build", "build:lib": "tsc --emitDeclarationOnly --declaration --outDir dist && vite build --mode library", "build:analyze": "vite build --config vite.config.ts --mode analyze", "build:profile": "vite build --config vite.config.ts --mode profile", "bundle-analyzer": "vite-bundle-analyzer", "start": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0 --fix", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "type-check": "tsc --noEmit", "test": "vitest", "test:watch": "vitest --watch", "test:coverage": "vitest --coverage", "test:ui": "vitest --ui"}, "dependencies": {"@luminar/shared-ui": "workspace:*", "@luminar/shared-core": "workspace:*", "@luminar/shared-config": "workspace:*", "@sentry/react": "^9.41.0", "@tailwindcss/postcss": "^4.1.4", "@tanstack/router-devtools": "^1.125.6", "@tanstack/router-vite-plugin": "^1.125.6", "framer-motion": "^12.23.0", "idb": "^8.0.3", "vite": "^6.0.0"}, "devDependencies": {"@eslint/js": "^9.15.0", "@faker-js/faker": "^9.9.0", "@mswjs/data": "^0.16.2", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@testing-library/user-event": "^14.5.2", "@types/node": "^22.5.4", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@typescript-eslint/eslint-plugin": "^8.15.0", "@typescript-eslint/parser": "^8.15.0", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "eslint": "^9.15.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^15.12.0", "happy-dom": "^15.11.6", "msw": "^2.10.3", "postcss": "^8.5.3", "prettier": "^3.3.3", "rollup-plugin-visualizer": "^5.12.0", "tailwindcss": "^4.1.4", "typescript": "^5.7.2", "vite-bundle-analyzer": "^0.12.1", "vite-tsconfig-paths": "^5.1.4", "vitest": "^2.1.8"}, "msw": {"workerDirectory": ["public"]}}