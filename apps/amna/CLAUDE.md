# AMNA WebSocket Integration

## Current Status
- ✅ Implemented WebSocketClient with Socket.IO in shared-ui package
- ✅ Implemented IntegrationClient with proper API methods
- ✅ Fixed useAMNAState hook to include unreadCount property
- ✅ Added VITE_ENABLE_WEBSOCKET to environment configuration

## WebSocket Architecture
- AMNA connects to Command Center WebSocket at `ws://localhost:3000/amna`
- Uses Socket.IO for real-time communication
- IntegrationProvider manages WebSocket connection lifecycle

## Key Components

### WebSocketClient (`packages/shared-ui/src/services/integration/websocketClient.ts`)
- Singleton pattern with EventEmitter interface
- Automatic reconnection with exponential backoff
- Authentication token support
- Methods: connect(), disconnect(), send(), isConnected(), on(), off()

### IntegrationClient (`packages/shared-ui/src/services/integration/integrationClient.ts`)
- REST API client for integration management
- Methods: getAllStatuses(), getIntegrationStatus(), syncIntegration(), configureIntegration()
- Automatic auth token handling

### IntegrationProvider (`packages/shared-ui/src/components/integration/providers/IntegrationProvider.tsx`)
- React context for integration state
- Manages WebSocket connection
- Provides integration status and sync methods

## Environment Configuration
```env
VITE_API_URL=http://localhost:3000
VITE_WS_URL=ws://localhost:3000
VITE_ENABLE_WEBSOCKET=true
```

## Usage
The AMNA app automatically connects to the Command Center WebSocket when:
1. IntegrationProvider is mounted in the app
2. VITE_ENABLE_WEBSOCKET is set to true
3. Command Center is running on port 3000

## Testing
To test WebSocket integration:
1. Start Command Center: `cd apps/command-center && pnpm dev`
2. Start AMNA: `cd apps/amna && pnpm dev`
3. Check browser console for "WebSocket connected to integration service" message
4. The AMNAWidget should show connection status

## Troubleshooting
- If connection fails, ensure Command Center is running
- Check browser console for WebSocket errors
- Verify auth token is available in localStorage/sessionStorage
- Check CORS configuration allows AMNA origin

## CORS Configuration
AMNA sends the following custom headers:
- `X-Client-Version`: App version from environment

The Command Center's CORS configuration has been updated to allow this header.
If you encounter CORS errors, ensure:
1. `X-Client-Version` is in the allowed headers list in `/apps/command-center/src/config/cors.config.ts`
2. AMNA's origin (http://localhost:5001) is in the CORS_ORIGINS environment variable