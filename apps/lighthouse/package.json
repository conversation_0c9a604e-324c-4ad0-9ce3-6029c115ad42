{"name": "tanstack-start-example-basic", "private": true, "sideEffects": false, "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "start": "vite start", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "typecheck": "tsc --noEmit --skipL<PERSON><PERSON><PERSON><PERSON>"}, "dependencies": {"@luminar/shared-core": "workspace:^", "@luminar/shared-ui": "workspace:*", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@tailwindcss/postcss": "^4.1.4", "@tanstack/react-query": "^5.81.5", "@tanstack/react-query-devtools": "^5.83.0", "@tanstack/react-start": "^1.116.1", "@tanstack/start": "^1.116.1", "chart.js": "^4.5.0", "immer": "^10.1.1", "react-chartjs-2": "^5.3.0", "react-force-graph-2d": "^1.28.0"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@testing-library/user-event": "^14.5.2", "@types/node": "^22.5.4", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "happy-dom": "^15.11.6", "msw": "^2.10.3", "postcss": "^8.5.3", "tailwindcss": "^4.1.4", "typescript": "^5.7.2", "vite": "^6.0.0", "vite-tsconfig-paths": "^5.1.4", "vitest": "^2.1.8"}}