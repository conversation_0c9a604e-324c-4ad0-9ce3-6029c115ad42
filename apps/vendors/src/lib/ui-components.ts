/**
 * UI Components Import Aliases
 *
 * This file provides backward-compatible imports from @luminar/shared-ui
 * to maintain existing component usage patterns while migrating to centralized components.
 */

// Actions
export {
  Button,
  LuminarButton as AdvancedButton,
  LuminarCommand as Command,
  LuminarDropdown as Dropdown,
} from '@luminar/shared-ui/actions'
// Chart Components
export {
  LuminarAreaChart as AreaChart,
  LuminarBarChart as BarChart,
  LuminarDonutChart as DonutChart,
  LuminarLine<PERSON>hart as LineChart,
  LuminarPieChart as PieChart,
} from '@luminar/shared-ui/charts'
// Display Components
export {
  LuminarAvatar as Avatar,
  LuminarBadge as Badge,
  LuminarCard as Card,
  LuminarDataTable as DataTable,
  LuminarMetricCard as MetricCard,
  LuminarSkeleton as LoadingSkeleton,
  LuminarTable as Table,
  ProgressBar as Progress,
  Skeleton,
  // Re-export card sub-components if available in shared-ui
} from '@luminar/shared-ui/display'
// Form Components
export {
  LuminarCheckbox as Checkbox,
  LuminarDatePicker as DatePicker,
  LuminarFileUpload as FileUpload,
  LuminarForm as Form,
  LuminarInput as Input,
  LuminarLabel as Label,
  LuminarRadioGroup as RadioGroup,
  LuminarSelect as Select,
  LuminarSwitch as Switch,
  LuminarTextarea as Textarea,
} from '@luminar/shared-ui/forms'

// Feedback Components (if available)
// export { Dialog, DialogContent, DialogHeader, DialogTitle } from '@luminar/shared-ui/feedback'

// Layout Components (if available)
// export { Tabs, TabsContent, TabsList, TabsTrigger } from '@luminar/shared-ui/layout'

// Auth Components - TODO: Replace with local implementation
// export { ProtectedRoute } from '@luminar/shared-auth/components'

export type * from '@luminar/shared-ui/actions'
export type * from '@luminar/shared-ui/charts'
// Re-export types
export type * from '@luminar/shared-ui/display'
export type * from '@luminar/shared-ui/forms'
