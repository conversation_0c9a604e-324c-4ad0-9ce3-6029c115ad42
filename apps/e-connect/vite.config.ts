import path from 'node:path'
import { TanStackRouterVite } from '@tanstack/router-vite-plugin'
import react from '@vitejs/plugin-react'
import { visualizer } from 'rollup-plugin-visualizer'
import { defineConfig, splitVendorChunkPlugin } from 'vite'
import { checker } from 'vite-plugin-checker'

export default defineConfig(({ mode }) => {
  const analyze = mode === 'analyze' || mode === 'profile'

  const plugins = [
    react({
      babel: {
        plugins: [
          process.env?.NODE_ENV === 'development' && [
            '@babel/plugin-transform-react-jsx-development',
          ],
        ].filter(Boolean),
      },
    }),
    TanStackRouterVite({
      routesDirectory: './src/routes',
      generatedRouteTree: './src/routeTree.gen.ts',
      routeFileIgnorePrefix: '-',
      quoteStyle: 'single',
    }),
    checker({
      typescript: true,
    }),
    splitVendorChunkPlugin(),
  ]

  if (analyze) {
    plugins.push(
      visualizer({
        filename: 'dist/stats.html',
        open: true,
        gzipSize: true,
        brotliSize: true,
        template: 'treemap',
      })
    )
  }

  return {
    resolve: {
      alias: {
        '@': path.resolve(__dirname, 'src'),
        '@components': path.resolve(__dirname, 'src/components'),
        '@hooks': path.resolve(__dirname, 'src/hooks'),
        '@utils': path.resolve(__dirname, 'src/utils'),
        '@types': path.resolve(__dirname, 'src/types'),
        '@stores': path.resolve(__dirname, 'src/stores'),
        '@services': path.resolve(__dirname, 'src/services'),
        '@mocks': path.resolve(__dirname, 'src/mocks'),
        '@luminar/shared-ui': path.resolve(__dirname, '../../packages/shared-ui/src'),
        '@luminar/shared-core': path.resolve(__dirname, '../../packages/shared-core/src'),
      },
    },

    define: {
      __APP_NAME__: JSON.stringify('E-Connect'),
      __API_URL__: JSON.stringify(process.env.VITE_API_URL || 'http://localhost:3000'),
      __PERFORMANCE_MONITORING__: JSON.stringify(mode !== 'development'),
      __APP_VERSION__: JSON.stringify(process.env?.npm_package_version || '1.0.0'),
      __BUILD_TIME__: JSON.stringify(new Date().toISOString()),
      'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'development'),
      global: 'globalThis',
    },

    plugins,

    build: {
      outDir: 'dist',
      sourcemap: mode === 'development',
      target: 'esnext',
      minify: 'esbuild',
      cssTarget: 'chrome80',
      chunkSizeWarningLimit: 800, // 800KB

      rollupOptions: {
        output: {
          manualChunks: (id) => {
            if (id.includes('node_modules')) {
              if (id.includes('react') || id.includes('react-dom')) {
                return 'vendor-react'
              }
              if (id.includes('@tanstack/react-router') || id.includes('@tanstack/react-query')) {
                return 'vendor-router'
              }
              if (
                id.includes('@radix-ui') ||
                id.includes('lucide-react') ||
                id.includes('framer-motion')
              ) {
                return 'vendor-ui'
              }
              if (id.includes('recharts') || id.includes('d3')) {
                return 'vendor-charts'
              }
              return 'vendor-misc'
            }
          },
          chunkFileNames: 'js/[name]-[hash].js',
          entryFileNames: 'js/[name]-[hash].js',
          assetFileNames: (assetInfo) => {
            const info = assetInfo.name.split('.')
            const ext = info[info.length - 1]
            if (/png|jpe?g|svg|gif|tiff|bmp|ico/i.test(ext)) {
              return `images/[name]-[hash][extname]`
            }
            if (/css/i.test(ext)) {
              return `css/[name]-[hash][extname]`
            }
            return `assets/[name]-[hash][extname]`
          },
        },
      },

      terserOptions: {
        compress: {
          drop_console: true,
          drop_debugger: true,
        },
      },
    },

    optimizeDeps: {
      include: [
        'react',
        'react-dom',
        '@tanstack/react-router',
        '@tanstack/react-query',
        'axios',
        'clsx',
        'tailwind-merge',
        'lucide-react',
        'framer-motion',
        'recharts',
        'date-fns',
        'lodash',
        'immer',
        'jotai',
      ],
      entries: ['src/main.tsx', 'src/**/*.tsx', 'src/**/*.ts'],
      esbuildOptions: {
        define: {
          global: 'globalThis',
        },
      },
    },

    server: {
      port: 5002,
      strictPort: true,
      host: true,
      open: true,
      cors: true,
      proxy: {
        '/api': {
          target: 'http://localhost:3000',
          changeOrigin: true,
          secure: false,
        },
      },
      hmr: {
        overlay: true,
      },
    },

    preview: {
      port: 5002,
      strictPort: true,
      host: true,
      open: false,
    },

    css: {
      modules: {
        localsConvention: 'camelCaseOnly',
      },
    },

    envPrefix: ['VITE_', 'LUMINAR_'],

    worker: {
      format: 'es',
    },
  }
})
