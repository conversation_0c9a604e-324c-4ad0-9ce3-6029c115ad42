import { useNavigate } from '@tanstack/react-router'
import type React from 'react'
import { createContext, useContext, useEffect, useState } from 'react'

// TODO: Replace with local authentication implementation
interface User {
  id: string
  email: string
  name: string
}

// TODO: Replace with local auth client
const authClient = {
  getCurrentUser: () => Promise.resolve(null),
  login: () => Promise.resolve(),
  logout: () => Promise.resolve(),
  onAuthStateChange: () => () => {},
}

interface AuthContextType {
  user: User | null
  isLoading: boolean
  isAuthenticated: boolean
  login: (email: string, password: string) => Promise<void>
  logout: () => Promise<void>
  refreshSession: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const navigate = useNavigate()

  useEffect(() => {
    // Check if user is already authenticated
    const checkAuth = async () => {
      try {
        const currentUser = await authClient.getCurrentUser()
        setUser(currentUser)
      } catch {
        // User is not authenticated
        console.log('User not authenticated')
      } finally {
        setIsLoading(false)
      }
    }

    checkAuth()
  }, [])

  const login = async (email: string, password: string) => {
    try {
      const response = await authClient.login({ email, password })
      setUser(response.user)
      navigate({ to: '/' })
    } catch (error) {
      console.error('Login failed:', error)
      throw error
    }
  }

  const logout = async () => {
    try {
      await authClient.logout()
      setUser(null)
      navigate({ to: '/login' })
    } catch (error) {
      console.error('Logout failed:', error)
    }
  }

  const refreshSession = async () => {
    try {
      const response = await authClient.refreshToken()
      if (response.user) {
        setUser(response.user)
      }
    } catch (error) {
      console.error('Session refresh failed:', error)
      setUser(null)
    }
  }

  return (
    <AuthContext.Provider
      value={{
        user,
        isLoading,
        isAuthenticated: !!user,
        login,
        logout,
        refreshSession,
      }}
    >
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
