# Luminar Full Stack Management Makefile
# Provides convenient commands for managing the complete platform

.PHONY: help check build start stop restart clean logs status health validate

# Default target
help: ## Show this help message
	@echo "Luminar Full Stack Management Commands:"
	@echo ""
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}'

# Configuration
COMPOSE_FILE := docker-compose.yml
VALIDATION_SCRIPT := scripts/validate-full-stack.sh
PROJECT_ROOT := $(shell pwd)

# Prerequisites and validation
check: ## Check system prerequisites and configuration
	@echo "🔍 Checking prerequisites..."
	@$(VALIDATION_SCRIPT) check

validate: ## Run complete validation and health checks
	@echo "🔬 Running full validation..."
	@$(VALIDATION_SCRIPT)

# Build commands
build: ## Build all Docker images
	@echo "🔨 Building Docker images..."
	@docker-compose -f $(COMPOSE_FILE) build --no-cache

build-fast: ## Build Docker images (with cache)
	@echo "⚡ Building Docker images (cached)..."
	@docker-compose -f $(COMPOSE_FILE) build

# Deployment commands
start: ## Start all services
	@echo "🚀 Starting all services..."
	@$(VALIDATION_SCRIPT) start

start-infra: ## Start only infrastructure services
	@echo "🏗️ Starting infrastructure services..."
	@docker-compose -f $(COMPOSE_FILE) up -d postgres redis elasticsearch rabbitmq qdrant ollama minio

start-apps: ## Start application services (requires infrastructure)
	@echo "💻 Starting application services..."
	@docker-compose -f $(COMPOSE_FILE) up -d command-center amna-frontend e-connect-frontend lighthouse-frontend luminar-dashboard training-need-analysis vendors-frontend wins-of-week

start-monitoring: ## Start monitoring services
	@echo "📊 Starting monitoring services..."
	@docker-compose -f $(COMPOSE_FILE) up -d prometheus grafana jaeger

start-proxy: ## Start reverse proxy
	@echo "🌐 Starting reverse proxy..."
	@docker-compose -f $(COMPOSE_FILE) up -d nginx

start-dev: ## Start development environment (with dev tools)
	@echo "🛠️ Starting development environment..."
	@docker-compose -f $(COMPOSE_FILE) --profile dev-tools up -d

# Management commands
stop: ## Stop all services
	@echo "🛑 Stopping all services..."
	@docker-compose -f $(COMPOSE_FILE) down

stop-apps: ## Stop only application services
	@echo "🛑 Stopping application services..."
	@docker-compose -f $(COMPOSE_FILE) stop command-center amna-frontend e-connect-frontend lighthouse-frontend luminar-dashboard training-need-analysis vendors-frontend wins-of-week nginx

restart: ## Restart all services
	@echo "🔄 Restarting all services..."
	@make stop
	@make start

restart-app: ## Restart application services only
	@echo "🔄 Restarting application services..."
	@docker-compose -f $(COMPOSE_FILE) restart command-center amna-frontend e-connect-frontend lighthouse-frontend luminar-dashboard training-need-analysis vendors-frontend wins-of-week

# Monitoring and debugging
status: ## Show service status
	@echo "📊 Service Status:"
	@docker-compose -f $(COMPOSE_FILE) ps --format "table {{.Name}}\t{{.Status}}\t{{.Ports}}"

health: ## Run health checks
	@echo "🏥 Running health checks..."
	@$(VALIDATION_SCRIPT) health

logs: ## Show logs for all services
	@docker-compose -f $(COMPOSE_FILE) logs -f

logs-app: ## Show logs for Command Center
	@docker-compose -f $(COMPOSE_FILE) logs -f command-center

logs-db: ## Show database logs
	@docker-compose -f $(COMPOSE_FILE) logs -f postgres

logs-redis: ## Show Redis logs
	@docker-compose -f $(COMPOSE_FILE) logs -f redis

logs-nginx: ## Show Nginx logs
	@docker-compose -f $(COMPOSE_FILE) logs -f nginx

# Scaling commands
scale-api: ## Scale API services (usage: make scale-api REPLICAS=3)
	@docker-compose -f $(COMPOSE_FILE) up -d --scale command-center=${REPLICAS:-2}

scale-frontend: ## Scale frontend services (usage: make scale-frontend REPLICAS=2)
	@docker-compose -f $(COMPOSE_FILE) up -d --scale amna-frontend=${REPLICAS:-2} --scale e-connect-frontend=${REPLICAS:-2}

# Database operations
db-connect: ## Connect to PostgreSQL database
	@docker-compose -f $(COMPOSE_FILE) exec postgres psql -U postgres

db-backup: ## Backup database
	@echo "💾 Creating database backup..."
	@mkdir -p backups
	@docker-compose -f $(COMPOSE_FILE) exec -T postgres pg_dumpall -U postgres > backups/luminar-backup-$(shell date +%Y%m%d-%H%M%S).sql
	@echo "✅ Backup completed: backups/luminar-backup-$(shell date +%Y%m%d-%H%M%S).sql"

db-restore: ## Restore database (usage: make db-restore BACKUP=backup-file.sql)
	@echo "📥 Restoring database from $(BACKUP)..."
	@docker-compose -f $(COMPOSE_FILE) exec -T postgres psql -U postgres < $(BACKUP)
	@echo "✅ Database restored"

# Cache operations
cache-connect: ## Connect to Redis
	@docker-compose -f $(COMPOSE_FILE) exec redis redis-cli

cache-flush: ## Flush Redis cache
	@echo "🧹 Flushing Redis cache..."
	@docker-compose -f $(COMPOSE_FILE) exec redis redis-cli FLUSHALL
	@echo "✅ Cache flushed"

cache-info: ## Show Redis info
	@docker-compose -f $(COMPOSE_FILE) exec redis redis-cli INFO

# Development commands
dev-install: ## Install development dependencies
	@echo "📦 Installing development dependencies..."
	@cd Command-Center && npm install
	@cd AMNA && npm install
	@cd e-connect && npm install
	@cd Lighthouse && npm install
	@cd Luminar-Dashboard && npm install
	@cd Training-need-analysis && npm install
	@cd Vendors && npm install
	@cd Wins-of-Week && npm install

dev-build: ## Build for development
	@echo "🔨 Building for development..."
	@make build-fast

dev-start: ## Start development environment
	@echo "🚀 Starting development environment..."
	@make start-infra
	@sleep 30
	@make start-apps
	@make start-monitoring
	@make start-proxy

# Testing commands
test: ## Run tests
	@echo "🧪 Running tests..."
	@docker-compose -f $(COMPOSE_FILE) exec command-center npm test

test-integration: ## Run integration tests
	@echo "🔗 Running integration tests..."
	@docker-compose -f $(COMPOSE_FILE) exec command-center npm run test:integration

test-e2e: ## Run end-to-end tests
	@echo "🎭 Running E2E tests..."
	@docker-compose -f $(COMPOSE_FILE) exec command-center npm run test:e2e

# Maintenance commands
clean: ## Stop services and remove containers, networks, and volumes
	@echo "🧹 Cleaning up..."
	@docker-compose -f $(COMPOSE_FILE) down -v --remove-orphans
	@docker system prune -f

clean-images: ## Remove all Luminar Docker images
	@echo "🗑️ Removing Docker images..."
	@docker images | grep luminar | awk '{print $$3}' | xargs -r docker rmi -f

clean-all: ## Complete cleanup (containers, images, volumes, networks)
	@echo "🧹 Complete cleanup..."
	@make clean
	@make clean-images
	@docker volume prune -f
	@docker network prune -f

# Security commands
security-scan: ## Run security scan on containers
	@echo "🛡️ Running security scan..."
	@docker run --rm -v /var/run/docker.sock:/var/run/docker.sock -v $(shell pwd):/project aquasec/trivy fs /project

# Backup and restore
backup-all: ## Backup all data
	@echo "💾 Creating full backup..."
	@mkdir -p backups/$(shell date +%Y%m%d-%H%M%S)
	@make db-backup
	@docker run --rm -v luminar_redis-data:/data -v $(shell pwd)/backups:/backup alpine tar czf /backup/redis-backup-$(shell date +%Y%m%d-%H%M%S).tar.gz /data
	@echo "✅ Full backup completed"

restore-all: ## Restore all data (usage: make restore-all DATE=20240101-120000)
	@echo "📥 Restoring full backup from $(DATE)..."
	@make db-restore BACKUP=backups/luminar-backup-$(DATE).sql
	@docker run --rm -v luminar_redis-data:/data -v $(shell pwd)/backups:/backup alpine tar xzf /backup/redis-backup-$(DATE).tar.gz -C /
	@echo "✅ Full restore completed"

# Documentation
docs: ## Generate documentation
	@echo "📚 Generating documentation..."
	@docker-compose -f $(COMPOSE_FILE) exec command-center npm run docs:generate

# Quick commands for common workflows
quick-start: check build start health ## Quick start: check, build, start, and validate
	@echo "✅ Quick start completed successfully!"

quick-restart: stop start health ## Quick restart: stop, start, and validate
	@echo "✅ Quick restart completed successfully!"

quick-update: stop build start health ## Quick update: stop, build, start, and validate
	@echo "✅ Quick update completed successfully!"

# Script-based commands
start-all: ## Start all services using new script
	@$(PROJECT_ROOT)/scripts/start-all.sh

dev: ## Start development environment
	@$(PROJECT_ROOT)/scripts/dev-start.sh

dev-frontend: ## Start only frontend applications
	@$(PROJECT_ROOT)/scripts/dev-frontend.sh

dev-backend: ## Start only backend services
	@$(PROJECT_ROOT)/scripts/dev-backend.sh

dev-infra: ## Start only infrastructure
	@$(PROJECT_ROOT)/scripts/dev-infra.sh

stop-all: ## Stop all services using new script
	@$(PROJECT_ROOT)/scripts/stop-all.sh

restart: ## Restart specific service (usage: make restart SERVICE=postgres)
	@$(PROJECT_ROOT)/scripts/restart-services.sh $(SERVICE)

logs: ## View logs for specific service (usage: make logs SERVICE=command-center)
	@$(PROJECT_ROOT)/scripts/logs.sh $(SERVICE)

status: ## Show status of all services
	@$(PROJECT_ROOT)/scripts/status.sh

health: ## Run comprehensive health checks
	@$(PROJECT_ROOT)/scripts/health-check.sh

setup-env: ## Set up environment configuration
	@$(PROJECT_ROOT)/scripts/setup-env.sh

# Environment management
env-copy: ## Copy example environment file
	@cp .env.full-stack.example .env
	@echo "✅ Environment file copied. Please edit .env with your settings."

env-validate: ## Validate environment configuration
	@echo "🔍 Validating environment configuration..."
	@docker-compose -f $(COMPOSE_FILE) config > /dev/null && echo "✅ Environment configuration is valid"

# URL display
urls: ## Display all service URLs
	@echo "🌐 Service URLs:"
	@echo "Main Portal:     http://localhost"
	@echo "API Gateway:     http://localhost:3000"
	@echo "AMNA:            http://localhost:3001"
	@echo "E-Connect:       http://localhost:3002"
	@echo "Lighthouse:      http://localhost:3003"
	@echo "Dashboard:       http://localhost:3004"
	@echo "Training:        http://localhost:3005"
	@echo "Vendors:         http://localhost:3006"
	@echo "Wins:            http://localhost:3007"
	@echo "Grafana:         http://localhost:3100"
	@echo "Jaeger:          http://localhost:16686"
	@echo "RabbitMQ:        http://localhost:15672"

# System information
info: ## Show system information
	@echo "💻 System Information:"
	@echo "Docker version: $(shell docker --version)"
	@echo "Docker Compose version: $(shell docker-compose --version)"
	@echo "Available memory: $(shell free -h | awk 'NR==2{print $$7}')"
	@echo "Available disk space: $(shell df -h . | awk 'NR==2{print $$4}')"
	@echo "Running containers: $(shell docker ps -q | wc -l)"