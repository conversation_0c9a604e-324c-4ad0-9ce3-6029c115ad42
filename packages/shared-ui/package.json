{"name": "@luminar/shared-ui", "private": true, "sideEffects": false, "type": "module", "main": "./src/index.ts", "module": "./src/index.ts", "types": "./src/index.ts", "exports": {".": {"types": "./src/index.ts", "import": "./src/index.ts", "require": "./src/index.ts"}, "./actions": {"types": "./src/actions.ts", "import": "./src/actions.ts", "require": "./src/actions.ts"}, "./display": {"types": "./src/display.ts", "import": "./src/display.ts", "require": "./src/display.ts"}, "./forms": {"types": "./src/forms.ts", "import": "./src/forms.ts", "require": "./src/forms.ts"}, "./charts": {"types": "./src/charts.ts", "import": "./src/charts.ts", "require": "./src/charts.ts"}, "./feedback": {"types": "./src/feedback.ts", "import": "./src/feedback.ts", "require": "./src/feedback.ts"}, "./utils": {"types": "./src/lib/utils.ts", "import": "./src/lib/utils.ts", "require": "./src/lib/utils.ts"}}, "bin": {"luminar": "./cli/index.js"}, "scripts": {"dev": "vite dev --config vite.config.dev.ts", "dev:watch": "vite dev --config vite.config.dev.ts --watch", "build": "vite build", "build:analyze": "vite build && open dist/stats.html", "start": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:watch": "vitest --watch", "test:coverage": "vitest --coverage", "test:run": "vitest run", "lint": "eslint . --ext ts,tsx,js,jsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx,js,jsx --fix", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,css,md}\"", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,json,css,md}\"", "typecheck": "tsc --noEmit", "prepare": "husky install", "quality:check": "node scripts/quality-check.js", "quality:check:simple": "npm run lint && npm run typecheck && npm run test:run && npm run build", "storybook": "NODE_ENV=development STORYBOOK=true storybook dev -p 6006", "build-storybook": "storybook build", "migrate:props": "tsx src/scripts/migrate-to-standardized-props.ts", "playground": "vite dev --open /playground --config vite.config.dev.ts", "showcase": "vite dev --open /showcase --config vite.config.dev.ts", "performance": "vite dev --open /performance --config vite.config.dev.ts", "docs": "vite dev --open /documentation --config vite.config.dev.ts"}, "dependencies": {"@ai-sdk/openai": "^1.3.22", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@hookform/resolvers": "^5.1.1", "@luminar/shared-core": "workspace:^", "@mui/icons-material": "^7.2.0", "@mui/material": "^7.2.0", "@radix-ui/react-slot": "^1.2.0", "@react-three/fiber": "^9.2.0", "@tailwindcss/postcss": "^4.1.4", "@tanstack/react-query": "^5.83.0", "@tanstack/react-router": "^1.116.0", "@tanstack/react-router-devtools": "^1.116.0", "@tanstack/react-start": "^1.116.1", "@tanstack/router-generator": "^1.124.0", "@tanstack/start": "^1.116.1", "@types/three": "^0.178.0", "ai": "^4.3.16", "axios": "^1.10.0", "chalk": "^5.3.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "commander": "^12.0.0", "framer-motion": "^12.23.0", "fs-extra": "^11.2.0", "glob": "^10.3.10", "immer": "^10.0.3", "lucide-react": "^0.488.0", "ogl": "^0.0.79", "ora": "^8.0.1", "prettier": "^3.2.5", "prompts": "^2.4.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.60.0", "semver": "^7.5.4", "socket.io-client": "^4.8.1", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "three": "^0.178.0", "vite": "^6.0.0", "zustand": "^4.4.7"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@eslint/js": "^9.30.1", "@storybook/addon-a11y": "^9.0.16", "@storybook/addon-links": "^9.0.17", "@storybook/addon-onboarding": "^9.0.17", "@storybook/react-vite": "^9.0.15", "@tanstack/react-query-devtools": "^5.83.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest-axe": "^3.5.9", "@types/lodash": "^4.17.20", "@types/node": "^22.5.4", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@types/socket.io-client": "^3.0.0", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "@vitejs/plugin-react": "^4.6.0", "@webgpu/types": "^0.1.64", "autoprefixer": "^10.4.20", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "husky": "^9.1.7", "jest-axe": "^10.0.0", "jsdom": "^26.1.0", "lint-staged": "^16.1.2", "lodash": "^4.17.21", "msw": "^2.10.4", "postcss": "^8.5.3", "rollup-plugin-visualizer": "^6.0.3", "storybook": "^9.0.15", "tailwindcss": "^4.1.4", "typescript": "^5.7.2", "vite-plugin-pwa": "^1.0.1", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.2.4", "workbox-window": "^7.3.0", "zod": "^3.25.71"}}