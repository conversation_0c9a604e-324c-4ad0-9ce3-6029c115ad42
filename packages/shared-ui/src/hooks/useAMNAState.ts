import { useCallback, useEffect, useRef, useState } from 'react'

export type AMNAState = 'dormant' | 'processing' | 'communicating' | 'voice'

export interface AMNAStateConfig {
  initialState?: AMNAState
  autoTransition?: boolean
  transitionDelay?: number
  onStateChange?: (state: AMNAState) => void
}

export interface AMNAStateReturn {
  currentState: AMNAState
  isTransitioning: boolean
  setState: (state: AMNAState) => void
  triggerProcessing: () => Promise<void>
  triggerCommunication: (duration?: number) => Promise<void>
  triggerVoice: (duration?: number) => Promise<void>
  reset: () => void
  unreadCount: number
  setUnreadCount: (count: number) => void
  incrementUnreadCount: () => void
  resetUnreadCount: () => void
}

/**
 * Custom hook for managing AMNA state transitions
 * Provides programmatic control over AMNA states with smooth transitions
 */
export function useAMNAState({
  initialState = 'dormant',
  autoTransition = false,
  transitionDelay = 300,
  onStateChange,
}: AMNAStateConfig = {}): AMNAStateReturn {
  const [currentState, setCurrentState] = useState<AMNAState>(initialState)
  const [isTransitioning, setIsTransitioning] = useState(false)
  const [unreadCount, setUnreadCountState] = useState(0)
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)
  const autoTransitionRef = useRef<NodeJS.Timeout | null>(null)

  // Clear timeouts on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) clearTimeout(timeoutRef.current)
      if (autoTransitionRef.current) clearTimeout(autoTransitionRef.current)
    }
  }, [])

  // Handle state changes with transition management
  const setState = useCallback(
    (newState: AMNAState) => {
      if (currentState === newState || isTransitioning) return

      setIsTransitioning(true)
      setCurrentState(newState)
      onStateChange?.(newState)

      // Clear existing timeout
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }

      // Set transition complete after delay
      timeoutRef.current = setTimeout(() => {
        setIsTransitioning(false)
      }, transitionDelay)
    },
    [currentState, isTransitioning, transitionDelay, onStateChange]
  )

  // Trigger processing state with automatic return to dormant
  const triggerProcessing = useCallback(async (): Promise<void> => {
    return new Promise((resolve) => {
      setState('processing')

      const timeout = setTimeout(() => {
        setState('dormant')
        resolve()
      }, 2000)

      // Store timeout for cleanup
      timeoutRef.current = timeout
    })
  }, [setState])

  // Trigger communication state with optional duration
  const triggerCommunication = useCallback(
    async (duration = 1500): Promise<void> => {
      return new Promise((resolve) => {
        setState('communicating')

        const timeout = setTimeout(() => {
          setState('dormant')
          resolve()
        }, duration)

        timeoutRef.current = timeout
      })
    },
    [setState]
  )

  // Trigger voice state with optional duration
  const triggerVoice = useCallback(
    async (duration = 2000): Promise<void> => {
      return new Promise((resolve) => {
        setState('voice')

        const timeout = setTimeout(() => {
          setState('dormant')
          resolve()
        }, duration)

        timeoutRef.current = timeout
      })
    },
    [setState]
  )

  // Reset to initial state
  const reset = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }
    if (autoTransitionRef.current) {
      clearTimeout(autoTransitionRef.current)
    }
    setIsTransitioning(false)
    setCurrentState(initialState)
  }, [initialState])

  // Unread count management
  const setUnreadCount = useCallback((count: number) => {
    setUnreadCountState(Math.max(0, count))
  }, [])

  const incrementUnreadCount = useCallback(() => {
    setUnreadCountState((prev) => prev + 1)
  }, [])

  const resetUnreadCount = useCallback(() => {
    setUnreadCountState(0)
  }, [])

  // Auto transition logic
  useEffect(() => {
    if (!autoTransition) return

    const runAutoTransition = async () => {
      if (currentState !== 'dormant') return

      // Wait for dormant period
      await new Promise((resolve) => {
        autoTransitionRef.current = setTimeout(resolve, 3000)
      })

      // Processing phase
      setState('processing')
      await new Promise((resolve) => {
        autoTransitionRef.current = setTimeout(resolve, 2000)
      })

      // Communication phase
      setState('communicating')
      await new Promise((resolve) => {
        autoTransitionRef.current = setTimeout(resolve, 1500)
      })

      // Voice phase
      setState('voice')
      await new Promise((resolve) => {
        autoTransitionRef.current = setTimeout(resolve, 2000)
      })

      // Return to dormant
      setState('dormant')
    }

    if (currentState === 'dormant' && !isTransitioning) {
      const timeout = setTimeout(runAutoTransition, 1000)
      autoTransitionRef.current = timeout
    }

    return () => {
      if (autoTransitionRef.current) {
        clearTimeout(autoTransitionRef.current)
      }
    }
  }, [autoTransition, currentState, isTransitioning, setState])

  return {
    currentState,
    isTransitioning,
    setState,
    triggerProcessing,
    triggerCommunication,
    triggerVoice,
    reset,
    unreadCount,
    setUnreadCount,
    incrementUnreadCount,
    resetUnreadCount,
  }
}

// Utility hook for chat integration
export interface ChatAMNAConfig {
  onUserMessage?: () => void
  onAIResponse?: () => void
  onVoiceInput?: () => void
  processingDuration?: number
  communicationDuration?: number
  voiceDuration?: number
}

/**
 * Specialized hook for AMNA chat interface integration
 * Automatically manages AMNA states based on chat events
 */
export function useChatAMNA({
  onUserMessage,
  onAIResponse,
  onVoiceInput,
  processingDuration = 1500,
  communicationDuration = 2000,
  voiceDuration = 3000,
}: ChatAMNAConfig = {}) {
  const amna = useAMNAState()

  // Handle user message (trigger processing)
  const handleUserMessage = useCallback(async () => {
    onUserMessage?.()
    await amna.triggerProcessing()
  }, [amna, onUserMessage])

  // Handle AI response (trigger communication)
  const handleAIResponse = useCallback(async () => {
    onAIResponse?.()
    await amna.triggerCommunication(communicationDuration)
  }, [amna, onAIResponse, communicationDuration])

  // Handle voice input (trigger voice state)
  const handleVoiceInput = useCallback(async () => {
    onVoiceInput?.()
    await amna.triggerVoice(voiceDuration)
  }, [amna, onVoiceInput, voiceDuration])

  // Simulate full chat interaction
  const simulateChatInteraction = useCallback(async () => {
    await handleUserMessage()
    await new Promise((resolve) => setTimeout(resolve, 500))
    await handleAIResponse()
  }, [handleUserMessage, handleAIResponse])

  return {
    ...amna,
    handleUserMessage,
    handleAIResponse,
    handleVoiceInput,
    simulateChatInteraction,
  }
}

export default useAMNAState
