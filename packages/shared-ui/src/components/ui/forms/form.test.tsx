import { zod<PERSON><PERSON>olver } from '@hookform/resolvers/zod'
import { render, screen, waitFor } from '@testing-library/react'
import { useForm } from 'react-hook-form'
import { describe, expect, it, vi } from 'vitest'
import { z } from 'zod'
import { Button } from '../actions/button'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from './form'
import { Input } from './input'

// Test schema
const testSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Invalid email address'),
  age: z.number().min(18, 'Must be at least 18 years old'),
  bio: z.string().optional(),
  terms: z.boolean().refine((val) => val === true, 'You must accept the terms'),
})

type TestFormData = z.infer<typeof testSchema>

// Test form component
const TestForm = ({
  onSubmit,
  defaultValues,
}: {
  onSubmit: (data: TestFormData) => void
  defaultValues?: Partial<TestFormData>
}) => {
  const form = useForm<TestFormData>({
    resolver: zodResolver(testSchema),
    defaultValues: {
      name: '',
      email: '',
      age: 0,
      bio: '',
      terms: false,
      ...defaultValues,
    },
  })

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} noValidate>
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Name</FormLabel>
              <FormControl>
                <Input {...field} placeholder="Enter your name" />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email</FormLabel>
              <FormControl>
                <Input {...field} type="email" placeholder="Enter your email" />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="age"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Age</FormLabel>
              <FormControl>
                <Input
                  {...field}
                  type="number"
                  placeholder="Enter your age"
                  onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="terms"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <label>
                  <input type="checkbox" checked={field.value} onChange={field.onChange} />I accept
                  the terms and conditions
                </label>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button type="submit">Submit</Button>
      </form>
    </Form>
  )
}

describe('Form Component', () => {
  describe('Rendering', () => {
    it('renders all form fields correctly', () => {
      render(<TestForm onSubmit={vi.fn()} />)

      expect(screen.getByLabelText('Name')).toBeInTheDocument()
      expect(screen.getByLabelText('Email')).toBeInTheDocument()
      expect(screen.getByLabelText('Age')).toBeInTheDocument()
      expect(screen.getByText('I accept the terms and conditions')).toBeInTheDocument()
      expect(screen.getByRole('button', { name: 'Submit' })).toBeInTheDocument()
    })

    it('renders with default values', () => {
      const defaultValues: Partial<TestFormData> = {
        name: 'John Doe',
        email: '<EMAIL>',
        age: 25,
      }

      render(<TestForm onSubmit={vi.fn()} defaultValues={defaultValues} />)

      expect(screen.getByDisplayValue('John Doe')).toBeInTheDocument()
      expect(screen.getByDisplayValue('<EMAIL>')).toBeInTheDocument()
      expect(screen.getByDisplayValue('25')).toBeInTheDocument()
    })
  })

  describe('Validation', () => {
    it('shows validation errors on submit with empty fields', async () => {
      const handleSubmit = vi.fn()
      const { user } = render(<TestForm onSubmit={handleSubmit} />)

      await user.click(screen.getByRole('button', { name: 'Submit' }))

      await waitFor(() => {
        expect(screen.getByText('Name must be at least 2 characters')).toBeInTheDocument()
        expect(screen.getByText('Invalid email address')).toBeInTheDocument()
        expect(screen.getByText('Must be at least 18 years old')).toBeInTheDocument()
        expect(screen.getByText('You must accept the terms')).toBeInTheDocument()
      })

      expect(handleSubmit).not.toHaveBeenCalled()
    })

    it('shows validation errors on blur', async () => {
      const { user } = render(<TestForm onSubmit={vi.fn()} />)

      const nameInput = screen.getByLabelText('Name')
      const emailInput = screen.getByLabelText('Email')

      // Focus and blur name with invalid value
      await user.click(nameInput)
      await user.type(nameInput, 'J')
      await user.click(emailInput) // Blur by clicking another field

      await waitFor(() => {
        expect(screen.getByText('Name must be at least 2 characters')).toBeInTheDocument()
      })

      // Type invalid email
      await user.type(emailInput, 'invalid-email')
      await user.click(nameInput) // Blur

      await waitFor(() => {
        expect(screen.getByText('Invalid email address')).toBeInTheDocument()
      })
    })

    it('clears validation errors when corrected', async () => {
      const { user } = render(<TestForm onSubmit={vi.fn()} />)

      // Submit to trigger validation
      await user.click(screen.getByRole('button', { name: 'Submit' }))

      await waitFor(() => {
        expect(screen.getByText('Name must be at least 2 characters')).toBeInTheDocument()
      })

      // Fix the name field
      const nameInput = screen.getByLabelText('Name')
      await user.clear(nameInput)
      await user.type(nameInput, 'John Doe')

      await waitFor(() => {
        expect(screen.queryByText('Name must be at least 2 characters')).not.toBeInTheDocument()
      })
    })
  })

  describe('Form Submission', () => {
    it('submits form with valid data', async () => {
      const handleSubmit = vi.fn()
      const { user } = render(<TestForm onSubmit={handleSubmit} />)

      // Fill out form
      await user.type(screen.getByLabelText('Name'), 'John Doe')
      await user.type(screen.getByLabelText('Email'), '<EMAIL>')
      await user.type(screen.getByLabelText('Age'), '25')
      await user.click(screen.getByRole('checkbox'))

      // Submit form
      await user.click(screen.getByRole('button', { name: 'Submit' }))

      await waitFor(() => {
        expect(handleSubmit).toHaveBeenCalledWith({
          name: 'John Doe',
          email: '<EMAIL>',
          age: 25,
          bio: '',
          terms: true,
        })
      })
    })

    it('prevents submission while form is submitting', async () => {
      const handleSubmit = vi
        .fn()
        .mockImplementation(() => new Promise((resolve) => setTimeout(resolve, 1000)))

      const { user } = render(<TestForm onSubmit={handleSubmit} />)

      // Fill out form
      await user.type(screen.getByLabelText('Name'), 'John Doe')
      await user.type(screen.getByLabelText('Email'), '<EMAIL>')
      await user.type(screen.getByLabelText('Age'), '25')
      await user.click(screen.getByRole('checkbox'))

      // Submit form
      const submitButton = screen.getByRole('button', { name: 'Submit' })
      await user.click(submitButton)

      // Button should be disabled during submission
      expect(submitButton).toBeDisabled()

      // Try to submit again
      await user.click(submitButton)

      // Should only be called once
      expect(handleSubmit).toHaveBeenCalledTimes(1)
    })
  })

  describe('Field Interactions', () => {
    it('handles field dependencies', async () => {
      const DependentForm = () => {
        const form = useForm({
          defaultValues: {
            country: '',
            state: '',
          },
        })

        const country = form.watch('country')

        return (
          <Form {...form}>
            <form>
              <FormField
                control={form.control}
                name="country"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Country</FormLabel>
                    <FormControl>
                      <select {...field}>
                        <option value="">Select country</option>
                        <option value="us">United States</option>
                        <option value="ca">Canada</option>
                      </select>
                    </FormControl>
                  </FormItem>
                )}
              />

              {country && (
                <FormField
                  control={form.control}
                  name="state"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>State/Province</FormLabel>
                      <FormControl>
                        <select {...field}>
                          <option value="">Select state</option>
                          {country === 'us' ? (
                            <>
                              <option value="ny">New York</option>
                              <option value="ca">California</option>
                            </>
                          ) : (
                            <>
                              <option value="on">Ontario</option>
                              <option value="bc">British Columbia</option>
                            </>
                          )}
                        </select>
                      </FormControl>
                    </FormItem>
                  )}
                />
              )}
            </form>
          </Form>
        )
      }

      const { user } = render(<DependentForm />)

      // State field should not be visible initially
      expect(screen.queryByLabelText('State/Province')).not.toBeInTheDocument()

      // Select country
      await user.selectOptions(screen.getByLabelText('Country'), 'us')

      // State field should appear with US states
      const stateSelect = await screen.findByLabelText('State/Province')
      expect(stateSelect).toBeInTheDocument()
      expect(screen.getByText('New York')).toBeInTheDocument()
      expect(screen.queryByText('Ontario')).not.toBeInTheDocument()

      // Change country
      await user.selectOptions(screen.getByLabelText('Country'), 'ca')

      // State options should update
      expect(screen.getByText('Ontario')).toBeInTheDocument()
      expect(screen.queryByText('New York')).not.toBeInTheDocument()
    })
  })

  describe('Accessibility', () => {
    it('associates labels with form controls', () => {
      render(<TestForm onSubmit={vi.fn()} />)

      const nameInput = screen.getByLabelText('Name')
      const nameLabel = screen.getByText('Name', { selector: 'label' })

      expect(nameLabel).toHaveAttribute('for', nameInput.id)
    })

    it('announces validation errors to screen readers', async () => {
      const { user } = render(<TestForm onSubmit={vi.fn()} />)

      await user.click(screen.getByRole('button', { name: 'Submit' }))

      await waitFor(() => {
        const nameInput = screen.getByLabelText('Name')
        const errorId = nameInput.getAttribute('aria-describedby')
        expect(errorId).toBeTruthy()

        const errorMessage = document.getElementById(errorId!)
        expect(errorMessage).toHaveTextContent('Name must be at least 2 characters')
        expect(nameInput).toHaveAttribute('aria-invalid', 'true')
      })
    })

    it('supports keyboard navigation', async () => {
      const { user } = render(<TestForm onSubmit={vi.fn()} />)

      // Tab through form fields
      await user.tab()
      expect(screen.getByLabelText('Name')).toHaveFocus()

      await user.tab()
      expect(screen.getByLabelText('Email')).toHaveFocus()

      await user.tab()
      expect(screen.getByLabelText('Age')).toHaveFocus()

      await user.tab()
      expect(screen.getByRole('checkbox')).toHaveFocus()

      await user.tab()
      expect(screen.getByRole('button', { name: 'Submit' })).toHaveFocus()
    })
  })

  describe('Advanced Patterns', () => {
    it('supports array fields', async () => {
      const ArrayFieldForm = () => {
        const form = useForm({
          defaultValues: {
            items: [{ name: '' }],
          },
        })

        return (
          <Form {...form}>
            <form>
              {form.watch('items').map((_, index) => (
                <FormField
                  key={index}
                  control={form.control}
                  name={`items.${index}.name`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Item {index + 1}</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                    </FormItem>
                  )}
                />
              ))}
              <Button
                type="button"
                onClick={() => form.setValue('items', [...form.getValues('items'), { name: '' }])}
              >
                Add Item
              </Button>
            </form>
          </Form>
        )
      }

      const { user } = render(<ArrayFieldForm />)

      expect(screen.getByLabelText('Item 1')).toBeInTheDocument()

      // Add new item
      await user.click(screen.getByRole('button', { name: 'Add Item' }))

      expect(screen.getByLabelText('Item 2')).toBeInTheDocument()
    })
  })
})
