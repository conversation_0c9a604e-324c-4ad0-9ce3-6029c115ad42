/**
 * Input Component Tests
 */

import { render as renderWithProviders, screen } from '@testing-library/react'
import { describe, expect, it, vi } from 'vitest'
import { LuminarInput } from '../luminar-input'

describe('LuminarInput Component', () => {
  const defaultProps = {
    name: 'test-input',
    label: 'Test Input',
    onChange: vi.fn(),
  }

  // Test basic rendering
  it('renders correctly', () => {
    renderWithProviders(<LuminarInput {...defaultProps} />)

    const input = screen.getByRole('textbox', { name: 'Test Input' })
    const label = screen.getByText('Test Input')

    expect(input).toBeInTheDocument()
    expect(label).toBeInTheDocument()
    expect(input).toHaveAttribute('name', 'test-input')
  })

  // Test value changes
  it('handles value changes', async () => {
    const onChange = vi.fn()
    const { user } = renderWithProviders(<LuminarInput {...defaultProps} onChange={onChange} />)

    const input = screen.getByRole('textbox')
    await user.type(input, 'test value')

    expect(onChange).toHaveBeenCalled()
    expect(input).toHaveValue('test value')
  })

  // Test controlled input
  it('works as controlled component', async () => {
    const onChange = vi.fn()
    const { rerender, user } = renderWithProviders(
      <LuminarInput {...defaultProps} value="initial" onChange={onChange} />
    )

    const input = screen.getByRole('textbox')
    expect(input).toHaveValue('initial')

    // Type in input
    await user.type(input, ' more text')
    expect(onChange).toHaveBeenCalled()

    // Update value prop
    rerender(<LuminarInput {...defaultProps} value="updated" onChange={onChange} />)

    expect(input).toHaveValue('updated')
  })

  // Test uncontrolled input
  it('works as uncontrolled component', async () => {
    const { user } = renderWithProviders(<LuminarInput {...defaultProps} defaultValue="default" />)

    const input = screen.getByRole('textbox')
    expect(input).toHaveValue('default')

    await user.clear(input)
    await user.type(input, 'new value')
    expect(input).toHaveValue('new value')
  })

  // Test validation states
  describe('validation', () => {
    it('shows error state', () => {
      renderWithProviders(<LuminarInput {...defaultProps} error="This field is required" invalid />)

      const input = screen.getByRole('textbox')
      const errorMessage = screen.getByText('This field is required')

      expect(input).toHaveAttribute('aria-invalid', 'true')
      expect(input).toHaveAttribute('aria-describedby')
      expect(errorMessage).toBeInTheDocument()
      expect(errorMessage).toHaveClass('text-red-600')
    })

    it('shows success state', () => {
      renderWithProviders(<LuminarInput {...defaultProps} valid success="Input is valid" />)

      const input = screen.getByRole('textbox')
      const successMessage = screen.getByText('Input is valid')

      expect(input).toHaveAttribute('aria-invalid', 'false')
      expect(successMessage).toBeInTheDocument()
      expect(successMessage).toHaveClass('text-green-600')
    })

    it('shows warning state', () => {
      renderWithProviders(<LuminarInput {...defaultProps} warning="This might be incorrect" />)

      const _input = screen.getByRole('textbox')
      const warningMessage = screen.getByText('This might be incorrect')

      expect(warningMessage).toBeInTheDocument()
      expect(warningMessage).toHaveClass('text-yellow-600')
    })
  })

  // Test required field
  it('handles required state', () => {
    renderWithProviders(<LuminarInput {...defaultProps} required />)

    const input = screen.getByRole('textbox')
    const label = screen.getByText('Test Input')

    expect(input).toHaveAttribute('required')
    expect(input).toHaveAttribute('aria-required', 'true')
    expect(label).toHaveTextContent('*') // Required indicator
  })

  // Test disabled state
  it('handles disabled state', () => {
    renderWithProviders(<LuminarInput {...defaultProps} disabled />)

    const input = screen.getByRole('textbox')
    expect(input).toBeDisabled()
    expect(input).toHaveAttribute('aria-disabled', 'true')
  })

  // Test readonly state
  it('handles readonly state', () => {
    renderWithProviders(<LuminarInput {...defaultProps} readOnly />)

    const input = screen.getByRole('textbox')
    expect(input).toHaveAttribute('readonly')
  })

  // Test input types
  describe('input types', () => {
    const types = ['text', 'email', 'password', 'number', 'tel', 'url'] as const

    types.forEach((type) => {
      it(`renders ${type} input correctly`, () => {
        renderWithProviders(<LuminarInput {...defaultProps} type={type} />)

        const input = screen.getByRole(
          type === 'email'
            ? 'textbox'
            : type === 'password'
              ? 'textbox'
              : type === 'number'
                ? 'spinbutton'
                : 'textbox'
        )

        expect(input).toHaveAttribute('type', type)
      })
    })
  })

  // Test with icon
  it('renders with icon', () => {
    const TestIcon = () => <span data-testid="test-icon">Icon</span>

    renderWithProviders(<LuminarInput {...defaultProps} icon={<TestIcon />} />)

    expect(screen.getByTestId('test-icon')).toBeInTheDocument()
  })

  // Test with prefix/suffix
  it('renders with prefix and suffix', () => {
    renderWithProviders(<LuminarInput {...defaultProps} prefix="$" suffix=".00" />)

    expect(screen.getByText('$')).toBeInTheDocument()
    expect(screen.getByText('.00')).toBeInTheDocument()
  })

  // Test placeholder
  it('shows placeholder text', () => {
    renderWithProviders(<LuminarInput {...defaultProps} placeholder="Enter text here" />)

    const input = screen.getByRole('textbox')
    expect(input).toHaveAttribute('placeholder', 'Enter text here')
  })

  // Test help text
  it('shows help text', () => {
    renderWithProviders(<LuminarInput {...defaultProps} help="This is help text" />)

    const helpText = screen.getByText('This is help text')
    const input = screen.getByRole('textbox')

    expect(helpText).toBeInTheDocument()
    expect(input).toHaveAttribute('aria-describedby')
  })

  // Test character limit
  it('shows character count with maxLength', async () => {
    const { user } = renderWithProviders(
      <LuminarInput {...defaultProps} maxLength={10} showCharCount />
    )

    const input = screen.getByRole('textbox')
    const charCount = screen.getByText('0/10')

    expect(charCount).toBeInTheDocument()

    await user.type(input, 'hello')
    expect(screen.getByText('5/10')).toBeInTheDocument()
  })

  // Accessibility tests
  describe('accessibility', () => {
    it('has proper ARIA attributes', () => {
      renderWithProviders(<LuminarInput {...defaultProps} help="Help text" error="Error text" />)

      const input = screen.getByRole('textbox')

      expect(input).toHaveAttribute('aria-describedby')
      expect(input).toHaveAccessibleName('Test Input')
    })

    it('supports keyboard navigation', async () => {
      const onFocus = vi.fn()
      const onBlur = vi.fn()
      const { user } = renderWithProviders(
        <LuminarInput {...defaultProps} onFocus={onFocus} onBlur={onBlur} />
      )

      const input = screen.getByRole('textbox')

      // Tab to input
      await user.tab()
      expect(input).toHaveFocus()
      expect(onFocus).toHaveBeenCalled()

      // Tab away
      await user.tab()
      expect(input).not.toHaveFocus()
      expect(onBlur).toHaveBeenCalled()
    })

    it('announces validation errors to screen readers', async () => {
      const { rerender } = renderWithProviders(<LuminarInput {...defaultProps} />)

      // Add error
      rerender(<LuminarInput {...defaultProps} error="This field is required" invalid />)

      const input = screen.getByRole('textbox')
      const errorMessage = screen.getByText('This field is required')

      expect(input).toHaveAttribute('aria-invalid', 'true')
      expect(errorMessage).toHaveAttribute('role', 'alert')
    })
  })

  // Performance tests
  describe('performance', () => {
    it('renders quickly', () => {
      const start = performance.now()
      renderWithProviders(<LuminarInput {...defaultProps} />)
      const end = performance.now()

      expect(end - start).toBeLessThan(50)
    })

    it('handles rapid typing efficiently', async () => {
      const onChange = vi.fn()
      const { user } = renderWithProviders(<LuminarInput {...defaultProps} onChange={onChange} />)

      const input = screen.getByRole('textbox')

      // Type rapidly
      const text = 'rapid typing test'
      await user.type(input, text, { delay: 1 })

      expect(input).toHaveValue(text)
      expect(onChange).toHaveBeenCalledTimes(text.length)
    })
  })

  // Form integration
  describe('form integration', () => {
    it('works with form validation', async () => {
      const onSubmit = vi.fn()
      const { user } = renderWithProviders(
        <form onSubmit={onSubmit}>
          <LuminarInput {...defaultProps} required />
          <button type="submit">Submit</button>
        </form>
      )

      const submitButton = screen.getByRole('button', { name: 'Submit' })

      // Try to submit without filling required field
      await user.click(submitButton)

      const input = screen.getByRole('textbox')
      expect(input).toBeInvalid()
    })

    it('submits form data correctly', async () => {
      const onSubmit = vi.fn((e) => {
        e.preventDefault()
        const formData = new FormData(e.target)
        return formData.get('test-input')
      })

      const { user } = renderWithProviders(
        <form onSubmit={onSubmit}>
          <LuminarInput {...defaultProps} />
          <button type="submit">Submit</button>
        </form>
      )

      const input = screen.getByRole('textbox')
      const submitButton = screen.getByRole('button', { name: 'Submit' })

      await user.type(input, 'form data')
      await user.click(submitButton)

      expect(onSubmit).toHaveBeenCalled()
    })
  })
})
