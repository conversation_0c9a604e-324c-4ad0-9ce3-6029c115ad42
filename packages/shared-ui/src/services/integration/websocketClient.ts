import { io, Socket } from 'socket.io-client'

// Event handler type
type EventHandler = (...args: any[]) => void

// Simple EventEmitter interface for browser compatibility
interface EventEmitterInterface {
  on(event: string, handler: <PERSON><PERSON>and<PERSON>): this
  off(event: string, handler: <PERSON><PERSON><PERSON><PERSON>): this
  emit(event: string, ...args: any[]): boolean
  removeAllListeners(event?: string): this
}

export interface IWebSocketClient extends EventEmitterInterface {
  connect(): Promise<void>
  disconnect(): void
  send(type: string, data: any): Promise<void>
  isConnected(): boolean
  getSocket(): Socket | null
}

export class WebSocketClient implements IWebSocketClient {
  private static instance: WebSocketClient
  private socket: Socket | null = null
  private wsUrl: string
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectDelay = 1000
  private connectionPromise: Promise<void> | null = null
  private eventHandlers: Map<string, Set<EventHandler>> = new Map()

  private constructor(wsUrl?: string) {
    this.wsUrl = wsUrl || 'ws://localhost:3000'
  }

  static getInstance(wsUrl?: string): WebSocketClient {
    if (!WebSocketClient.instance) {
      WebSocketClient.instance = new WebSocketClient(wsUrl)
    }
    return WebSocketClient.instance
  }

  async connect(): Promise<void> {
    // If already connected, return immediately
    if (this.socket?.connected) {
      return Promise.resolve()
    }

    // If connection is in progress, return the existing promise
    if (this.connectionPromise) {
      return this.connectionPromise
    }

    this.connectionPromise = new Promise((resolve, reject) => {
      try {
        // Create Socket.IO connection to the /amna namespace
        this.socket = io(`${this.wsUrl}/amna`, {
          transports: ['websocket', 'polling'],
          reconnection: true,
          reconnectionAttempts: this.maxReconnectAttempts,
          reconnectionDelay: this.reconnectDelay,
          reconnectionDelayMax: 5000,
          timeout: 20000,
          // Add auth token if available
          auth: {
            token: this.getAuthToken(),
          },
        })

        // Handle connection events
        this.socket.on('connect', () => {
          console.log('WebSocket connected to integration service')
          this.reconnectAttempts = 0
          this.emit('connected')
          resolve()
        })

        this.socket.on('disconnect', (reason) => {
          console.log('WebSocket disconnected:', reason)
          this.emit('disconnected', reason)
        })

        this.socket.on('connect_error', (error) => {
          console.error('WebSocket connection error:', error.message)
          this.emit('error', error)
          
          if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            reject(new Error('Max reconnection attempts reached'))
          }
        })

        // Handle integration-specific events
        this.socket.on('integration.update', (data) => {
          this.emit('integration.update', data)
        })

        this.socket.on('status', (data) => {
          this.emit('status', data)
        })

        this.socket.on('error', (error) => {
          console.error('WebSocket error:', error)
          this.emit('error', error)
        })

        // Handle reconnection
        this.socket.io.on('reconnect', (attempt) => {
          console.log(`WebSocket reconnected after ${attempt} attempts`)
          this.reconnectAttempts = 0
          this.emit('reconnected')
        })

        this.socket.io.on('reconnect_attempt', (attempt) => {
          this.reconnectAttempts = attempt
          this.emit('reconnecting', attempt)
        })

        this.socket.io.on('reconnect_error', (error) => {
          console.error('WebSocket reconnection error:', error.message)
          this.emit('reconnect_error', error)
        })

        this.socket.io.on('reconnect_failed', () => {
          console.error('WebSocket reconnection failed')
          this.emit('reconnect_failed')
          reject(new Error('Reconnection failed'))
        })

      } catch (error) {
        console.error('Failed to create WebSocket connection:', error)
        reject(error)
      }
    })

    this.connectionPromise.finally(() => {
      this.connectionPromise = null
    })

    return this.connectionPromise
  }

  disconnect(): void {
    if (this.socket) {
      this.socket.removeAllListeners()
      this.socket.disconnect()
      this.socket = null
    }
    this.removeAllListeners()
  }

  // EventEmitter implementation
  on(event: string, handler: EventHandler): this {
    if (!this.eventHandlers.has(event)) {
      this.eventHandlers.set(event, new Set())
    }
    this.eventHandlers.get(event)!.add(handler)
    return this
  }

  off(event: string, handler: EventHandler): this {
    const handlers = this.eventHandlers.get(event)
    if (handlers) {
      handlers.delete(handler)
      if (handlers.size === 0) {
        this.eventHandlers.delete(event)
      }
    }
    return this
  }

  emit(event: string, ...args: any[]): boolean {
    const handlers = this.eventHandlers.get(event)
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(...args)
        } catch (error) {
          console.error(`Error in event handler for ${event}:`, error)
        }
      })
      return true
    }
    return false
  }

  removeAllListeners(event?: string): this {
    if (event) {
      this.eventHandlers.delete(event)
    } else {
      this.eventHandlers.clear()
    }
    return this
  }

  async send(type: string, data: any): Promise<void> {
    if (!this.socket?.connected) {
      throw new Error('WebSocket not connected')
    }

    return new Promise((resolve, reject) => {
      // Send with acknowledgment
      this.socket!.emit(type, data, (error?: Error) => {
        if (error) {
          reject(error)
        } else {
          resolve()
        }
      })
    })
  }

  isConnected(): boolean {
    return this.socket?.connected || false
  }

  getSocket(): Socket | null {
    return this.socket
  }

  private getAuthToken(): string | undefined {
    // Try to get auth token from localStorage or session
    try {
      const token = localStorage.getItem('auth_token') || 
                   sessionStorage.getItem('auth_token')
      return token || undefined
    } catch {
      return undefined
    }
  }
}

export const createWebSocketClient = (wsUrl?: string): IWebSocketClient => {
  return WebSocketClient.getInstance(wsUrl)
}

export default WebSocketClient
