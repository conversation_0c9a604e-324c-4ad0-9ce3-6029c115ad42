export interface IntegrationStatus {
  name: string
  connected: boolean
  lastSync?: Date
  error?: string
  metadata?: Record<string, any>
}

export interface IntegrationConfig {
  [key: string]: any
}

export interface IIntegrationClient {
  getAllStatuses(): Promise<Record<string, IntegrationStatus>>
  getIntegrationStatus(name: string): Promise<IntegrationStatus>
  syncIntegration(name: string): Promise<void>
  configureIntegration(name: string, config: IntegrationConfig): Promise<void>
}

export class IntegrationClient implements IIntegrationClient {
  private static instance: IntegrationClient
  private apiUrl: string
  private headers: Record<string, string>

  private constructor(apiUrl?: string) {
    this.apiUrl = apiUrl || 'http://localhost:3000/api/v1'
    this.headers = {
      'Content-Type': 'application/json',
    }
  }

  static getInstance(apiUrl?: string): IntegrationClient {
    if (!IntegrationClient.instance) {
      IntegrationClient.instance = new IntegrationClient(apiUrl)
    }
    return IntegrationClient.instance
  }

  private getAuthHeaders(): Record<string, string> {
    const token = this.getAuthToken()
    if (token) {
      return {
        ...this.headers,
        'Authorization': `Bear<PERSON> ${token}`,
      }
    }
    return this.headers
  }

  private getAuthToken(): string | null {
    try {
      return localStorage.getItem('auth_token') || 
             sessionStorage.getItem('auth_token') ||
             null
    } catch {
      return null
    }
  }

  private async handleResponse<T>(response: Response): Promise<T> {
    if (!response.ok) {
      const error = await response.text()
      throw new Error(`Integration API error: ${response.status} - ${error}`)
    }
    return response.json()
  }

  async getAllStatuses(): Promise<Record<string, IntegrationStatus>> {
    try {
      const response = await fetch(`${this.apiUrl}/integration/status`, {
        method: 'GET',
        headers: this.getAuthHeaders(),
      })

      const data = await this.handleResponse<{ statuses: Record<string, IntegrationStatus> }>(response)
      return data.statuses || {}
    } catch (error) {
      console.error('Failed to fetch integration statuses:', error)
      throw error
    }
  }

  async getIntegrationStatus(name: string): Promise<IntegrationStatus> {
    try {
      const response = await fetch(`${this.apiUrl}/integration/status/${name}`, {
        method: 'GET',
        headers: this.getAuthHeaders(),
      })

      return this.handleResponse<IntegrationStatus>(response)
    } catch (error) {
      console.error(`Failed to fetch status for integration ${name}:`, error)
      throw error
    }
  }

  async syncIntegration(name: string): Promise<void> {
    try {
      const response = await fetch(`${this.apiUrl}/integration/sync/${name}`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
      })

      await this.handleResponse<void>(response)
    } catch (error) {
      console.error(`Failed to sync integration ${name}:`, error)
      throw error
    }
  }

  async configureIntegration(name: string, config: IntegrationConfig): Promise<void> {
    try {
      const response = await fetch(`${this.apiUrl}/integration/config/${name}`, {
        method: 'PUT',
        headers: this.getAuthHeaders(),
        body: JSON.stringify(config),
      })

      await this.handleResponse<void>(response)
    } catch (error) {
      console.error(`Failed to configure integration ${name}:`, error)
      throw error
    }
  }
}

export const createIntegrationClient = (apiUrl?: string): IIntegrationClient => {
  return IntegrationClient.getInstance(apiUrl)
}

export default IntegrationClient
