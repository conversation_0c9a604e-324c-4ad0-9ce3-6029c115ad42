#!/bin/bash

# ==============================================
# Luminar Platform - Frontend Development Script
# ==============================================
# Starts only frontend applications for UI development
# Usage: ./scripts/dev-frontend.sh [app-name]
# If no app name provided, starts all frontend apps
# ==============================================

set -e

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# Script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_ROOT="$( cd "$SCRIPT_DIR/.." && pwd )"

# Load environment
source "$PROJECT_ROOT/.env"

# Frontend apps configuration
declare -A FRONTEND_APPS=(
    ["amna"]="$AMNA_DEV_PORT:AMNA AI Assistant"
    ["e-connect"]="$E_CONNECT_DEV_PORT:E-Connect Email Assistant"
    ["lighthouse"]="$LIGHTHOUSE_DEV_PORT:Lighthouse Knowledge Base"
    ["training-need-analysis"]="$TRAINING_DEV_PORT:Training Need Analysis"
    ["vendors"]="$VENDORS_DEV_PORT:Vendor Management"
    ["wins-of-week"]="$WINS_DEV_PORT:Wins of the Week"
    ["shell"]="$SHELL_DEV_PORT:Shell/Launcher"
    ["service-monitor"]="$SERVICE_MONITOR_PORT:Service Monitor"
)

print_header() {
    echo -e "\n${BLUE}=== $1 ===${NC}"
}

print_step() {
    echo -e "${CYAN}→ $1${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

start_single_app() {
    local app=$1
    if [[ ! ${FRONTEND_APPS[$app]+_} ]]; then
        echo -e "${YELLOW}Unknown app: $app${NC}"
        echo "Available apps: ${!FRONTEND_APPS[@]}"
        exit 1
    fi
    
    IFS=':' read -r port name <<< "${FRONTEND_APPS[$app]}"
    print_header "Starting $name"
    
    cd "$PROJECT_ROOT/apps/$app"
    
    # Install dependencies if needed
    if [ ! -d "node_modules" ]; then
        print_step "Installing dependencies..."
        pnpm install
    fi
    
    print_step "Starting $name on port $port..."
    pnpm run dev
}

start_all_apps() {
    print_header "Starting All Frontend Applications"
    
    # Check if backend is running
    if ! curl -s http://localhost:$COMMAND_CENTER_PORT/health > /dev/null; then
        echo -e "${YELLOW}Warning: Backend API is not running at http://localhost:$COMMAND_CENTER_PORT${NC}"
        echo -e "${YELLOW}Frontend apps may not function properly without the backend.${NC}"
        echo -e "${YELLOW}Run './scripts/dev-backend.sh' to start the backend.${NC}"
        echo
        read -p "Continue anyway? (y/N) " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
    
    # Create tmux session for frontend apps
    tmux new-session -d -s luminar-frontend
    
    local window=0
    for app in "${!FRONTEND_APPS[@]}"; do
        IFS=':' read -r port name <<< "${FRONTEND_APPS[$app]}"
        
        if [ $window -eq 0 ]; then
            tmux rename-window -t luminar-frontend:0 "$app"
        else
            tmux new-window -t luminar-frontend:$window -n "$app"
        fi
        
        tmux send-keys -t luminar-frontend:$window "cd $PROJECT_ROOT/apps/$app && pnpm run dev" C-m
        
        window=$((window + 1))
    done
    
    print_success "All frontend applications started"
    
    # Print URLs
    echo -e "\n${GREEN}Frontend Application URLs:${NC}"
    for app in "${!FRONTEND_APPS[@]}"; do
        IFS=':' read -r port name <<< "${FRONTEND_APPS[$app]}"
        printf "  • %-25s http://localhost:%s\n" "$name:" "$port"
    done
    
    echo -e "\n${YELLOW}Tips:${NC}"
    echo "  • View logs: tmux attach -t luminar-frontend"
    echo "  • Switch windows: Ctrl+B then window number (0-7)"
    echo "  • Detach: Ctrl+B then D"
    echo "  • Kill session: tmux kill-session -t luminar-frontend"
}

# Main execution
if [ $# -eq 0 ]; then
    # No arguments, start all apps
    start_all_apps
else
    # Start specific app
    start_single_app "$1"
fi