#!/bin/bash

# ==============================================
# Luminar Platform - Stop All Services Script
# ==============================================
# Gracefully stops all running services
# Usage: ./scripts/stop-all.sh [options]
# Options:
#   --force       Force stop without graceful shutdown
#   --clean       Remove containers and volumes after stopping
# ==============================================

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# Script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_ROOT="$( cd "$SCRIPT_DIR/.." && pwd )"

# Parse arguments
FORCE_STOP=false
CLEAN_UP=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --force)
            FORCE_STOP=true
            shift
            ;;
        --clean)
            CLEAN_UP=true
            shift
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

print_header() {
    echo -e "\n${BLUE}=== $1 ===${NC}"
}

print_step() {
    echo -e "${CYAN}→ $1${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

stop_frontend_apps() {
    print_header "Stopping Frontend Applications"
    
    # Kill processes using PID files
    for app in amna e-connect lighthouse training-need-analysis vendors wins-of-week shell service-monitor; do
        if [ -f "$PROJECT_ROOT/logs/$app.pid" ]; then
            PID=$(cat "$PROJECT_ROOT/logs/$app.pid")
            if ps -p $PID > /dev/null 2>&1; then
                print_step "Stopping $app (PID: $PID)..."
                if [ "$FORCE_STOP" = true ]; then
                    kill -9 $PID 2>/dev/null || true
                else
                    kill $PID 2>/dev/null || true
                fi
                rm -f "$PROJECT_ROOT/logs/$app.pid"
            else
                print_step "$app already stopped"
                rm -f "$PROJECT_ROOT/logs/$app.pid"
            fi
        fi
    done
    
    # Kill any remaining node processes on frontend ports
    for port in 5001 5002 5003 5005 5006 5007 5008 5009; do
        PID=$(lsof -ti:$port 2>/dev/null || true)
        if [ -n "$PID" ]; then
            print_step "Stopping process on port $port..."
            kill $PID 2>/dev/null || true
        fi
    done
    
    print_success "Frontend applications stopped"
}

stop_backend_services() {
    print_header "Stopping Backend Services"
    
    # Stop Command Center
    if [ -f "$PROJECT_ROOT/logs/command-center.pid" ]; then
        PID=$(cat "$PROJECT_ROOT/logs/command-center.pid")
        if ps -p $PID > /dev/null 2>&1; then
            print_step "Stopping Command Center (PID: $PID)..."
            if [ "$FORCE_STOP" = true ]; then
                kill -9 $PID 2>/dev/null || true
            else
                kill $PID 2>/dev/null || true
            fi
        fi
        rm -f "$PROJECT_ROOT/logs/command-center.pid"
    fi
    
    # Stop Document Processor
    if [ -f "$PROJECT_ROOT/logs/document-processor.pid" ]; then
        PID=$(cat "$PROJECT_ROOT/logs/document-processor.pid")
        if ps -p $PID > /dev/null 2>&1; then
            print_step "Stopping Document Processor (PID: $PID)..."
            if [ "$FORCE_STOP" = true ]; then
                kill -9 $PID 2>/dev/null || true
            else
                kill $PID 2>/dev/null || true
            fi
        fi
        rm -f "$PROJECT_ROOT/logs/document-processor.pid"
    fi
    
    # Kill processes on backend ports
    for port in 3000 8001; do
        PID=$(lsof -ti:$port 2>/dev/null || true)
        if [ -n "$PID" ]; then
            print_step "Stopping process on port $port..."
            kill $PID 2>/dev/null || true
        fi
    done
    
    print_success "Backend services stopped"
}

stop_tmux_sessions() {
    print_header "Stopping tmux Sessions"
    
    # Stop luminar-dev session
    if tmux has-session -t luminar-dev 2>/dev/null; then
        print_step "Stopping luminar-dev session..."
        tmux kill-session -t luminar-dev
    fi
    
    # Stop luminar-frontend session
    if tmux has-session -t luminar-frontend 2>/dev/null; then
        print_step "Stopping luminar-frontend session..."
        tmux kill-session -t luminar-frontend
    fi
    
    # Stop luminar-backend session
    if tmux has-session -t luminar-backend 2>/dev/null; then
        print_step "Stopping luminar-backend session..."
        tmux kill-session -t luminar-backend
    fi
    
    print_success "tmux sessions stopped"
}

stop_development_tools() {
    print_header "Stopping Development Tools"
    
    # Stop Prisma Studio
    PID=$(lsof -ti:5555 2>/dev/null || true)
    if [ -n "$PID" ]; then
        print_step "Stopping Prisma Studio..."
        kill $PID 2>/dev/null || true
    fi
    
    # Stop PgAdmin if running
    if pgrep -f pgadmin4 > /dev/null; then
        print_step "Stopping PgAdmin..."
        pkill -f pgadmin4 || true
    fi
    
    print_success "Development tools stopped"
}

stop_docker_services() {
    print_header "Stopping Docker Services"
    
    if [ "$CLEAN_UP" = true ]; then
        print_step "Stopping and removing all containers..."
        docker-compose -f "$PROJECT_ROOT/docker-compose.yml" down -v --remove-orphans
        
        if [ -f "$PROJECT_ROOT/docker-compose.monitoring.yml" ]; then
            docker-compose -f "$PROJECT_ROOT/docker-compose.monitoring.yml" down -v --remove-orphans
        fi
    else
        print_step "Stopping Docker containers..."
        docker-compose -f "$PROJECT_ROOT/docker-compose.yml" stop
        
        if [ -f "$PROJECT_ROOT/docker-compose.monitoring.yml" ]; then
            docker-compose -f "$PROJECT_ROOT/docker-compose.monitoring.yml" stop
        fi
    fi
    
    print_success "Docker services stopped"
}

clean_up_files() {
    if [ "$CLEAN_UP" = false ]; then
        return
    fi
    
    print_header "Cleaning Up"
    
    # Remove PID files
    print_step "Removing PID files..."
    rm -f "$PROJECT_ROOT/logs"/*.pid
    
    # Remove log files
    read -p "Remove log files? (y/N) " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_step "Removing log files..."
        rm -f "$PROJECT_ROOT/logs"/*.log
    fi
    
    print_success "Cleanup completed"
}

print_summary() {
    print_header "Stop Summary"
    
    echo -e "${GREEN}Services stopped:${NC}"
    echo "  • Frontend applications"
    echo "  • Backend services"
    echo "  • Development tools"
    echo "  • Docker containers"
    
    if [ "$CLEAN_UP" = true ]; then
        echo -e "\n${GREEN}Cleanup performed:${NC}"
        echo "  • Removed containers and volumes"
        echo "  • Cleaned up PID files"
    fi
    
    echo -e "\n${YELLOW}To start services again:${NC}"
    echo "  • Full stack:       ./scripts/start-all.sh"
    echo "  • Development:      ./scripts/dev-start.sh"
    echo "  • Infrastructure:   ./scripts/dev-infra.sh"
}

# Main execution
main() {
    print_header "Stopping Luminar Platform"
    
    if [ "$FORCE_STOP" = true ]; then
        print_warning "Force stop enabled - services will be terminated immediately"
    fi
    
    # Stop services in reverse order
    stop_frontend_apps
    stop_backend_services
    stop_tmux_sessions
    stop_development_tools
    stop_docker_services
    
    # Clean up if requested
    clean_up_files
    
    # Print summary
    print_summary
    
    print_success "All services stopped successfully"
}

# Run main function
main