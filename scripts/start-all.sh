#!/bin/bash

# ==============================================
# Luminar Platform - Master Start Script
# ==============================================
# Starts all services in the correct order with health checks
# Usage: ./scripts/start-all.sh [options]
# Options:
#   --skip-build    Skip building Docker images
#   --skip-health   Skip health checks
#   --dev           Start in development mode
#   --monitor       Start with monitoring enabled
# ==============================================

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_ROOT="$( cd "$SCRIPT_DIR/.." && pwd )"

# Load environment
if [ ! -f "$PROJECT_ROOT/.env" ]; then
    echo -e "${YELLOW}No .env file found. Creating from template...${NC}"
    cp "$PROJECT_ROOT/.env.full-stack.example" "$PROJECT_ROOT/.env"
    echo -e "${GREEN}Created .env file. Please review and update if needed.${NC}"
fi

# Source environment variables
set -a
source "$PROJECT_ROOT/.env"
set +a

# Parse arguments
SKIP_BUILD=false
SKIP_HEALTH=false
DEV_MODE=false
MONITOR_MODE=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --skip-build)
            SKIP_BUILD=true
            shift
            ;;
        --skip-health)
            SKIP_HEALTH=true
            shift
            ;;
        --dev)
            DEV_MODE=true
            shift
            ;;
        --monitor)
            MONITOR_MODE=true
            shift
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Functions
print_header() {
    echo
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}========================================${NC}"
}

print_step() {
    echo -e "${CYAN}→ $1${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

check_prerequisites() {
    print_header "Checking Prerequisites"
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed"
        exit 1
    fi
    print_success "Docker installed"
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed"
        exit 1
    fi
    print_success "Docker Compose installed"
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed"
        exit 1
    fi
    print_success "Node.js installed"
    
    # Check pnpm
    if ! command -v pnpm &> /dev/null; then
        print_error "pnpm is not installed"
        exit 1
    fi
    print_success "pnpm installed"
    
    # Check Python
    if ! command -v python3 &> /dev/null; then
        print_error "Python 3 is not installed"
        exit 1
    fi
    print_success "Python 3 installed"
}

build_images() {
    if [ "$SKIP_BUILD" = true ]; then
        print_warning "Skipping Docker image build"
        return
    fi
    
    print_header "Building Docker Images"
    
    print_step "Building infrastructure images..."
    docker-compose -f "$PROJECT_ROOT/docker-compose.yml" build --parallel postgres redis elasticsearch rabbitmq minio qdrant
    
    print_step "Building application images..."
    docker-compose -f "$PROJECT_ROOT/docker-compose.yml" build --parallel command-center
    
    print_success "All images built successfully"
}

start_infrastructure() {
    print_header "Starting Infrastructure Services"
    
    print_step "Starting PostgreSQL..."
    docker-compose -f "$PROJECT_ROOT/docker-compose.yml" up -d postgres
    sleep 5
    
    print_step "Starting Redis..."
    docker-compose -f "$PROJECT_ROOT/docker-compose.yml" up -d redis
    sleep 3
    
    print_step "Starting Elasticsearch..."
    docker-compose -f "$PROJECT_ROOT/docker-compose.yml" up -d elasticsearch
    sleep 10
    
    print_step "Starting RabbitMQ..."
    docker-compose -f "$PROJECT_ROOT/docker-compose.yml" up -d rabbitmq
    sleep 5
    
    print_step "Starting MinIO..."
    docker-compose -f "$PROJECT_ROOT/docker-compose.yml" up -d minio
    sleep 3
    
    print_step "Starting Qdrant..."
    docker-compose -f "$PROJECT_ROOT/docker-compose.yml" up -d qdrant
    sleep 3
    
    if [ "$DEV_MODE" = true ]; then
        print_step "Starting Ollama..."
        docker-compose -f "$PROJECT_ROOT/docker-compose.yml" up -d ollama
        sleep 5
    fi
    
    print_success "Infrastructure services started"
}

wait_for_service() {
    local service_name=$1
    local check_command=$2
    local max_attempts=30
    local attempt=0
    
    echo -n "Waiting for $service_name..."
    while [ $attempt -lt $max_attempts ]; do
        if eval "$check_command" &> /dev/null; then
            echo -e " ${GREEN}ready${NC}"
            return 0
        fi
        echo -n "."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    echo -e " ${RED}timeout${NC}"
    return 1
}

check_infrastructure_health() {
    if [ "$SKIP_HEALTH" = true ]; then
        print_warning "Skipping health checks"
        return
    fi
    
    print_header "Checking Infrastructure Health"
    
    # PostgreSQL
    wait_for_service "PostgreSQL" "docker exec luminar-postgres pg_isready -U ${DATABASE_USERNAME}"
    
    # Redis
    wait_for_service "Redis" "docker exec luminar-redis redis-cli ping"
    
    # Elasticsearch
    wait_for_service "Elasticsearch" "curl -s http://localhost:${ELASTICSEARCH_PORT}/_cluster/health"
    
    # RabbitMQ
    wait_for_service "RabbitMQ" "curl -s http://localhost:${RABBITMQ_MGMT_PORT}/api/health/checks/alarms"
    
    # MinIO
    wait_for_service "MinIO" "curl -s http://localhost:${MINIO_PORT}/minio/health/live"
    
    # Qdrant
    wait_for_service "Qdrant" "curl -s http://localhost:${QDRANT_PORT}/health"
    
    print_success "All infrastructure services are healthy"
}

setup_databases() {
    print_header "Setting Up Databases"
    
    print_step "Running database migrations..."
    cd "$PROJECT_ROOT/apps/command-center"
    npx prisma migrate deploy
    
    print_step "Seeding database..."
    npx prisma db seed
    
    cd "$PROJECT_ROOT"
    print_success "Database setup completed"
}

start_backend_services() {
    print_header "Starting Backend Services"
    
    # Start Command Center
    print_step "Starting Command Center API..."
    cd "$PROJECT_ROOT/apps/command-center"
    if [ "$DEV_MODE" = true ]; then
        nohup pnpm run start:dev > "$PROJECT_ROOT/logs/command-center.log" 2>&1 &
        echo $! > "$PROJECT_ROOT/logs/command-center.pid"
    else
        nohup pnpm run start:prod > "$PROJECT_ROOT/logs/command-center.log" 2>&1 &
        echo $! > "$PROJECT_ROOT/logs/command-center.pid"
    fi
    
    # Start Python Document Processor
    print_step "Starting Python Document Processor..."
    cd "$PROJECT_ROOT/apps/python-services"
    if [ ! -d "venv" ]; then
        python3 -m venv venv
        source venv/bin/activate
        pip install -r requirements.txt
    else
        source venv/bin/activate
    fi
    
    nohup python main.py > "$PROJECT_ROOT/logs/document-processor.log" 2>&1 &
    echo $! > "$PROJECT_ROOT/logs/document-processor.pid"
    
    cd "$PROJECT_ROOT"
    print_success "Backend services started"
}

start_frontend_apps() {
    print_header "Starting Frontend Applications"
    
    # Array of frontend apps
    declare -A FRONTEND_APPS=(
        ["amna"]="AMNA AI Assistant:$AMNA_DEV_PORT"
        ["e-connect"]="E-Connect Email Assistant:$E_CONNECT_DEV_PORT"
        ["lighthouse"]="Lighthouse Knowledge Base:$LIGHTHOUSE_DEV_PORT"
        ["training-need-analysis"]="Training Need Analysis:$TRAINING_DEV_PORT"
        ["vendors"]="Vendor Management:$VENDORS_DEV_PORT"
        ["wins-of-week"]="Wins of the Week:$WINS_DEV_PORT"
        ["shell"]="Shell/Launcher:$SHELL_DEV_PORT"
        ["service-monitor"]="Service Monitor:$SERVICE_MONITOR_PORT"
    )
    
    # Start each frontend app
    for app in "${!FRONTEND_APPS[@]}"; do
        IFS=':' read -r app_name port <<< "${FRONTEND_APPS[$app]}"
        print_step "Starting $app_name on port $port..."
        
        cd "$PROJECT_ROOT/apps/$app"
        if [ "$DEV_MODE" = true ]; then
            nohup pnpm run dev > "$PROJECT_ROOT/logs/$app.log" 2>&1 &
        else
            pnpm run build
            nohup pnpm run preview > "$PROJECT_ROOT/logs/$app.log" 2>&1 &
        fi
        echo $! > "$PROJECT_ROOT/logs/$app.pid"
        sleep 2
    done
    
    cd "$PROJECT_ROOT"
    print_success "All frontend applications started"
}

start_monitoring() {
    if [ "$MONITOR_MODE" = false ]; then
        return
    fi
    
    print_header "Starting Monitoring Services"
    
    print_step "Starting Prometheus..."
    docker-compose -f "$PROJECT_ROOT/docker-compose.monitoring.yml" up -d prometheus
    
    print_step "Starting Grafana..."
    docker-compose -f "$PROJECT_ROOT/docker-compose.monitoring.yml" up -d grafana
    
    print_step "Starting Jaeger..."
    docker-compose -f "$PROJECT_ROOT/docker-compose.monitoring.yml" up -d jaeger
    
    print_success "Monitoring services started"
}

print_service_urls() {
    print_header "Service URLs"
    
    echo -e "${GREEN}Frontend Applications:${NC}"
    echo "  • Shell/Launcher:        http://localhost:$SHELL_DEV_PORT"
    echo "  • AMNA AI Assistant:     http://localhost:$AMNA_DEV_PORT"
    echo "  • E-Connect:             http://localhost:$E_CONNECT_DEV_PORT"
    echo "  • Lighthouse:            http://localhost:$LIGHTHOUSE_DEV_PORT"
    echo "  • Training Analysis:     http://localhost:$TRAINING_DEV_PORT"
    echo "  • Vendor Management:     http://localhost:$VENDORS_DEV_PORT"
    echo "  • Wins of the Week:      http://localhost:$WINS_DEV_PORT"
    echo "  • Service Monitor:       http://localhost:$SERVICE_MONITOR_PORT"
    echo
    echo -e "${GREEN}Backend Services:${NC}"
    echo "  • Command Center API:    http://localhost:$COMMAND_CENTER_PORT"
    echo "  • Document Processor:    http://localhost:$DOCUMENT_PROCESSOR_PORT"
    echo
    echo -e "${GREEN}Infrastructure:${NC}"
    echo "  • PostgreSQL:            localhost:$POSTGRES_PORT"
    echo "  • Redis:                 localhost:$REDIS_PORT"
    echo "  • RabbitMQ Management:   http://localhost:$RABBITMQ_MGMT_PORT"
    echo "  • MinIO Console:         http://localhost:$MINIO_CONSOLE_PORT"
    echo "  • Elasticsearch:         http://localhost:$ELASTICSEARCH_PORT"
    echo "  • Qdrant:                http://localhost:$QDRANT_PORT"
    
    if [ "$MONITOR_MODE" = true ]; then
        echo
        echo -e "${GREEN}Monitoring:${NC}"
        echo "  • Prometheus:            http://localhost:$PROMETHEUS_PORT"
        echo "  • Grafana:               http://localhost:$GRAFANA_PORT"
        echo "  • Jaeger:                http://localhost:$JAEGER_PORT"
    fi
    
    echo
    echo -e "${GREEN}Developer Credentials:${NC}"
    echo "  • Email:    $DEVELOPER_EMAIL"
    echo "  • Password: $DEVELOPER_PASSWORD"
}

create_log_directory() {
    print_step "Creating log directory..."
    mkdir -p "$PROJECT_ROOT/logs"
}

# Main execution
main() {
    print_header "Starting Luminar Platform"
    echo "Mode: $([ "$DEV_MODE" = true ] && echo "Development" || echo "Production")"
    echo "Monitoring: $([ "$MONITOR_MODE" = true ] && echo "Enabled" || echo "Disabled")"
    
    # Create log directory
    create_log_directory
    
    # Check prerequisites
    check_prerequisites
    
    # Build images
    build_images
    
    # Start infrastructure
    start_infrastructure
    
    # Check infrastructure health
    check_infrastructure_health
    
    # Setup databases
    setup_databases
    
    # Start backend services
    start_backend_services
    
    # Wait for backend to be ready
    sleep 10
    wait_for_service "Command Center API" "curl -s http://localhost:$COMMAND_CENTER_PORT/health"
    
    # Start frontend applications
    start_frontend_apps
    
    # Start monitoring
    start_monitoring
    
    # Final health check
    sleep 5
    if [ "$SKIP_HEALTH" = false ]; then
        "$SCRIPT_DIR/health-check.sh" --quiet || print_warning "Some services may not be fully ready yet"
    fi
    
    # Print service URLs
    print_service_urls
    
    print_header "Luminar Platform Started Successfully! 🚀"
    echo -e "${YELLOW}Tip: Use './scripts/status.sh' to check service status${NC}"
    echo -e "${YELLOW}Tip: Use './scripts/logs.sh <service>' to view logs${NC}"
    echo -e "${YELLOW}Tip: Use './scripts/stop-all.sh' to stop all services${NC}"
}

# Run main function
main