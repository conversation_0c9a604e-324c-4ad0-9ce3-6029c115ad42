#!/bin/bash

# ==============================================
# Luminar Platform - Restart Services Script
# ==============================================
# Restart specific services or service groups
# Usage: ./scripts/restart-services.sh [service|group] [options]
# Services: postgres, redis, elasticsearch, rabbitmq, minio, qdrant, 
#          command-center, doc-processor, amna, e-connect, etc.
# Groups: frontend, backend, infra, all
# Options:
#   --quick       Skip health checks
#   --force       Force restart
# ==============================================

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# Script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_ROOT="$( cd "$SCRIPT_DIR/.." && pwd )"

# Load environment
source "$PROJECT_ROOT/.env"

# Parse arguments
SERVICE_NAME=${1:-all}
QUICK_MODE=false
FORCE_MODE=false

shift || true
while [[ $# -gt 0 ]]; do
    case $1 in
        --quick)
            QUICK_MODE=true
            shift
            ;;
        --force)
            FORCE_MODE=true
            shift
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

print_header() {
    echo -e "\n${BLUE}=== $1 ===${NC}"
}

print_step() {
    echo -e "${CYAN}→ $1${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

restart_docker_service() {
    local service=$1
    print_step "Restarting $service..."
    
    if [ "$FORCE_MODE" = true ]; then
        docker-compose -f "$PROJECT_ROOT/docker-compose.yml" rm -f -s -v $service
    fi
    
    docker-compose -f "$PROJECT_ROOT/docker-compose.yml" restart $service
    
    if [ "$QUICK_MODE" = false ]; then
        sleep 5
    fi
}

restart_node_service() {
    local service=$1
    local port=$2
    local start_cmd=$3
    local app_dir=$4
    
    print_step "Restarting $service..."
    
    # Kill existing process
    if [ -f "$PROJECT_ROOT/logs/$service.pid" ]; then
        PID=$(cat "$PROJECT_ROOT/logs/$service.pid")
        if ps -p $PID > /dev/null 2>&1; then
            kill $PID 2>/dev/null || true
            sleep 2
        fi
    fi
    
    # Kill by port if still running
    PID=$(lsof -ti:$port 2>/dev/null || true)
    if [ -n "$PID" ]; then
        kill $PID 2>/dev/null || true
        sleep 2
    fi
    
    # Start service
    cd "$app_dir"
    eval "$start_cmd" > "$PROJECT_ROOT/logs/$service.log" 2>&1 &
    echo $! > "$PROJECT_ROOT/logs/$service.pid"
    cd "$PROJECT_ROOT"
}

restart_infrastructure() {
    print_header "Restarting Infrastructure Services"
    
    case $SERVICE_NAME in
        postgres|postgresql)
            restart_docker_service postgres
            ;;
        redis)
            restart_docker_service redis
            ;;
        elasticsearch|elastic)
            restart_docker_service elasticsearch
            ;;
        rabbitmq|rabbit)
            restart_docker_service rabbitmq
            ;;
        minio)
            restart_docker_service minio
            ;;
        qdrant)
            restart_docker_service qdrant
            ;;
        infra|infrastructure)
            restart_docker_service postgres
            restart_docker_service redis
            restart_docker_service elasticsearch
            restart_docker_service rabbitmq
            restart_docker_service minio
            restart_docker_service qdrant
            ;;
        *)
            return 1
            ;;
    esac
    
    print_success "Infrastructure services restarted"
    return 0
}

restart_backend() {
    print_header "Restarting Backend Services"
    
    case $SERVICE_NAME in
        command-center|api)
            restart_node_service "command-center" "$COMMAND_CENTER_PORT" \
                "nohup pnpm run start:dev" "$PROJECT_ROOT/apps/command-center"
            ;;
        doc-processor|document-processor|python)
            # Restart Python service
            print_step "Restarting Document Processor..."
            if [ -f "$PROJECT_ROOT/logs/document-processor.pid" ]; then
                PID=$(cat "$PROJECT_ROOT/logs/document-processor.pid")
                if ps -p $PID > /dev/null 2>&1; then
                    kill $PID 2>/dev/null || true
                    sleep 2
                fi
            fi
            cd "$PROJECT_ROOT/apps/python-services"
            source venv/bin/activate
            nohup python main.py > "$PROJECT_ROOT/logs/document-processor.log" 2>&1 &
            echo $! > "$PROJECT_ROOT/logs/document-processor.pid"
            cd "$PROJECT_ROOT"
            ;;
        backend)
            restart_node_service "command-center" "$COMMAND_CENTER_PORT" \
                "nohup pnpm run start:dev" "$PROJECT_ROOT/apps/command-center"
            # Restart Python service
            cd "$PROJECT_ROOT/apps/python-services"
            source venv/bin/activate
            nohup python main.py > "$PROJECT_ROOT/logs/document-processor.log" 2>&1 &
            echo $! > "$PROJECT_ROOT/logs/document-processor.pid"
            cd "$PROJECT_ROOT"
            ;;
        *)
            return 1
            ;;
    esac
    
    print_success "Backend services restarted"
    return 0
}

restart_frontend() {
    print_header "Restarting Frontend Applications"
    
    case $SERVICE_NAME in
        amna)
            restart_node_service "amna" "$AMNA_DEV_PORT" \
                "nohup pnpm run dev" "$PROJECT_ROOT/apps/amna"
            ;;
        e-connect)
            restart_node_service "e-connect" "$E_CONNECT_DEV_PORT" \
                "nohup pnpm run dev" "$PROJECT_ROOT/apps/e-connect"
            ;;
        lighthouse)
            restart_node_service "lighthouse" "$LIGHTHOUSE_DEV_PORT" \
                "nohup pnpm run dev" "$PROJECT_ROOT/apps/lighthouse"
            ;;
        training)
            restart_node_service "training-need-analysis" "$TRAINING_DEV_PORT" \
                "nohup pnpm run dev" "$PROJECT_ROOT/apps/training-need-analysis"
            ;;
        vendors)
            restart_node_service "vendors" "$VENDORS_DEV_PORT" \
                "nohup pnpm run dev" "$PROJECT_ROOT/apps/vendors"
            ;;
        wins)
            restart_node_service "wins-of-week" "$WINS_DEV_PORT" \
                "nohup pnpm run dev" "$PROJECT_ROOT/apps/wins-of-week"
            ;;
        shell)
            restart_node_service "shell" "$SHELL_DEV_PORT" \
                "nohup pnpm run dev" "$PROJECT_ROOT/apps/shell"
            ;;
        monitor)
            restart_node_service "service-monitor" "$SERVICE_MONITOR_PORT" \
                "nohup pnpm run dev" "$PROJECT_ROOT/apps/service-monitor"
            ;;
        frontend)
            # Restart all frontend apps
            for app in amna e-connect lighthouse training-need-analysis vendors wins-of-week shell service-monitor; do
                SERVICE_NAME=$app
                restart_frontend
            done
            SERVICE_NAME=frontend
            ;;
        *)
            return 1
            ;;
    esac
    
    print_success "Frontend applications restarted"
    return 0
}

restart_all() {
    print_header "Restarting All Services"
    
    # Stop all services first
    "$SCRIPT_DIR/stop-all.sh"
    
    sleep 3
    
    # Start all services
    "$SCRIPT_DIR/start-all.sh" --skip-build
}

check_service_health() {
    if [ "$QUICK_MODE" = true ]; then
        return
    fi
    
    print_header "Checking Service Health"
    
    case $SERVICE_NAME in
        postgres|postgresql)
            wait_for_service "PostgreSQL" "docker exec luminar-postgres pg_isready -U $DATABASE_USERNAME"
            ;;
        redis)
            wait_for_service "Redis" "docker exec luminar-redis redis-cli ping"
            ;;
        command-center|api)
            wait_for_service "Command Center" "curl -s http://localhost:$COMMAND_CENTER_PORT/health"
            ;;
        *)
            # Basic health check for other services
            sleep 5
            ;;
    esac
}

wait_for_service() {
    local service_name=$1
    local check_command=$2
    local max_attempts=30
    local attempt=0
    
    echo -n "  Waiting for $service_name..."
    while [ $attempt -lt $max_attempts ]; do
        if eval "$check_command" &> /dev/null; then
            echo -e " ${GREEN}ready${NC}"
            return 0
        fi
        echo -n "."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    echo -e " ${RED}timeout${NC}"
    return 1
}

# Main execution
main() {
    print_header "Restarting Service: $SERVICE_NAME"
    
    # Try to restart the service
    if restart_infrastructure; then
        check_service_health
    elif restart_backend; then
        check_service_health
    elif restart_frontend; then
        check_service_health
    elif [ "$SERVICE_NAME" = "all" ]; then
        restart_all
    else
        print_error "Unknown service or group: $SERVICE_NAME"
        echo
        echo "Available services:"
        echo "  Infrastructure: postgres, redis, elasticsearch, rabbitmq, minio, qdrant"
        echo "  Backend: command-center, doc-processor"
        echo "  Frontend: amna, e-connect, lighthouse, training, vendors, wins, shell, monitor"
        echo
        echo "Available groups:"
        echo "  infra, backend, frontend, all"
        exit 1
    fi
    
    print_success "Service restart completed"
}

# Run main function
main