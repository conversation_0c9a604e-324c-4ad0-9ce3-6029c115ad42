#!/bin/bash

# ==============================================
# Luminar Platform - Health Check Script
# ==============================================
# Performs comprehensive health checks on all services
# Usage: ./scripts/health-check.sh [options]
# Options:
#   --quiet       Minimal output (exit code only)
#   --json        Output in JSON format
#   --fix         Attempt to fix unhealthy services
# ==============================================

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# Script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_ROOT="$( cd "$SCRIPT_DIR/.." && pwd )"

# Load environment
source "$PROJECT_ROOT/.env"

# Parse arguments
QUIET_MODE=false
JSON_OUTPUT=false
FIX_MODE=false
HEALTH_ISSUES=0

while [[ $# -gt 0 ]]; do
    case $1 in
        --quiet)
            QUIET_MODE=true
            shift
            ;;
        --json)
            JSON_OUTPUT=true
            shift
            ;;
        --fix)
            FIX_MODE=true
            shift
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Health check results
declare -A HEALTH_RESULTS
declare -A HEALTH_MESSAGES

print_header() {
    if [ "$QUIET_MODE" = false ] && [ "$JSON_OUTPUT" = false ]; then
        echo -e "\n${BLUE}╔════════════════════════════════════════╗${NC}"
        echo -e "${BLUE}║     LUMINAR PLATFORM HEALTH CHECK      ║${NC}"
        echo -e "${BLUE}╚════════════════════════════════════════╝${NC}"
    fi
}

print_check() {
    if [ "$QUIET_MODE" = false ] && [ "$JSON_OUTPUT" = false ]; then
        echo -ne "${CYAN}→ Checking $1...${NC} "
    fi
}

print_result() {
    local service=$1
    local status=$2
    local message=$3
    
    HEALTH_RESULTS[$service]=$status
    HEALTH_MESSAGES[$service]=$message
    
    if [ "$status" != "healthy" ]; then
        ((HEALTH_ISSUES++))
    fi
    
    if [ "$QUIET_MODE" = false ] && [ "$JSON_OUTPUT" = false ]; then
        case $status in
            "healthy")
                echo -e "${GREEN}✓ Healthy${NC}"
                ;;
            "unhealthy")
                echo -e "${RED}✗ Unhealthy${NC}"
                echo -e "  ${RED}└─ $message${NC}"
                ;;
            "warning")
                echo -e "${YELLOW}⚠ Warning${NC}"
                echo -e "  ${YELLOW}└─ $message${NC}"
                ;;
        esac
    fi
}

check_docker_service() {
    local service=$1
    local container_name="luminar-$service"
    local health_check=$2
    
    print_check "$service"
    
    # Check if container is running
    if ! docker ps --format "{{.Names}}" | grep -q "^${container_name}$"; then
        print_result "$service" "unhealthy" "Container not running"
        
        if [ "$FIX_MODE" = true ]; then
            attempt_fix "docker-compose up -d $service" "$service"
        fi
        return
    fi
    
    # Check container health
    local health=$(docker inspect --format='{{.State.Health.Status}}' "$container_name" 2>/dev/null || echo "none")
    
    # Custom health check if provided
    if [ -n "$health_check" ]; then
        if eval "$health_check" &> /dev/null; then
            print_result "$service" "healthy" "Service is responding"
        else
            print_result "$service" "unhealthy" "Service not responding to health check"
        fi
    elif [ "$health" = "healthy" ]; then
        print_result "$service" "healthy" "Docker health check passed"
    elif [ "$health" = "unhealthy" ]; then
        print_result "$service" "unhealthy" "Docker health check failed"
    else
        print_result "$service" "warning" "No health check configured"
    fi
}

check_node_service() {
    local service=$1
    local port=$2
    local health_endpoint=${3:-"/health"}
    
    print_check "$service"
    
    # Check if process is running
    local pid_file="$PROJECT_ROOT/logs/$service.pid"
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if ! ps -p $pid > /dev/null 2>&1; then
            print_result "$service" "unhealthy" "Process not running (PID: $pid)"
            
            if [ "$FIX_MODE" = true ]; then
                case $service in
                    "command-center")
                        attempt_fix "cd $PROJECT_ROOT/apps/command-center && nohup pnpm run start:dev > $PROJECT_ROOT/logs/command-center.log 2>&1 &" "$service"
                        ;;
                    "document-processor")
                        attempt_fix "cd $PROJECT_ROOT/apps/python-services && source venv/bin/activate && nohup python main.py > $PROJECT_ROOT/logs/document-processor.log 2>&1 &" "$service"
                        ;;
                esac
            fi
            return
        fi
    fi
    
    # Check port availability
    if ! lsof -ti:$port > /dev/null 2>&1; then
        print_result "$service" "unhealthy" "Port $port not in use"
        return
    fi
    
    # Check HTTP health endpoint
    local response=$(curl -s -o /dev/null -w "%{http_code}" "http://localhost:$port$health_endpoint" 2>/dev/null || echo "000")
    
    if [ "$response" = "200" ]; then
        print_result "$service" "healthy" "Health endpoint responding"
    elif [ "$response" = "000" ]; then
        print_result "$service" "unhealthy" "Connection refused on port $port"
    else
        print_result "$service" "warning" "Health endpoint returned $response"
    fi
}

check_database_connectivity() {
    print_check "PostgreSQL connectivity"
    
    if docker exec luminar-postgres psql -U "$DATABASE_USERNAME" -d "$DATABASE_NAME" -c "SELECT 1" &> /dev/null; then
        # Check if tables exist
        local table_count=$(docker exec luminar-postgres psql -U "$DATABASE_USERNAME" -d "$DATABASE_NAME" -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public'" | tr -d ' ')
        
        if [ "$table_count" -gt 0 ]; then
            print_result "database" "healthy" "Connected and $table_count tables found"
        else
            print_result "database" "warning" "Connected but no tables found"
            
            if [ "$FIX_MODE" = true ]; then
                attempt_fix "cd $PROJECT_ROOT/apps/command-center && npx prisma migrate deploy && npx prisma db seed" "database migration"
            fi
        fi
    else
        print_result "database" "unhealthy" "Cannot connect to database"
    fi
}

check_redis_connectivity() {
    print_check "Redis connectivity"
    
    if docker exec luminar-redis redis-cli ping | grep -q "PONG"; then
        print_result "redis-connection" "healthy" "Redis responding to ping"
    else
        print_result "redis-connection" "unhealthy" "Redis not responding"
    fi
}

check_api_endpoints() {
    print_check "API endpoints"
    
    # Check main API
    local api_response=$(curl -s "http://localhost:$COMMAND_CENTER_PORT/api" 2>/dev/null || echo "")
    if [ -n "$api_response" ]; then
        print_result "api-main" "healthy" "API root accessible"
    else
        print_result "api-main" "unhealthy" "API not accessible"
    fi
    
    # Check auth endpoint
    local auth_response=$(curl -s -o /dev/null -w "%{http_code}" "http://localhost:$COMMAND_CENTER_PORT/api/auth/login" -X POST -H "Content-Type: application/json" -d '{}' 2>/dev/null || echo "000")
    if [ "$auth_response" = "400" ] || [ "$auth_response" = "401" ]; then
        print_result "api-auth" "healthy" "Auth endpoint responding"
    else
        print_result "api-auth" "warning" "Auth endpoint returned unexpected status: $auth_response"
    fi
}

check_disk_space() {
    print_check "Disk space"
    
    local usage=$(df -h . | awk 'NR==2{print $5}' | sed 's/%//')
    
    if [ "$usage" -lt 80 ]; then
        print_result "disk-space" "healthy" "$usage% used"
    elif [ "$usage" -lt 90 ]; then
        print_result "disk-space" "warning" "$usage% used - running low"
    else
        print_result "disk-space" "unhealthy" "$usage% used - critical"
    fi
}

check_memory_usage() {
    print_check "Memory usage"
    
    local mem_usage=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
    
    if [ "$mem_usage" -lt 80 ]; then
        print_result "memory" "healthy" "$mem_usage% used"
    elif [ "$mem_usage" -lt 90 ]; then
        print_result "memory" "warning" "$mem_usage% used - high usage"
    else
        print_result "memory" "unhealthy" "$mem_usage% used - critical"
    fi
}

attempt_fix() {
    local fix_command=$1
    local service_name=$2
    
    if [ "$QUIET_MODE" = false ] && [ "$JSON_OUTPUT" = false ]; then
        echo -e "${YELLOW}  → Attempting to fix $service_name...${NC}"
    fi
    
    if eval "$fix_command"; then
        sleep 5
        if [ "$QUIET_MODE" = false ] && [ "$JSON_OUTPUT" = false ]; then
            echo -e "${GREEN}  ✓ Fix applied for $service_name${NC}"
        fi
    else
        if [ "$QUIET_MODE" = false ] && [ "$JSON_OUTPUT" = false ]; then
            echo -e "${RED}  ✗ Failed to fix $service_name${NC}"
        fi
    fi
}

output_json() {
    local json='{"timestamp":"'$(date -u +"%Y-%m-%dT%H:%M:%SZ")'",'
    json+='"overall_health":"'$([ $HEALTH_ISSUES -eq 0 ] && echo "healthy" || echo "unhealthy")'",'
    json+='"issue_count":'$HEALTH_ISSUES','
    json+='"checks":{'
    
    local first=true
    for service in "${!HEALTH_RESULTS[@]}"; do
        if [ "$first" = true ]; then
            first=false
        else
            json+=","
        fi
        json+='"'$service'":{'
        json+='"status":"'${HEALTH_RESULTS[$service]}'",'
        json+='"message":"'${HEALTH_MESSAGES[$service]}'"'
        json+='}'
    done
    
    json+='}}'
    echo "$json" | jq . 2>/dev/null || echo "$json"
}

show_summary() {
    if [ "$QUIET_MODE" = false ] && [ "$JSON_OUTPUT" = false ]; then
        echo -e "\n${BLUE}═══════════════════════════════════════${NC}"
        echo -e "${BLUE}Health Check Summary${NC}"
        echo -e "${BLUE}═══════════════════════════════════════${NC}"
        
        local healthy=0
        local warnings=0
        local unhealthy=0
        
        for status in "${HEALTH_RESULTS[@]}"; do
            case $status in
                "healthy") ((healthy++)) ;;
                "warning") ((warnings++)) ;;
                "unhealthy") ((unhealthy++)) ;;
            esac
        done
        
        echo -e "${GREEN}Healthy:${NC}   $healthy checks"
        echo -e "${YELLOW}Warnings:${NC}  $warnings checks"
        echo -e "${RED}Unhealthy:${NC} $unhealthy checks"
        
        echo
        if [ $HEALTH_ISSUES -eq 0 ]; then
            echo -e "${GREEN}✓ All systems are healthy!${NC}"
        else
            echo -e "${RED}✗ Found $HEALTH_ISSUES health issues${NC}"
            
            if [ "$FIX_MODE" = false ]; then
                echo -e "${YELLOW}Tip: Run with --fix to attempt automatic fixes${NC}"
            fi
        fi
    fi
}

# Main execution
main() {
    print_header
    
    # Infrastructure checks
    if [ "$QUIET_MODE" = false ] && [ "$JSON_OUTPUT" = false ]; then
        echo -e "\n${CYAN}Infrastructure Services:${NC}"
    fi
    
    check_docker_service "postgres" "docker exec luminar-postgres pg_isready -U $DATABASE_USERNAME"
    check_docker_service "redis" "docker exec luminar-redis redis-cli ping"
    check_docker_service "elasticsearch" "curl -s http://localhost:$ELASTICSEARCH_PORT/_cluster/health"
    check_docker_service "rabbitmq" "curl -s http://localhost:$RABBITMQ_MGMT_PORT/api/health/checks/alarms"
    check_docker_service "minio" "curl -s http://localhost:$MINIO_PORT/minio/health/live"
    check_docker_service "qdrant" "curl -s http://localhost:$QDRANT_PORT/health"
    
    # Backend checks
    if [ "$QUIET_MODE" = false ] && [ "$JSON_OUTPUT" = false ]; then
        echo -e "\n${CYAN}Backend Services:${NC}"
    fi
    
    check_node_service "command-center" "$COMMAND_CENTER_PORT" "/health"
    check_node_service "document-processor" "$DOCUMENT_PROCESSOR_PORT" "/health"
    
    # Connectivity checks
    if [ "$QUIET_MODE" = false ] && [ "$JSON_OUTPUT" = false ]; then
        echo -e "\n${CYAN}Connectivity:${NC}"
    fi
    
    check_database_connectivity
    check_redis_connectivity
    check_api_endpoints
    
    # System checks
    if [ "$QUIET_MODE" = false ] && [ "$JSON_OUTPUT" = false ]; then
        echo -e "\n${CYAN}System Resources:${NC}"
    fi
    
    check_disk_space
    check_memory_usage
    
    # Output results
    if [ "$JSON_OUTPUT" = true ]; then
        output_json
    else
        show_summary
    fi
    
    # Exit with appropriate code
    exit $HEALTH_ISSUES
}

# Run main function
main