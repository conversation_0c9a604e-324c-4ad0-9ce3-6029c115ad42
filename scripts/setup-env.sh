#!/bin/bash

# ==============================================
# Luminar Platform - Environment Setup Script
# ==============================================
# Sets up the development environment with proper configuration
# Usage: ./scripts/setup-env.sh [options]
# Options:
#   --force       Overwrite existing .env file
#   --minimal     Use minimal configuration
#   --prod        Set up for production environment
# ==============================================

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
PURPLE='\033[0;35m'
NC='\033[0m'

# Script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_ROOT="$( cd "$SCRIPT_DIR/.." && pwd )"

# Parse arguments
FORCE_OVERWRITE=false
MINIMAL_CONFIG=false
PROD_CONFIG=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --force)
            FORCE_OVERWRITE=true
            shift
            ;;
        --minimal)
            MINIMAL_CONFIG=true
            shift
            ;;
        --prod)
            PROD_CONFIG=true
            shift
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

print_header() {
    echo -e "\n${BLUE}╔════════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${BLUE}║          LUMINAR PLATFORM ENVIRONMENT SETUP                    ║${NC}"
    echo -e "${BLUE}╚════════════════════════════════════════════════════════════════╝${NC}"
}

print_step() {
    echo -e "${CYAN}→ $1${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

check_prerequisites() {
    print_step "Checking prerequisites..."
    
    local all_good=true
    
    # Check Node.js
    if command -v node &> /dev/null; then
        local node_version=$(node --version)
        print_success "Node.js $node_version installed"
    else
        print_error "Node.js is not installed"
        echo "  Please install Node.js 18+ from https://nodejs.org"
        all_good=false
    fi
    
    # Check pnpm
    if command -v pnpm &> /dev/null; then
        local pnpm_version=$(pnpm --version)
        print_success "pnpm $pnpm_version installed"
    else
        print_error "pnpm is not installed"
        echo "  Install with: npm install -g pnpm"
        all_good=false
    fi
    
    # Check Docker
    if command -v docker &> /dev/null; then
        local docker_version=$(docker --version | cut -d' ' -f3 | sed 's/,//')
        print_success "Docker $docker_version installed"
    else
        print_error "Docker is not installed"
        echo "  Please install Docker from https://docker.com"
        all_good=false
    fi
    
    # Check Docker Compose
    if command -v docker-compose &> /dev/null; then
        local compose_version=$(docker-compose --version | cut -d' ' -f3 | sed 's/,//')
        print_success "Docker Compose $compose_version installed"
    else
        print_error "Docker Compose is not installed"
        all_good=false
    fi
    
    # Check Python 3
    if command -v python3 &> /dev/null; then
        local python_version=$(python3 --version | cut -d' ' -f2)
        print_success "Python $python_version installed"
    else
        print_error "Python 3 is not installed"
        echo "  Please install Python 3.8+ from https://python.org"
        all_good=false
    fi
    
    if [ "$all_good" = false ]; then
        echo
        print_error "Missing prerequisites. Please install them before continuing."
        exit 1
    fi
}

setup_environment_file() {
    print_step "Setting up .env file..."
    
    # Check if .env exists
    if [ -f "$PROJECT_ROOT/.env" ]; then
        if [ "$FORCE_OVERWRITE" = true ]; then
            print_warning "Overwriting existing .env file..."
            cp "$PROJECT_ROOT/.env" "$PROJECT_ROOT/.env.backup.$(date +%Y%m%d_%H%M%S)"
            print_success "Backup created"
        else
            print_warning ".env file already exists"
            echo -n "Do you want to overwrite it? (y/N) "
            read -r response
            if [[ ! "$response" =~ ^[Yy]$ ]]; then
                print_step "Keeping existing .env file"
                return
            fi
            cp "$PROJECT_ROOT/.env" "$PROJECT_ROOT/.env.backup.$(date +%Y%m%d_%H%M%S)"
            print_success "Backup created"
        fi
    fi
    
    # Copy appropriate template
    if [ "$PROD_CONFIG" = true ]; then
        cp "$PROJECT_ROOT/.env.production.example" "$PROJECT_ROOT/.env"
        print_success "Created .env from production template"
    elif [ "$MINIMAL_CONFIG" = true ]; then
        # Create minimal .env with just essentials
        cat > "$PROJECT_ROOT/.env" << EOF
# Minimal Luminar Development Environment
DEVELOPER_EMAIL=<EMAIL>
DEVELOPER_PASSWORD=LuminarDev2024!

# Core Services
NODE_ENV=development
COMMAND_CENTER_PORT=3000

# Database
DATABASE_USERNAME=developer
DATABASE_PASSWORD=\${DEVELOPER_PASSWORD}
DATABASE_NAME=luminar_dev
DATABASE_URL=postgresql://developer:\${DEVELOPER_PASSWORD}@localhost:5432/luminar_dev?schema=public

# Redis
REDIS_PASSWORD=\${DEVELOPER_PASSWORD}

# Authentication
JWT_SECRET=luminar-dev-jwt-secret-min-32-characters-long-development
SESSION_SECRET=luminar-dev-session-secret-min-32-characters-long-development

# Frontend
VITE_API_URL=http://localhost:3000
VITE_WS_URL=ws://localhost:3000
EOF
        print_success "Created minimal .env file"
    else
        cp "$PROJECT_ROOT/.env.full-stack.example" "$PROJECT_ROOT/.env"
        print_success "Created .env from full-stack template"
    fi
}

prompt_for_api_keys() {
    print_step "Configuring API keys (optional)..."
    
    echo
    echo "You can add API keys now or skip and add them later."
    echo "Press Enter to skip any key you don't have."
    echo
    
    # OpenAI API Key
    echo -n "OpenAI API Key (for AI features): "
    read -r openai_key
    if [ -n "$openai_key" ]; then
        sed -i.tmp "s/# OPENAI_API_KEY=.*/OPENAI_API_KEY=$openai_key/" "$PROJECT_ROOT/.env"
        print_success "OpenAI API key added"
    fi
    
    # Gemini API Key
    echo -n "Google Gemini API Key (for AI features): "
    read -r gemini_key
    if [ -n "$gemini_key" ]; then
        sed -i.tmp "s/# GEMINI_API_KEY=.*/GEMINI_API_KEY=$gemini_key/" "$PROJECT_ROOT/.env"
        print_success "Gemini API key added"
    fi
    
    # Clean up temp files
    rm -f "$PROJECT_ROOT/.env.tmp"
}

create_directories() {
    print_step "Creating necessary directories..."
    
    # Create log directory
    mkdir -p "$PROJECT_ROOT/logs"
    print_success "Created logs directory"
    
    # Create upload directories
    mkdir -p "$PROJECT_ROOT/uploads/temp"
    mkdir -p "$PROJECT_ROOT/uploads/documents"
    mkdir -p "$PROJECT_ROOT/uploads/thumbnails"
    print_success "Created upload directories"
    
    # Create data directory
    mkdir -p "$PROJECT_ROOT/data"
    print_success "Created data directory"
    
    # Create Python venv directory
    mkdir -p "$PROJECT_ROOT/apps/python-services"
    print_success "Created Python services directory"
}

setup_git_hooks() {
    print_step "Setting up Git hooks..."
    
    # Check if .git exists
    if [ ! -d "$PROJECT_ROOT/.git" ]; then
        print_warning "Not a Git repository, skipping Git hooks"
        return
    fi
    
    # Set up husky if package.json has it
    if grep -q "husky" "$PROJECT_ROOT/package.json"; then
        cd "$PROJECT_ROOT"
        pnpm run prepare || true
        cd - > /dev/null
        print_success "Git hooks configured"
    else
        print_warning "Husky not found in package.json"
    fi
}

validate_environment() {
    print_step "Validating environment configuration..."
    
    # Source the new .env file
    set -a
    source "$PROJECT_ROOT/.env"
    set +a
    
    # Check critical variables
    local missing_vars=()
    
    [ -z "$DATABASE_USERNAME" ] && missing_vars+=("DATABASE_USERNAME")
    [ -z "$DATABASE_PASSWORD" ] && missing_vars+=("DATABASE_PASSWORD")
    [ -z "$JWT_SECRET" ] && missing_vars+=("JWT_SECRET")
    [ -z "$SESSION_SECRET" ] && missing_vars+=("SESSION_SECRET")
    
    if [ ${#missing_vars[@]} -gt 0 ]; then
        print_error "Missing critical environment variables:"
        for var in "${missing_vars[@]}"; do
            echo "  - $var"
        done
        exit 1
    else
        print_success "Environment configuration is valid"
    fi
}

show_developer_info() {
    # Source environment to get credentials
    set -a
    source "$PROJECT_ROOT/.env"
    set +a
    
    echo
    echo -e "${GREEN}╔════════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${GREEN}║                  DEVELOPER CREDENTIALS                         ║${NC}"
    echo -e "${GREEN}╠════════════════════════════════════════════════════════════════╣${NC}"
    echo -e "${GREEN}║${NC} Email:    ${CYAN}$DEVELOPER_EMAIL${NC}"
    echo -e "${GREEN}║${NC} Password: ${CYAN}$DEVELOPER_PASSWORD${NC}"
    echo -e "${GREEN}╠════════════════════════════════════════════════════════════════╣${NC}"
    echo -e "${GREEN}║${NC} These credentials work for all services:                      ${GREEN}║${NC}"
    echo -e "${GREEN}║${NC} • PostgreSQL, Redis, RabbitMQ, MinIO                         ${GREEN}║${NC}"
    echo -e "${GREEN}║${NC} • Grafana, PgAdmin, Elasticsearch                            ${GREEN}║${NC}"
    echo -e "${GREEN}║${NC} • Application login                                          ${GREEN}║${NC}"
    echo -e "${GREEN}╚════════════════════════════════════════════════════════════════╝${NC}"
}

show_next_steps() {
    echo
    echo -e "${BLUE}═══════════════════════════════════════════════════════════════${NC}"
    echo -e "${BLUE}Next Steps:${NC}"
    echo -e "${BLUE}═══════════════════════════════════════════════════════════════${NC}"
    echo
    echo "1. Install dependencies:"
    echo "   ${CYAN}pnpm install${NC}"
    echo
    echo "2. Start infrastructure:"
    echo "   ${CYAN}./scripts/dev-infra.sh${NC}"
    echo
    echo "3. Start development:"
    echo "   ${CYAN}./scripts/dev-start.sh${NC}"
    echo
    echo "Or start everything at once:"
    echo "   ${CYAN}./scripts/start-all.sh --dev${NC}"
    echo
    echo -e "${YELLOW}Tip: Run './scripts/status.sh' to check service status${NC}"
}

# Main execution
main() {
    print_header
    
    # Check prerequisites
    check_prerequisites
    
    # Setup environment file
    setup_environment_file
    
    # Prompt for API keys (only in interactive mode)
    if [ "$FORCE_OVERWRITE" = false ] && [ "$MINIMAL_CONFIG" = false ] && [ "$PROD_CONFIG" = false ]; then
        prompt_for_api_keys
    fi
    
    # Create necessary directories
    create_directories
    
    # Setup git hooks
    setup_git_hooks
    
    # Validate environment
    validate_environment
    
    # Show developer info
    show_developer_info
    
    # Show next steps
    show_next_steps
    
    echo
    print_success "Environment setup completed successfully! 🚀"
}

# Run main function
main