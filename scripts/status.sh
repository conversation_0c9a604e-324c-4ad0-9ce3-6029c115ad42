#!/bin/bash

# ==============================================
# Luminar Platform - Status Check Script
# ==============================================
# Shows the status of all services and infrastructure
# Usage: ./scripts/status.sh [options]
# Options:
#   --json        Output in JSON format
#   --health      Include health check details
#   --ports       Show port assignments
# ==============================================

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
PURPLE='\033[0;35m'
NC='\033[0m'

# Icons
CHECK_MARK="✓"
CROSS_MARK="✗"
WARNING_SIGN="⚠"

# Script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_ROOT="$( cd "$SCRIPT_DIR/.." && pwd )"

# Load environment
source "$PROJECT_ROOT/.env"

# Parse arguments
JSON_OUTPUT=false
SHOW_HEALTH=false
SHOW_PORTS=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --json)
            JSON_OUTPUT=true
            shift
            ;;
        --health)
            SHOW_HEALTH=true
            shift
            ;;
        --ports)
            SHOW_PORTS=true
            shift
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Status storage
declare -A SERVICE_STATUS
declare -A SERVICE_HEALTH
declare -A SERVICE_PORTS

print_header() {
    if [ "$JSON_OUTPUT" = false ]; then
        echo -e "\n${BLUE}╔════════════════════════════════════════════════════════════════╗${NC}"
        echo -e "${BLUE}║                   LUMINAR PLATFORM STATUS                      ║${NC}"
        echo -e "${BLUE}╚════════════════════════════════════════════════════════════════╝${NC}"
    fi
}

print_section() {
    if [ "$JSON_OUTPUT" = false ]; then
        echo -e "\n${PURPLE}▶ $1${NC}"
        echo -e "${CYAN}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
    fi
}

get_status_icon() {
    local status=$1
    case $status in
        "running"|"healthy")
            echo -e "${GREEN}$CHECK_MARK${NC}"
            ;;
        "stopped"|"unhealthy")
            echo -e "${RED}$CROSS_MARK${NC}"
            ;;
        "unknown"|"starting")
            echo -e "${YELLOW}$WARNING_SIGN${NC}"
            ;;
    esac
}

check_docker_service() {
    local service=$1
    local container_name="luminar-$service"
    
    if docker ps --format "{{.Names}}" | grep -q "^${container_name}$"; then
        SERVICE_STATUS[$service]="running"
        
        # Get container health if available
        local health=$(docker inspect --format='{{.State.Health.Status}}' "$container_name" 2>/dev/null || echo "none")
        if [ "$health" = "healthy" ]; then
            SERVICE_HEALTH[$service]="healthy"
        elif [ "$health" = "unhealthy" ]; then
            SERVICE_HEALTH[$service]="unhealthy"
        else
            SERVICE_HEALTH[$service]="no health check"
        fi
        
        # Get port mapping
        local ports=$(docker port "$container_name" 2>/dev/null | head -1 | cut -d' ' -f3 | cut -d':' -f2 || echo "")
        SERVICE_PORTS[$service]=$ports
    else
        SERVICE_STATUS[$service]="stopped"
        SERVICE_HEALTH[$service]="N/A"
        SERVICE_PORTS[$service]="N/A"
    fi
}

check_node_service() {
    local service=$1
    local port=$2
    local pid_file="$PROJECT_ROOT/logs/$service.pid"
    
    # Check by PID file
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if ps -p $pid > /dev/null 2>&1; then
            SERVICE_STATUS[$service]="running"
            SERVICE_PORTS[$service]=$port
            
            # Check if service is responding
            if [ "$SHOW_HEALTH" = true ]; then
                if curl -s -o /dev/null -w "%{http_code}" "http://localhost:$port" | grep -q "200\|301\|302"; then
                    SERVICE_HEALTH[$service]="healthy"
                else
                    SERVICE_HEALTH[$service]="not responding"
                fi
            else
                SERVICE_HEALTH[$service]="running"
            fi
            return
        fi
    fi
    
    # Check by port
    if lsof -ti:$port > /dev/null 2>&1; then
        SERVICE_STATUS[$service]="running (no pid file)"
        SERVICE_PORTS[$service]=$port
        SERVICE_HEALTH[$service]="running"
    else
        SERVICE_STATUS[$service]="stopped"
        SERVICE_PORTS[$service]=$port
        SERVICE_HEALTH[$service]="N/A"
    fi
}

check_infrastructure() {
    print_section "Infrastructure Services"
    
    # Check Docker services
    for service in postgres redis elasticsearch rabbitmq minio qdrant; do
        check_docker_service $service
    done
    
    if [ "$JSON_OUTPUT" = false ]; then
        printf "%-20s %-15s %-20s %-10s\n" "Service" "Status" "Health" "Port"
        printf "%-20s %-15s %-20s %-10s\n" "-------" "------" "------" "----"
        
        for service in postgres redis elasticsearch rabbitmq minio qdrant; do
            local status_icon=$(get_status_icon "${SERVICE_STATUS[$service]}")
            local port="${SERVICE_PORTS[$service]:-N/A}"
            printf "%-20s %s %-13s %-20s %-10s\n" \
                "$service" \
                "$status_icon" \
                "${SERVICE_STATUS[$service]}" \
                "${SERVICE_HEALTH[$service]}" \
                "$port"
        done
    fi
}

check_backend() {
    print_section "Backend Services"
    
    # Check Command Center
    check_node_service "command-center" "$COMMAND_CENTER_PORT"
    
    # Check Document Processor
    check_node_service "document-processor" "$DOCUMENT_PROCESSOR_PORT"
    
    if [ "$JSON_OUTPUT" = false ]; then
        printf "%-20s %-15s %-20s %-10s\n" "Service" "Status" "Health" "Port"
        printf "%-20s %-15s %-20s %-10s\n" "-------" "------" "------" "----"
        
        for service in command-center document-processor; do
            local status_icon=$(get_status_icon "${SERVICE_STATUS[$service]}")
            local port="${SERVICE_PORTS[$service]:-N/A}"
            printf "%-20s %s %-13s %-20s %-10s\n" \
                "$service" \
                "$status_icon" \
                "${SERVICE_STATUS[$service]}" \
                "${SERVICE_HEALTH[$service]}" \
                "$port"
        done
    fi
}

check_frontend() {
    print_section "Frontend Applications"
    
    # Check frontend apps
    check_node_service "amna" "$AMNA_DEV_PORT"
    check_node_service "e-connect" "$E_CONNECT_DEV_PORT"
    check_node_service "lighthouse" "$LIGHTHOUSE_DEV_PORT"
    check_node_service "training-need-analysis" "$TRAINING_DEV_PORT"
    check_node_service "vendors" "$VENDORS_DEV_PORT"
    check_node_service "wins-of-week" "$WINS_DEV_PORT"
    check_node_service "shell" "$SHELL_DEV_PORT"
    check_node_service "service-monitor" "$SERVICE_MONITOR_PORT"
    
    if [ "$JSON_OUTPUT" = false ]; then
        printf "%-25s %-15s %-20s %-10s\n" "Application" "Status" "Health" "Port"
        printf "%-25s %-15s %-20s %-10s\n" "-----------" "------" "------" "----"
        
        for service in amna e-connect lighthouse training-need-analysis vendors wins-of-week shell service-monitor; do
            local status_icon=$(get_status_icon "${SERVICE_STATUS[$service]}")
            local port="${SERVICE_PORTS[$service]:-N/A}"
            printf "%-25s %s %-13s %-20s %-10s\n" \
                "$service" \
                "$status_icon" \
                "${SERVICE_STATUS[$service]}" \
                "${SERVICE_HEALTH[$service]}" \
                "$port"
        done
    fi
}

check_monitoring() {
    print_section "Monitoring Services"
    
    # Check monitoring services
    check_docker_service "prometheus"
    check_docker_service "grafana"
    check_docker_service "jaeger"
    
    if [ "$JSON_OUTPUT" = false ]; then
        printf "%-20s %-15s %-20s %-10s\n" "Service" "Status" "Health" "Port"
        printf "%-20s %-15s %-20s %-10s\n" "-------" "------" "------" "----"
        
        for service in prometheus grafana jaeger; do
            if [ -n "${SERVICE_STATUS[$service]}" ]; then
                local status_icon=$(get_status_icon "${SERVICE_STATUS[$service]}")
                local port="${SERVICE_PORTS[$service]:-N/A}"
                printf "%-20s %s %-13s %-20s %-10s\n" \
                    "$service" \
                    "$status_icon" \
                    "${SERVICE_STATUS[$service]}" \
                    "${SERVICE_HEALTH[$service]}" \
                    "$port"
            fi
        done
    fi
}

check_system_resources() {
    print_section "System Resources"
    
    if [ "$JSON_OUTPUT" = false ]; then
        # CPU usage
        local cpu_usage=$(top -bn1 | grep "Cpu(s)" | sed "s/.*, *\([0-9.]*\)%* id.*/\1/" | awk '{print 100 - $1}')
        echo -e "CPU Usage:          ${cpu_usage}%"
        
        # Memory usage
        local mem_info=$(free -h | awk 'NR==2{printf "%.1f/%.1f GB (%.0f%%)", $3, $2, $3*100/$2}')
        echo -e "Memory Usage:       $mem_info"
        
        # Disk usage
        local disk_usage=$(df -h . | awk 'NR==2{printf "%s/%s (%s)", $3, $2, $5}')
        echo -e "Disk Usage:         $disk_usage"
        
        # Docker stats
        local container_count=$(docker ps -q | wc -l)
        echo -e "Running Containers: $container_count"
        
        # Node processes
        local node_count=$(pgrep -f node | wc -l)
        echo -e "Node Processes:     $node_count"
    fi
}

show_summary() {
    if [ "$JSON_OUTPUT" = false ]; then
        print_section "Summary"
        
        # Count statuses
        local running=0
        local stopped=0
        local unknown=0
        
        for service in "${!SERVICE_STATUS[@]}"; do
            case "${SERVICE_STATUS[$service]}" in
                "running"*)
                    ((running++))
                    ;;
                "stopped")
                    ((stopped++))
                    ;;
                *)
                    ((unknown++))
                    ;;
            esac
        done
        
        echo -e "${GREEN}Running:${NC} $running services"
        echo -e "${RED}Stopped:${NC} $stopped services"
        if [ $unknown -gt 0 ]; then
            echo -e "${YELLOW}Unknown:${NC} $unknown services"
        fi
        
        # Overall status
        echo
        if [ $stopped -eq 0 ] && [ $unknown -eq 0 ]; then
            echo -e "${GREEN}${CHECK_MARK} All services are running!${NC}"
        elif [ $running -gt 0 ]; then
            echo -e "${YELLOW}${WARNING_SIGN} Some services are not running${NC}"
        else
            echo -e "${RED}${CROSS_MARK} No services are running${NC}"
        fi
    fi
}

output_json() {
    local json="{"
    json+='"timestamp":"'$(date -u +"%Y-%m-%dT%H:%M:%SZ")'",'
    json+='"services":{'
    
    local first=true
    for service in "${!SERVICE_STATUS[@]}"; do
        if [ "$first" = true ]; then
            first=false
        else
            json+=","
        fi
        json+='"'$service'":{'
        json+='"status":"'${SERVICE_STATUS[$service]}'",'
        json+='"health":"'${SERVICE_HEALTH[$service]}'",'
        json+='"port":"'${SERVICE_PORTS[$service]}'"'
        json+='}'
    done
    
    json+='}}'
    echo "$json" | jq . 2>/dev/null || echo "$json"
}

# Main execution
main() {
    if [ "$JSON_OUTPUT" = false ]; then
        print_header
    fi
    
    # Check all services
    check_infrastructure
    check_backend
    check_frontend
    check_monitoring
    
    if [ "$JSON_OUTPUT" = false ]; then
        check_system_resources
        show_summary
        
        # Show useful commands
        echo -e "\n${CYAN}Useful Commands:${NC}"
        echo "  • View logs:        ./scripts/logs.sh [service]"
        echo "  • Restart service:  ./scripts/restart-services.sh [service]"
        echo "  • Stop all:         ./scripts/stop-all.sh"
        echo "  • Health check:     ./scripts/health-check.sh"
    else
        output_json
    fi
}

# Run main function
main