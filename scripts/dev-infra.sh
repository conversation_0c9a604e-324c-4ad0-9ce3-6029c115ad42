#!/bin/bash

# ==============================================
# Luminar Platform - Infrastructure Development Script
# ==============================================
# Starts only infrastructure services (databases, caches, queues)
# Usage: ./scripts/dev-infra.sh [options]
# Options:
#   --minimal     Start only essential services (postgres, redis)
#   --full        Start all infrastructure services including monitoring
# ==============================================

set -e

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
RED='\033[0;31m'
NC='\033[0m'

# Script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_ROOT="$( cd "$SCRIPT_DIR/.." && pwd )"

# Load environment
source "$PROJECT_ROOT/.env"

# Parse arguments
MINIMAL=false
FULL=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --minimal)
            MINIMAL=true
            shift
            ;;
        --full)
            FULL=true
            shift
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

print_header() {
    echo -e "\n${BLUE}=== $1 ===${NC}"
}

print_step() {
    echo -e "${CYAN}→ $1${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

create_docker_network() {
    print_step "Creating Docker network..."
    docker network create luminar-network 2>/dev/null || true
}

start_essential_services() {
    print_header "Starting Essential Infrastructure"
    
    # PostgreSQL
    print_step "Starting PostgreSQL..."
    docker-compose -f "$PROJECT_ROOT/docker-compose.yml" up -d postgres
    
    # Redis
    print_step "Starting Redis..."
    docker-compose -f "$PROJECT_ROOT/docker-compose.yml" up -d redis
    
    print_success "Essential services started"
}

start_standard_services() {
    print_header "Starting Standard Infrastructure"
    
    # Elasticsearch
    print_step "Starting Elasticsearch..."
    docker-compose -f "$PROJECT_ROOT/docker-compose.yml" up -d elasticsearch
    
    # RabbitMQ
    print_step "Starting RabbitMQ..."
    docker-compose -f "$PROJECT_ROOT/docker-compose.yml" up -d rabbitmq
    
    # MinIO
    print_step "Starting MinIO..."
    docker-compose -f "$PROJECT_ROOT/docker-compose.yml" up -d minio
    
    # Qdrant
    print_step "Starting Qdrant..."
    docker-compose -f "$PROJECT_ROOT/docker-compose.yml" up -d qdrant
    
    print_success "Standard services started"
}

start_full_services() {
    print_header "Starting Full Infrastructure"
    
    # Ollama
    print_step "Starting Ollama (Local AI)..."
    docker-compose -f "$PROJECT_ROOT/docker-compose.yml" up -d ollama
    
    # Monitoring stack
    if [ -f "$PROJECT_ROOT/docker-compose.monitoring.yml" ]; then
        print_step "Starting Prometheus..."
        docker-compose -f "$PROJECT_ROOT/docker-compose.monitoring.yml" up -d prometheus
        
        print_step "Starting Grafana..."
        docker-compose -f "$PROJECT_ROOT/docker-compose.monitoring.yml" up -d grafana
        
        print_step "Starting Jaeger..."
        docker-compose -f "$PROJECT_ROOT/docker-compose.monitoring.yml" up -d jaeger
    fi
    
    print_success "Full infrastructure started"
}

wait_for_service() {
    local service_name=$1
    local check_command=$2
    local max_attempts=30
    local attempt=0
    
    echo -n "  Waiting for $service_name..."
    while [ $attempt -lt $max_attempts ]; do
        if eval "$check_command" &> /dev/null; then
            echo -e " ${GREEN}ready${NC}"
            return 0
        fi
        echo -n "."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    echo -e " ${RED}timeout${NC}"
    return 1
}

check_service_health() {
    print_header "Checking Service Health"
    
    # PostgreSQL
    wait_for_service "PostgreSQL" "docker exec luminar-postgres pg_isready -U $DATABASE_USERNAME"
    
    # Redis
    wait_for_service "Redis" "docker exec luminar-redis redis-cli ping"
    
    if [ "$MINIMAL" = false ]; then
        # Elasticsearch
        wait_for_service "Elasticsearch" "curl -s http://localhost:$ELASTICSEARCH_PORT/_cluster/health"
        
        # RabbitMQ
        wait_for_service "RabbitMQ" "curl -s -u $RABBITMQ_USERNAME:$RABBITMQ_PASSWORD http://localhost:$RABBITMQ_MGMT_PORT/api/health/checks/alarms"
        
        # MinIO
        wait_for_service "MinIO" "curl -s http://localhost:$MINIO_PORT/minio/health/live"
        
        # Qdrant
        wait_for_service "Qdrant" "curl -s http://localhost:$QDRANT_PORT/health"
    fi
    
    print_success "All services are healthy"
}

create_minio_buckets() {
    if [ "$MINIMAL" = true ]; then
        return
    fi
    
    print_header "Setting Up MinIO Buckets"
    
    # Wait a bit more for MinIO to be fully ready
    sleep 5
    
    # Configure MinIO client
    docker exec luminar-minio mc alias set local http://localhost:9000 "$MINIO_ROOT_USER" "$MINIO_ROOT_PASSWORD" || true
    
    # Create buckets
    for bucket in $(echo $MINIO_DEFAULT_BUCKETS | tr ',' ' '); do
        print_step "Creating bucket: $bucket"
        docker exec luminar-minio mc mb local/$bucket --ignore-existing || true
    done
    
    print_success "MinIO buckets created"
}

print_infrastructure_info() {
    print_header "Infrastructure Services Ready! 🚀"
    
    echo -e "\n${GREEN}Database:${NC}"
    echo "  • PostgreSQL:             localhost:$POSTGRES_PORT"
    echo "    Username:               $DATABASE_USERNAME"
    echo "    Password:               $DATABASE_PASSWORD"
    echo "    Database:               $DATABASE_NAME"
    
    echo -e "\n${GREEN}Cache:${NC}"
    echo "  • Redis:                  localhost:$REDIS_PORT"
    if [ -n "$REDIS_PASSWORD" ]; then
        echo "    Password:               $REDIS_PASSWORD"
    fi
    
    if [ "$MINIMAL" = false ]; then
        echo -e "\n${GREEN}Search & Analytics:${NC}"
        echo "  • Elasticsearch:          http://localhost:$ELASTICSEARCH_PORT"
        echo "    Username:               elastic"
        echo "    Password:               $ELASTICSEARCH_PASSWORD"
        
        echo -e "\n${GREEN}Message Queue:${NC}"
        echo "  • RabbitMQ Management:    http://localhost:$RABBITMQ_MGMT_PORT"
        echo "    Username:               $RABBITMQ_USERNAME"
        echo "    Password:               $RABBITMQ_PASSWORD"
        
        echo -e "\n${GREEN}Object Storage:${NC}"
        echo "  • MinIO Console:          http://localhost:$MINIO_CONSOLE_PORT"
        echo "    Username:               $MINIO_ROOT_USER"
        echo "    Password:               $MINIO_ROOT_PASSWORD"
        
        echo -e "\n${GREEN}Vector Database:${NC}"
        echo "  • Qdrant:                 http://localhost:$QDRANT_PORT"
        echo "    Dashboard:              http://localhost:$QDRANT_PORT/dashboard"
    fi
    
    if [ "$FULL" = true ]; then
        echo -e "\n${GREEN}Local AI:${NC}"
        echo "  • Ollama:                 http://localhost:$OLLAMA_PORT"
        
        echo -e "\n${GREEN}Monitoring:${NC}"
        echo "  • Prometheus:             http://localhost:$PROMETHEUS_PORT"
        echo "  • Grafana:                http://localhost:$GRAFANA_PORT"
        echo "    Username:               $GRAFANA_USER"
        echo "    Password:               $GRAFANA_PASSWORD"
        echo "  • Jaeger:                 http://localhost:$JAEGER_PORT"
    fi
    
    echo -e "\n${YELLOW}Tips:${NC}"
    echo "  • View logs:              docker-compose logs -f [service-name]"
    echo "  • Stop all:               docker-compose down"
    echo "  • Remove volumes:         docker-compose down -v"
    echo "  • Connect to PostgreSQL:  psql -h localhost -p $POSTGRES_PORT -U $DATABASE_USERNAME -d $DATABASE_NAME"
    echo "  • Connect to Redis:       redis-cli -p $REDIS_PORT"
}

# Main execution
main() {
    print_header "Starting Luminar Infrastructure"
    
    if [ "$MINIMAL" = true ]; then
        echo "Mode: Minimal (PostgreSQL + Redis only)"
    elif [ "$FULL" = true ]; then
        echo "Mode: Full (All infrastructure + monitoring)"
    else
        echo "Mode: Standard (All infrastructure, no monitoring)"
    fi
    
    # Create Docker network
    create_docker_network
    
    # Start services based on mode
    start_essential_services
    
    if [ "$MINIMAL" = false ]; then
        start_standard_services
        
        if [ "$FULL" = true ]; then
            start_full_services
        fi
    fi
    
    # Check service health
    check_service_health
    
    # Create MinIO buckets
    create_minio_buckets
    
    # Print infrastructure info
    print_infrastructure_info
}

# Run main function
main