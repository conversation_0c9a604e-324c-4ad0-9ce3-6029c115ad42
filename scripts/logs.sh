#!/bin/bash

# ==============================================
# Luminar Platform - Logs Viewer Script
# ==============================================
# View logs for specific services or all services
# Usage: ./scripts/logs.sh [service] [options]
# Services: postgres, redis, command-center, amna, etc.
# Options:
#   --follow, -f    Follow log output (tail -f)
#   --lines, -n     Number of lines to show (default: 100)
#   --since         Show logs since timestamp (e.g., "1h", "30m")
#   --grep          Filter logs with pattern
# ==============================================

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
PURPLE='\033[0;35m'
NC='\033[0m'

# Script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_ROOT="$( cd "$SCRIPT_DIR/.." && pwd )"

# Default values
SERVICE=${1:-all}
FOLLOW=false
LINES=100
SINCE=""
GREP_PATTERN=""

# Parse arguments
shift || true
while [[ $# -gt 0 ]]; do
    case $1 in
        -f|--follow)
            FOLLOW=true
            shift
            ;;
        -n|--lines)
            LINES="$2"
            shift 2
            ;;
        --since)
            SINCE="$2"
            shift 2
            ;;
        --grep)
            GREP_PATTERN="$2"
            shift 2
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

print_header() {
    echo -e "\n${BLUE}=== $1 ===${NC}"
}

print_separator() {
    echo -e "${CYAN}════════════════════════════════════════════════════════════════${NC}"
}

show_docker_logs() {
    local service=$1
    local container_name="luminar-$service"
    
    print_header "Docker Logs: $service"
    
    if ! docker ps --format "table {{.Names}}" | grep -q "$container_name"; then
        echo -e "${YELLOW}Container $container_name is not running${NC}"
        return
    fi
    
    local cmd="docker logs"
    
    if [ "$FOLLOW" = true ]; then
        cmd="$cmd -f"
    fi
    
    if [ -n "$LINES" ]; then
        cmd="$cmd --tail $LINES"
    fi
    
    if [ -n "$SINCE" ]; then
        cmd="$cmd --since $SINCE"
    fi
    
    if [ -n "$GREP_PATTERN" ]; then
        eval "$cmd $container_name 2>&1" | grep -E "$GREP_PATTERN" || true
    else
        eval "$cmd $container_name 2>&1"
    fi
}

show_file_logs() {
    local service=$1
    local log_file="$PROJECT_ROOT/logs/$service.log"
    
    print_header "File Logs: $service"
    
    if [ ! -f "$log_file" ]; then
        echo -e "${YELLOW}Log file not found: $log_file${NC}"
        return
    fi
    
    if [ "$FOLLOW" = true ]; then
        if [ -n "$GREP_PATTERN" ]; then
            tail -f "$log_file" | grep -E "$GREP_PATTERN"
        else
            tail -f "$log_file"
        fi
    else
        if [ -n "$GREP_PATTERN" ]; then
            tail -n "$LINES" "$log_file" | grep -E "$GREP_PATTERN" || true
        else
            tail -n "$LINES" "$log_file"
        fi
    fi
}

show_infrastructure_logs() {
    case $SERVICE in
        postgres|postgresql)
            show_docker_logs "postgres"
            ;;
        redis)
            show_docker_logs "redis"
            ;;
        elasticsearch|elastic)
            show_docker_logs "elasticsearch"
            ;;
        rabbitmq|rabbit)
            show_docker_logs "rabbitmq"
            ;;
        minio)
            show_docker_logs "minio"
            ;;
        qdrant)
            show_docker_logs "qdrant"
            ;;
        infra|infrastructure)
            for service in postgres redis elasticsearch rabbitmq minio qdrant; do
                show_docker_logs "$service"
                print_separator
            done
            ;;
        *)
            return 1
            ;;
    esac
    return 0
}

show_backend_logs() {
    case $SERVICE in
        command-center|api)
            show_file_logs "command-center"
            ;;
        doc-processor|document-processor|python)
            show_file_logs "document-processor"
            ;;
        backend)
            show_file_logs "command-center"
            print_separator
            show_file_logs "document-processor"
            ;;
        *)
            return 1
            ;;
    esac
    return 0
}

show_frontend_logs() {
    case $SERVICE in
        amna)
            show_file_logs "amna"
            ;;
        e-connect)
            show_file_logs "e-connect"
            ;;
        lighthouse)
            show_file_logs "lighthouse"
            ;;
        training)
            show_file_logs "training-need-analysis"
            ;;
        vendors)
            show_file_logs "vendors"
            ;;
        wins)
            show_file_logs "wins-of-week"
            ;;
        shell)
            show_file_logs "shell"
            ;;
        monitor)
            show_file_logs "service-monitor"
            ;;
        frontend)
            for app in amna e-connect lighthouse training-need-analysis vendors wins-of-week shell service-monitor; do
                show_file_logs "$app"
                print_separator
            done
            ;;
        *)
            return 1
            ;;
    esac
    return 0
}

show_monitoring_logs() {
    case $SERVICE in
        prometheus)
            show_docker_logs "prometheus"
            ;;
        grafana)
            show_docker_logs "grafana"
            ;;
        jaeger)
            show_docker_logs "jaeger"
            ;;
        monitoring)
            for service in prometheus grafana jaeger; do
                show_docker_logs "$service"
                print_separator
            done
            ;;
        *)
            return 1
            ;;
    esac
    return 0
}

show_all_logs() {
    print_header "All Service Logs"
    
    # Infrastructure
    echo -e "\n${GREEN}Infrastructure Services:${NC}"
    for service in postgres redis elasticsearch rabbitmq minio qdrant; do
        if docker ps --format "table {{.Names}}" | grep -q "luminar-$service"; then
            show_docker_logs "$service"
            print_separator
        fi
    done
    
    # Backend
    echo -e "\n${GREEN}Backend Services:${NC}"
    show_file_logs "command-center"
    print_separator
    show_file_logs "document-processor"
    print_separator
    
    # Frontend
    echo -e "\n${GREEN}Frontend Applications:${NC}"
    for app in amna e-connect lighthouse training-need-analysis vendors wins-of-week shell service-monitor; do
        if [ -f "$PROJECT_ROOT/logs/$app.log" ]; then
            show_file_logs "$app"
            print_separator
        fi
    done
}

show_error_logs() {
    print_header "Error Logs (Last 50 errors)"
    
    echo -e "${YELLOW}Searching for errors in all log files...${NC}\n"
    
    # Search for errors in Docker logs
    echo -e "${PURPLE}Docker Container Errors:${NC}"
    for container in $(docker ps --format "{{.Names}}" | grep "luminar-"); do
        echo -e "${CYAN}$container:${NC}"
        docker logs "$container" 2>&1 | grep -iE "(error|exception|failed|fatal)" | tail -n 10 || echo "  No errors found"
        echo
    done
    
    # Search for errors in file logs
    echo -e "${PURPLE}Application Log Errors:${NC}"
    for log_file in "$PROJECT_ROOT/logs"/*.log; do
        if [ -f "$log_file" ]; then
            local service_name=$(basename "$log_file" .log)
            echo -e "${CYAN}$service_name:${NC}"
            grep -iE "(error|exception|failed|fatal)" "$log_file" | tail -n 10 || echo "  No errors found"
            echo
        fi
    done
}

print_usage() {
    echo "Usage: $0 [service] [options]"
    echo
    echo "Services:"
    echo "  Infrastructure: postgres, redis, elasticsearch, rabbitmq, minio, qdrant"
    echo "  Backend: command-center, doc-processor"
    echo "  Frontend: amna, e-connect, lighthouse, training, vendors, wins, shell, monitor"
    echo "  Groups: infra, backend, frontend, monitoring, all, errors"
    echo
    echo "Options:"
    echo "  -f, --follow    Follow log output (tail -f)"
    echo "  -n, --lines     Number of lines to show (default: 100)"
    echo "  --since         Show logs since timestamp (e.g., '1h', '30m')"
    echo "  --grep          Filter logs with pattern"
    echo
    echo "Examples:"
    echo "  $0 command-center -f                    # Follow command center logs"
    echo "  $0 postgres -n 50                       # Show last 50 lines of postgres logs"
    echo "  $0 frontend --grep 'error'              # Show errors in frontend logs"
    echo "  $0 errors                               # Show all error logs"
}

# Main execution
main() {
    if [ "$SERVICE" = "help" ] || [ "$SERVICE" = "--help" ] || [ "$SERVICE" = "-h" ]; then
        print_usage
        exit 0
    fi
    
    if [ "$SERVICE" = "errors" ]; then
        show_error_logs
        exit 0
    fi
    
    # Try to show logs for the service
    if ! show_infrastructure_logs && \
       ! show_backend_logs && \
       ! show_frontend_logs && \
       ! show_monitoring_logs; then
        if [ "$SERVICE" = "all" ]; then
            show_all_logs
        else
            echo -e "${RED}Unknown service: $SERVICE${NC}"
            echo
            print_usage
            exit 1
        fi
    fi
}

# Run main function
main