#!/bin/bash

# ==============================================
# Luminar Platform - Backend Development Script
# ==============================================
# Starts backend services (API and Python services)
# Usage: ./scripts/dev-backend.sh [options]
# Options:
#   --skip-db     Skip database setup
#   --watch       Enable watch mode for auto-restart
# ==============================================

set -e

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
RED='\033[0;31m'
NC='\033[0m'

# Script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_ROOT="$( cd "$SCRIPT_DIR/.." && pwd )"

# Load environment
source "$PROJECT_ROOT/.env"

# Parse arguments
SKIP_DB=false
WATCH_MODE=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --skip-db)
            SKIP_DB=true
            shift
            ;;
        --watch)
            WATCH_MODE=true
            shift
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

print_header() {
    echo -e "\n${BLUE}=== $1 ===${NC}"
}

print_step() {
    echo -e "${CYAN}→ $1${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

check_infrastructure() {
    print_header "Checking Infrastructure Services"
    
    local all_good=true
    
    # Check PostgreSQL
    if docker exec luminar-postgres pg_isready -U "$DATABASE_USERNAME" &> /dev/null; then
        print_success "PostgreSQL is running"
    else
        print_error "PostgreSQL is not running"
        all_good=false
    fi
    
    # Check Redis
    if docker exec luminar-redis redis-cli ping &> /dev/null; then
        print_success "Redis is running"
    else
        print_error "Redis is not running"
        all_good=false
    fi
    
    # Check Elasticsearch
    if curl -s http://localhost:$ELASTICSEARCH_PORT/_cluster/health &> /dev/null; then
        print_success "Elasticsearch is running"
    else
        print_error "Elasticsearch is not running"
        all_good=false
    fi
    
    # Check RabbitMQ
    if curl -s http://localhost:$RABBITMQ_MGMT_PORT/api/health/checks/alarms &> /dev/null; then
        print_success "RabbitMQ is running"
    else
        print_error "RabbitMQ is not running"
        all_good=false
    fi
    
    if [ "$all_good" = false ]; then
        echo -e "\n${YELLOW}Some infrastructure services are not running.${NC}"
        echo -e "${YELLOW}Run './scripts/dev-infra.sh' to start them.${NC}"
        exit 1
    fi
}

setup_database() {
    if [ "$SKIP_DB" = true ]; then
        return
    fi
    
    print_header "Setting Up Database"
    
    cd "$PROJECT_ROOT/apps/command-center"
    
    # Generate Prisma client
    print_step "Generating Prisma client..."
    npx prisma generate
    
    # Run migrations
    print_step "Running database migrations..."
    npx prisma migrate dev --skip-seed
    
    # Seed database
    print_step "Seeding database..."
    npx prisma db seed || true
    
    cd "$PROJECT_ROOT"
    print_success "Database setup completed"
}

start_command_center() {
    print_header "Starting Command Center API"
    
    cd "$PROJECT_ROOT/apps/command-center"
    
    # Install dependencies if needed
    if [ ! -d "node_modules" ]; then
        print_step "Installing dependencies..."
        pnpm install
    fi
    
    # Build if needed
    if [ ! -d "dist" ]; then
        print_step "Building Command Center..."
        pnpm run build
    fi
    
    print_step "Starting Command Center on port $COMMAND_CENTER_PORT..."
    
    if [ "$WATCH_MODE" = true ]; then
        # Start with nodemon for auto-restart
        pnpm run start:dev
    else
        # Create a new tmux window for Command Center
        tmux new-session -d -s luminar-backend
        tmux rename-window -t luminar-backend:0 'command-center'
        tmux send-keys -t luminar-backend:0 "cd $PROJECT_ROOT/apps/command-center && pnpm run start:dev" C-m
        
        print_success "Command Center started in tmux session"
    fi
}

start_python_services() {
    print_header "Starting Python Document Processor"
    
    cd "$PROJECT_ROOT/apps/python-services"
    
    # Create virtual environment if it doesn't exist
    if [ ! -d "venv" ]; then
        print_step "Creating Python virtual environment..."
        python3 -m venv venv
    fi
    
    # Activate virtual environment
    source venv/bin/activate
    
    # Install dependencies
    if [ ! -f ".deps_installed" ]; then
        print_step "Installing Python dependencies..."
        pip install --upgrade pip
        pip install -r requirements.txt
        touch .deps_installed
    fi
    
    print_step "Starting Document Processor on port $DOCUMENT_PROCESSOR_PORT..."
    
    if [ "$WATCH_MODE" = true ]; then
        # Run directly if in watch mode
        python main.py
    else
        # Create tmux window for Python service
        tmux new-window -t luminar-backend:1 -n 'doc-processor'
        tmux send-keys -t luminar-backend:1 "cd $PROJECT_ROOT/apps/python-services && source venv/bin/activate && python main.py" C-m
        
        print_success "Document Processor started in tmux session"
    fi
}

wait_for_backend() {
    print_header "Waiting for Backend Services"
    
    local max_attempts=30
    local attempt=0
    
    echo -n "Waiting for Command Center API..."
    while [ $attempt -lt $max_attempts ]; do
        if curl -s http://localhost:$COMMAND_CENTER_PORT/health &> /dev/null; then
            echo -e " ${GREEN}ready${NC}"
            break
        fi
        echo -n "."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    if [ $attempt -eq $max_attempts ]; then
        echo -e " ${RED}timeout${NC}"
        exit 1
    fi
}

print_backend_info() {
    print_header "Backend Services Ready! 🚀"
    
    echo -e "\n${GREEN}API Endpoints:${NC}"
    echo "  • Command Center API:     http://localhost:$COMMAND_CENTER_PORT"
    echo "  • API Documentation:      http://localhost:$COMMAND_CENTER_PORT/api"
    echo "  • Health Check:           http://localhost:$COMMAND_CENTER_PORT/health"
    echo "  • Document Processor:     http://localhost:$DOCUMENT_PROCESSOR_PORT"
    
    echo -e "\n${GREEN}Database Tools:${NC}"
    echo "  • Prisma Studio:          http://localhost:$PRISMA_STUDIO_PORT"
    
    echo -e "\n${GREEN}Infrastructure:${NC}"
    echo "  • PostgreSQL:             localhost:$POSTGRES_PORT"
    echo "  • Redis:                  localhost:$REDIS_PORT"
    echo "  • RabbitMQ Management:    http://localhost:$RABBITMQ_MGMT_PORT"
    echo "  • MinIO Console:          http://localhost:$MINIO_CONSOLE_PORT"
    
    if [ "$WATCH_MODE" = false ]; then
        echo -e "\n${YELLOW}Tips:${NC}"
        echo "  • View logs:              tmux attach -t luminar-backend"
        echo "  • Switch windows:         Ctrl+B then window number"
        echo "  • Kill session:           tmux kill-session -t luminar-backend"
    fi
    
    echo -e "\n${GREEN}Credentials:${NC}"
    echo "  • Email:                  $DEVELOPER_EMAIL"
    echo "  • Password:               $DEVELOPER_PASSWORD"
}

# Main execution
main() {
    print_header "Starting Luminar Backend Services"
    
    # Check infrastructure
    check_infrastructure
    
    # Setup database
    setup_database
    
    # Start Command Center
    start_command_center
    
    # Start Python services
    if [ "$WATCH_MODE" = false ]; then
        start_python_services
    fi
    
    # Wait for backend to be ready
    wait_for_backend
    
    # Start Prisma Studio
    if [ "$WATCH_MODE" = false ]; then
        print_step "Starting Prisma Studio..."
        cd "$PROJECT_ROOT/apps/command-center"
        nohup npx prisma studio > /dev/null 2>&1 &
        cd "$PROJECT_ROOT"
    fi
    
    # Print backend info
    print_backend_info
}

# Run main function
main