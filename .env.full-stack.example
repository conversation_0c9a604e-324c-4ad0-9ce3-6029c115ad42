# ==============================================
# Luminar Full Stack - Local Development Environment
# ==============================================
# Minimal configuration for running the full stack locally.
# Copy this file to .env to get started quickly.
# For complete options, see .env.example
# ==============================================

# ==============================================
# DEVELOPER CREDENTIALS (Used across all services)
# ==============================================
# Default developer email and password for all services
DEVELOPER_EMAIL=<EMAIL>
DEVELOPER_PASSWORD=LuminarDev2024!

# ==============================================
# Application Environment
# ==============================================
NODE_ENV=development
DATA_PATH=./data

# ==============================================
# Backend Services (3000-3099)
# ==============================================
COMMAND_CENTER_PORT=3000

# ==============================================
# Frontend Development Ports (5000-5099)
# ==============================================
AMNA_DEV_PORT=5001
E_CONNECT_DEV_PORT=5002
LIGHTHOUSE_DEV_PORT=5003
TRAINING_DEV_PORT=5005
VENDORS_DEV_PORT=5006
WINS_DEV_PORT=5007
SHELL_DEV_PORT=5008

# ==============================================
# Database Configuration (Using developer credentials)
# ==============================================
DATABASE_USERNAME=developer
DATABASE_PASSWORD=${DEVELOPER_PASSWORD}
DATABASE_NAME=luminar_dev
DATABASE_PORT=5432
POSTGRES_PORT=5432

# PostgreSQL connection URL for Prisma
DATABASE_URL=postgresql://developer:${DEVELOPER_PASSWORD}@localhost:5432/luminar_dev?schema=public

# ==============================================
# Redis Configuration (Using developer password)
# ==============================================
REDIS_PASSWORD=${DEVELOPER_PASSWORD}
REDIS_PORT=6379

# ==============================================
# Authentication (Development Defaults)
# ==============================================
JWT_SECRET=luminar-dev-jwt-secret-min-32-characters-long-development
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=30d
SESSION_SECRET=luminar-dev-session-secret-min-32-characters-long-development

# Default admin user (created on first run)
ADMIN_EMAIL=${DEVELOPER_EMAIL}
ADMIN_PASSWORD=${DEVELOPER_PASSWORD}

# ==============================================
# Infrastructure Services (Using developer credentials)
# ==============================================
ELASTICSEARCH_PORT=6200
RABBITMQ_PORT=6672
RABBITMQ_MGMT_PORT=6673
QDRANT_PORT=6333
MINIO_PORT=9000
MINIO_CONSOLE_PORT=9001

# ==============================================
# Python Services
# ==============================================
DOCUMENT_PROCESSOR_PORT=8001

# ==============================================
# Message Queue (RabbitMQ - Using developer credentials)
# ==============================================
RABBITMQ_USERNAME=developer
RABBITMQ_PASSWORD=${DEVELOPER_PASSWORD}

# ==============================================
# Object Storage (MinIO - Using developer credentials)
# ==============================================
MINIO_ROOT_USER=${DEVELOPER_EMAIL}
MINIO_ROOT_PASSWORD=${DEVELOPER_PASSWORD}
MINIO_DEFAULT_BUCKETS=uploads,documents,backups,training-assets,vendor-files,wins-attachments

# ==============================================
# Frontend Configuration
# ==============================================
VITE_API_URL=http://localhost:3000
VITE_WS_URL=ws://localhost:3000
VITE_ENABLE_WEBSOCKET=true

# ==============================================
# CORS Configuration
# ==============================================
CORS_ORIGINS=http://localhost:5001,http://localhost:5002,http://localhost:5003,http://localhost:5004,http://localhost:5005,http://localhost:5006,http://localhost:5007,http://localhost:5008

# ==============================================
# Feature Flags (All enabled for development)
# ==============================================
ENABLE_MONITORING=true
ENABLE_TRACING=true
ENABLE_CACHING=true
ENABLE_ANALYTICS=true
ENABLE_NOTIFICATIONS=true

# ==============================================
# Development Tools (Using developer credentials)
# ==============================================
PGADMIN_EMAIL=${DEVELOPER_EMAIL}
PGADMIN_PASSWORD=${DEVELOPER_PASSWORD}
PRISMA_STUDIO_PORT=5555

# ==============================================
# Monitoring (Using developer credentials)
# ==============================================
PROMETHEUS_PORT=9090
GRAFANA_PORT=9100
GRAFANA_USER=developer
GRAFANA_PASSWORD=${DEVELOPER_PASSWORD}

# ==============================================
# AI Services (Add your own keys)
# ==============================================
# OpenAI - Uncomment and add your key if using AI features
# OPENAI_API_KEY=your-openai-api-key

# Google Gemini - Uncomment and add your key if using Gemini
# GEMINI_API_KEY=your-gemini-api-key