# Luminar L&D Platform - Environment Variables Documentation

## Overview

The Luminar L&D Platform uses environment variables for configuration across all services and environments. This document provides a comprehensive guide to all available environment variables, their purposes, and configuration guidelines.

## Developer Credentials - Unified Authentication

For local development, the platform uses **unified developer credentials** across all services:

- **Email**: `<EMAIL>`
- **Password**: `LuminarDev2024!`

These credentials are used for:
- PostgreSQL database
- Redis
- RabbitMQ
- MinIO object storage
- Elasticsearch
- Grafana monitoring
- PgAdmin
- Application admin user
- Email configuration (for local testing)

This approach simplifies development by requiring developers to remember only one set of credentials for all services.

## Quick Start

### Local Development
```bash
# Copy the minimal configuration for local development
cp .env.full-stack.example .env

# The default developer credentials are already configured
# Just add your API keys if needed (OpenAI, Gemini, etc.)

# Edit the .env file with your specific values
nano .env
```

### Production Deployment
```bash
# Copy the production template
cp .env.production.example .env

# Update all [REQUIRED] values with production-specific configuration
# NEVER commit production values to version control
```

## Environment Files

### `.env.example`
Complete reference file containing ALL available environment variables with detailed comments. Use this to understand all configuration options.

### `.env.full-stack.example`
Minimal configuration for local development. Contains only essential variables needed to run the full stack locally.

### `.env.production.example`
Production-ready template with security guidelines and required values clearly marked.

## Variable Categories

### Developer Credentials (Development Only)

| Variable | Description | Value | Usage |
|----------|-------------|-------|-------|
| `DEVELOPER_EMAIL` | Master developer email | `<EMAIL>` | Used across all services |
| `DEVELOPER_PASSWORD` | Master developer password | `LuminarDev2024!` | Used across all services |

**Note**: These credentials are automatically applied to all services in development. For production, replace with secure, unique credentials per service.

### 1. Application Environment

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `NODE_ENV` | Application environment | `development` | Yes |
| `DATA_PATH` | Base path for data storage | `./data` | Yes |
| `APP_VERSION` | Application version | `1.0.0` | No |
| `BUILD_TIME` | Build timestamp | Current time | No |
| `LOG_LEVEL` | Logging level | `info` | No |
| `LOG_FORMAT` | Log output format | `json` | No |

### 2. Service Ports

#### Backend Services (3000-3099)
| Variable | Description | Default |
|----------|-------------|---------|
| `PORT` | Main backend port | `3000` |
| `COMMAND_CENTER_PORT` | Command Center API | `3000` |

#### Frontend Applications (5000-5099)
| Variable | Description | Default |
|----------|-------------|---------|
| `AMNA_DEV_PORT` | AMNA AI Assistant | `5001` |
| `E_CONNECT_DEV_PORT` | E-Connect Email Assistant | `5002` |
| `LIGHTHOUSE_DEV_PORT` | Lighthouse Knowledge Base | `5003` |
| `TRAINING_DEV_PORT` | Training Need Analysis | `5005` |
| `VENDORS_DEV_PORT` | Vendor Management | `5006` |
| `WINS_DEV_PORT` | Wins of the Week | `5007` |
| `SHELL_DEV_PORT` | Shell/Launcher | `5008` |

### 3. Database Configuration

| Variable | Description | Default | Security |
|----------|-------------|---------|----------|
| `DATABASE_HOST` | PostgreSQL host | `localhost` | 🔒 |
| `DATABASE_PORT` | PostgreSQL port | `5432` | |
| `DATABASE_USERNAME` | Database username | `postgres` | 🔒 |
| `DATABASE_PASSWORD` | Database password | - | 🔒 Required |
| `DATABASE_NAME` | Database name | `command_center` | |
| `DATABASE_URL` | Full connection string | - | 🔒 |

### 4. Redis Configuration

| Variable | Description | Default | Security |
|----------|-------------|---------|----------|
| `REDIS_HOST` | Redis host | `localhost` | 🔒 |
| `REDIS_PORT` | Redis port | `6379` | |
| `REDIS_PASSWORD` | Redis password | - | 🔒 Required in production |

### 5. Authentication & Security

| Variable | Description | Default | Security |
|----------|-------------|---------|----------|
| `JWT_SECRET` | JWT signing secret (min 32 chars) | - | 🔒 Required |
| `JWT_EXPIRES_IN` | Access token expiration | `15m` | |
| `JWT_REFRESH_EXPIRES_IN` | Refresh token expiration | `30d` | |
| `SESSION_SECRET` | Session encryption key | - | 🔒 Required |
| `BCRYPT_ROUNDS` | Password hashing rounds | `10` | |

### 6. AI & Machine Learning Services

#### OpenAI
| Variable | Description | Required |
|----------|-------------|----------|
| `OPENAI_API_KEY` | OpenAI API key | If using AI features |
| `OPENAI_MODEL` | Default model | No |
| `OPENAI_EMBEDDING_MODEL` | Embedding model | No |
| `OPENAI_MAX_TOKENS` | Max tokens per request | No |

#### Google Gemini
| Variable | Description | Required |
|----------|-------------|----------|
| `GEMINI_API_KEY` | Gemini API key | If using Gemini |
| `GOOGLE_API_KEY` | Alternative key name | If using Gemini |

### 7. File Storage

| Variable | Description | Options | Default |
|----------|-------------|---------|---------|
| `FILE_STORAGE_PROVIDER` | Storage backend | `local`, `minio`, `s3` | `local` |
| `MAX_FILE_SIZE` | Max upload size (bytes) | - | `104857600` (100MB) |

#### Local Storage
| Variable | Description | Default |
|----------|-------------|---------|
| `LOCAL_UPLOAD_PATH` | Upload directory | `./uploads` |
| `LOCAL_THUMBNAIL_PATH` | Thumbnail directory | `./uploads/thumbnails` |

#### MinIO/S3
| Variable | Description | Required |
|----------|-------------|----------|
| `MINIO_ACCESS_KEY` | MinIO access key | Yes |
| `MINIO_SECRET_KEY` | MinIO secret key | Yes |
| `AWS_ACCESS_KEY_ID` | AWS access key | For S3 |
| `AWS_SECRET_ACCESS_KEY` | AWS secret key | For S3 |

### 8. Monitoring & Observability

#### Sentry Error Tracking
| Variable | Description | Required |
|----------|-------------|----------|
| `SENTRY_DSN` | Sentry project DSN | For production |
| `SENTRY_ENVIRONMENT` | Environment name | No |
| `SENTRY_TRACES_SAMPLE_RATE` | Transaction sampling | No |

#### Metrics & Tracing
| Variable | Description | Default |
|----------|-------------|---------|
| `PROMETHEUS_PORT` | Prometheus metrics | `9090` |
| `GRAFANA_PORT` | Grafana dashboard | `9100` |
| `JAEGER_PORT` | Jaeger UI | `16686` |

### 9. Security & Rate Limiting

| Variable | Description | Default |
|----------|-------------|---------|
| `RATE_LIMIT_WINDOW` | Time window (ms) | `900000` (15 min) |
| `RATE_LIMIT_MAX` | Max requests per window | `100` |
| `CORS_ORIGINS` | Allowed origins | Frontend URLs |
| `HELMET_ENABLED` | Enable Helmet.js | `true` |

### 10. Feature Flags

| Variable | Description | Default |
|----------|-------------|---------|
| `ENABLE_MONITORING` | Enable monitoring | `true` |
| `ENABLE_CACHING` | Enable Redis caching | `true` |
| `ENABLE_AI_FEATURES` | Enable AI capabilities | `true` |
| `ENABLE_FILE_UPLOADS` | Enable file uploads | `true` |
| `ENABLE_SWAGGER_UI` | Swagger documentation | `true` (dev only) |

## Environment-Specific Guidelines

### Development Environment

1. Use `.env.full-stack.example` as a starting point
2. Unified developer credentials are pre-configured:
   - Email: `<EMAIL>`
   - Password: `LuminarDev2024!`
3. All services use the same credentials for simplicity
4. All features should be enabled for testing
5. Use localhost for all service connections
6. To access services:
   - PostgreSQL: `psql -U developer -d luminar_dev`
   - Redis: `redis-cli -a LuminarDev2024!`
   - RabbitMQ Management: http://localhost:15672 (developer/LuminarDev2024!)
   - MinIO Console: http://localhost:9001 (<EMAIL>/LuminarDev2024!)
   - Grafana: http://localhost:9100 (developer/LuminarDev2024!)
   - PgAdmin: http://localhost:5050 (<EMAIL>/LuminarDev2024!)

### Staging Environment

1. Use production-like configuration
2. Enable all monitoring and logging
3. Use separate databases from production
4. Test with production-like data volumes

### Production Environment

1. **Security Requirements:**
   - Generate cryptographically secure secrets for all keys
   - Use strong passwords (min 16 characters)
   - Enable SSL/TLS for all connections
   - Restrict CORS to actual domains

2. **Performance Optimization:**
   - Increase connection pool sizes
   - Enable caching layers
   - Configure appropriate rate limits
   - Set conservative feature flags

3. **Monitoring:**
   - Configure Sentry with appropriate sampling
   - Enable all health checks
   - Set up backup schedules
   - Configure audit logging

## Security Best Practices

### 1. Secret Generation

Generate secure secrets using:
```bash
# Generate a 64-character secret
openssl rand -base64 48

# Or using Node.js
node -e "console.log(require('crypto').randomBytes(48).toString('base64'))"
```

### 2. Password Requirements

- Minimum 16 characters for production
- Mix of uppercase, lowercase, numbers, and symbols
- Unique for each service
- Rotate regularly (every 90 days)

### 3. Environment File Security

- Never commit `.env` files to version control
- Use encrypted secret management (AWS Secrets Manager, Vault)
- Restrict file permissions: `chmod 600 .env`
- Audit access to production environment files

### 4. Connection Security

- Always use SSL/TLS in production
- Verify SSL certificates
- Use VPN or private networks for database access
- Enable authentication on all services

## Docker & Kubernetes

### Docker Compose

Environment variables are automatically loaded from `.env`:
```bash
docker-compose up
```

Override with specific file:
```bash
docker-compose --env-file .env.production up
```

### Kubernetes

Use ConfigMaps for non-sensitive values:
```yaml
kubectl create configmap app-config --from-env-file=.env.example
```

Use Secrets for sensitive values:
```yaml
kubectl create secret generic app-secrets --from-env-file=.env.production
```

## Validation Scripts

### Check Required Variables
```bash
# Script to validate required environment variables
./scripts/validate-env.sh
```

### Environment Health Check
```bash
# Test all service connections
npm run env:check
```

## Common Issues

### 1. Missing Required Variables
**Error:** `Error: JWT_SECRET is required`
**Solution:** Ensure all required variables are set in your `.env` file

### 2. Connection Refused
**Error:** `ECONNREFUSED 127.0.0.1:5432`
**Solution:** Verify the service is running and ports match configuration

### 3. Invalid Secrets
**Error:** `Invalid JWT secret`
**Solution:** Ensure secrets meet minimum length requirements

### 4. CORS Errors
**Error:** `CORS policy: No 'Access-Control-Allow-Origin'`
**Solution:** Add frontend URL to `CORS_ORIGINS`

## Migration Guide

### From Individual .env Files

1. Backup existing `.env` files
2. Copy `.env.full-stack.example` to `.env`
3. Merge values from old `.env` files
4. Update application imports to use root `.env`
5. Test all services

### Updating Variables

1. Add new variable to `.env.example` with documentation
2. Add to appropriate category section
3. Update this documentation
4. Add default value in application code
5. Test with and without the variable

## Support

For environment configuration issues:
1. Check this documentation
2. Review example files
3. Run validation scripts
4. Contact DevOps team

---

Last updated: 2024-01-01
Version: 1.0.0